import React, { useState } from 'react';
import { Card as CardType, Label } from '../types';
import { formatDate } from '../utils/dateUtils';
import { 
  Calendar, 
  MessageCircle, 
  Paperclip, 
  CheckSquare, 
  User,
  Edit3
} from 'lucide-react';

interface CardProps {
  card: CardType;
  onDragStart: (e: React.DragEvent) => void;
  onDragEnd: (e: React.DragEvent) => void;
  onClick?: () => void;
}

export function Card({ card, onDragStart, onDragEnd, onClick }: CardProps) {
  const [isEditing, setIsEditing] = useState(false);
  
  const checklist = card.checklist || [];
  const completedTasks = checklist.filter(item => item && item.completed).length;
  const totalTasks = checklist.length;
  const isOverdue = card.dueDate && new Date(card.dueDate) < new Date();
  
  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  return (
    <div
      draggable
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
      onDoubleClick={handleDoubleClick}
      onClick={onClick}
      className="card-container bg-white rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:shadow-md transition-all duration-200 group overflow-hidden mb-2 w-full"
    >
      {/* Cover Image/Color */}
      {card.cover && (
        <div
          className="w-full h-20 mb-3 -mx-3 -mt-3"
          style={{
            background: card.cover.startsWith('http') || card.cover.startsWith('data:')
              ? `url(${card.cover}) center/cover`
              : card.cover
          }}
        />
      )}

      <div className="p-4">
        {/* Labels */}
        {card.labels && card.labels.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-2">
            {card.labels.map((label) => (
              <span
                key={label.id}
                className="inline-block px-2 py-1 text-xs font-medium text-white rounded"
                style={{ backgroundColor: label.color }}
              >
                {label.name}
              </span>
            ))}
          </div>
        )}

        {/* Card Title */}
        <h3 className="card-title text-sm font-medium text-gray-900 mb-3 leading-tight break-words">
          {card.title}
        </h3>

        {/* Card Description Preview */}
        {card.description && (
          <p className="card-description text-xs text-gray-600 mb-3 break-words">
            {card.description}
          </p>
        )}

        {/* Card Stats */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-3">
            {/* Due Date */}
            {card.dueDate && (
              <div className={`flex items-center space-x-1 ${isOverdue ? 'text-red-500' : 'text-gray-500'}`}>
                <Calendar className="w-3 h-3" />
                <span className={isOverdue ? 'bg-red-100 text-red-700 px-1 rounded' : ''}>
                  {formatDate(card.dueDate)}
                </span>
              </div>
            )}

            {/* Checklist Progress */}
            {totalTasks > 0 && (
              <div className="flex items-center space-x-1">
                <CheckSquare className="w-3 h-3" />
                <span className={completedTasks === totalTasks ? 'text-green-600' : ''}>
                  {completedTasks}/{totalTasks}
                </span>
              </div>
            )}

            {/* Comments */}
            {card.comments && card.comments.length > 0 && (
              <div className="flex items-center space-x-1">
                <MessageCircle className="w-3 h-3" />
                <span>{card.comments.length}</span>
              </div>
            )}

            {/* Attachments */}
            {card.attachments && card.attachments.length > 0 && (
              <div className="flex items-center space-x-1">
                <Paperclip className="w-3 h-3" />
                <span>{card.attachments.length}</span>
              </div>
            )}
          </div>

          {/* Assigned Members */}
          {card.assignedMembers && card.assignedMembers.length > 0 && (
            <div className="flex -space-x-1">
              {card.assignedMembers.slice(0, 3).map((member) => (
                <img
                  key={member?.id || Math.random()}
                  src={member?.avatar || ''}
                  alt={member?.name || 'Member'}
                  className="w-6 h-6 rounded-full border-2 border-white object-cover"
                  title={member?.name || 'Member'}
                />
              ))}
              {card.assignedMembers.length > 3 && (
                <div className="w-6 h-6 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-xs font-medium text-gray-600">
                  +{card.assignedMembers.length - 3}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Quick Edit Button */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          setIsEditing(true);
        }}
        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-100 rounded transition-opacity"
      >
        <Edit3 className="w-3 h-3 text-gray-500" />
      </button>
    </div>
  );
}