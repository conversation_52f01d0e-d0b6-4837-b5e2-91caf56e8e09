import { useAuth } from '../context/AuthContext';
import { User } from '../types';

/**
 * Hook para verificar permisos de usuario
 */
export function useUserPermissions() {
  const { user } = useAuth();

  /**
   * Verifica si el usuario actual es administrador
   */
  const isAdmin = (): boolean => {
    return user?.role === 'admin';
  };

  /**
   * Verifica si el usuario actual es miembro
   */
  const isMember = (): boolean => {
    return user?.role === 'member';
  };

  /**
   * Verifica si el usuario actual es observador
   */
  const isObserver = (): boolean => {
    return user?.role === 'observer';
  };

  /**
   * Verifica si el usuario puede crear equipos
   * Solo los administradores pueden crear equipos
   */
  const canCreateTeams = (): boolean => {
    return isAdmin();
  };

  /**
   * Verifica si el usuario puede gestionar usuarios
   */
  const canManageUsers = (): boolean => {
    return isAdmin();
  };

  /**
   * Verifica si el usuario puede acceder a funcionalidades administrativas
   */
  const canAccessAdmin = (): boolean => {
    return isAdmin();
  };

  /**
   * Verifica si el usuario puede ver todos los tableros
   */
  const canViewAllBoards = (): boolean => {
    return isAdmin();
  };

  /**
   * Verifica si el usuario puede crear tableros
   */
  const canCreateBoards = (): boolean => {
    return isAdmin() || isMember();
  };

  /**
   * Verifica si el usuario puede editar tarjetas
   */
  const canEditCards = (): boolean => {
    return isAdmin() || isMember();
  };

  /**
   * Verifica si el usuario puede eliminar tarjetas
   */
  const canDeleteCards = (): boolean => {
    return isAdmin();
  };

  /**
   * Verifica si el usuario puede invitar otros usuarios
   */
  const canInviteUsers = (): boolean => {
    return isAdmin();
  };

  /**
   * Verifica si el usuario puede ver reportes
   */
  const canViewReports = (): boolean => {
    return isAdmin() || isMember();
  };

  /**
   * Verifica si el usuario puede gestionar configuraciones
   */
  const canManageSettings = (): boolean => {
    return isAdmin();
  };

  /**
   * Verifica si el usuario puede archivar tarjetas
   */
  const canArchiveCards = (): boolean => {
    return isAdmin() || isMember();
  };

  /**
   * Obtiene el rol del usuario actual
   */
  const getUserRole = (): User['role'] | null => {
    return user?.role || null;
  };

  /**
   * Obtiene el nombre del rol en español
   */
  const getRoleName = (role?: User['role']): string => {
    const userRole = role || user?.role;
    switch (userRole) {
      case 'admin':
        return 'Administrador';
      case 'member':
        return 'Miembro';
      case 'observer':
        return 'Observador';
      default:
        return 'Sin rol';
    }
  };

  /**
   * Verifica si el usuario tiene un permiso específico
   */
  const hasPermission = (permission: string): boolean => {
    switch (permission) {
      case 'createTeams':
        return canCreateTeams();
      case 'manageUsers':
        return canManageUsers();
      case 'createBoards':
        return canCreateBoards();
      case 'editCards':
        return canEditCards();
      case 'deleteCards':
        return canDeleteCards();
      case 'inviteUsers':
        return canInviteUsers();
      case 'viewReports':
        return canViewReports();
      case 'manageSettings':
        return canManageSettings();
      case 'archiveCards':
        return canArchiveCards();
      case 'accessAdmin':
        return canAccessAdmin();
      case 'viewAllBoards':
        return canViewAllBoards();
      default:
        return false;
    }
  };

  return {
    user,
    isAdmin,
    isMember,
    isObserver,
    canCreateTeams,
    canManageUsers,
    canAccessAdmin,
    canViewAllBoards,
    canCreateBoards,
    canEditCards,
    canDeleteCards,
    canInviteUsers,
    canViewReports,
    canManageSettings,
    canArchiveCards,
    getUserRole,
    getRoleName,
    hasPermission
  };
}
