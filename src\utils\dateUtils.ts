/**
 * Utility functions for handling dates in the application
 */

/**
 * Convert a date string or Date object to a Date object
 */
export function ensureDate(date: string | Date | undefined): Date | undefined {
  if (!date) return undefined;
  if (date instanceof Date) return date;
  return new Date(date);
}

/**
 * Convert date strings in an object to Date objects recursively
 */
export function convertDatesInObject<T>(obj: any): T {
  if (!obj || typeof obj !== 'object') return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertDatesInObject(item)) as T;
  }
  
  const result = { ...obj };
  
  // List of properties that should be converted to Date objects
  const dateProperties = [
    'createdAt',
    'updatedAt', 
    'dueDate',
    'completedAt',
    'archivedAt'
  ];
  
  for (const [key, value] of Object.entries(result)) {
    if (dateProperties.includes(key) && value) {
      result[key] = ensureDate(value as string | Date);
    } else if (typeof value === 'object' && value !== null) {
      result[key] = convertDatesInObject(value);
    }
  }
  
  return result as T;
}

/**
 * Format a date for display
 */
export function formatDate(date: string | Date | undefined, options?: Intl.DateTimeFormatOptions): string {
  if (!date) return '';
  
  const dateObj = ensureDate(date);
  if (!dateObj) return '';
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  };
  
  return dateObj.toLocaleDateString('es-ES', options || defaultOptions);
}

/**
 * Format a date and time for display
 */
export function formatDateTime(date: string | Date | undefined): string {
  if (!date) return '';
  
  const dateObj = ensureDate(date);
  if (!dateObj) return '';
  
  return dateObj.toLocaleString('es-ES', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Get relative time string (e.g., "hace 2 horas", "en 3 días")
 */
export function getRelativeTime(date: string | Date | undefined): string {
  if (!date) return '';
  
  const dateObj = ensureDate(date);
  if (!dateObj) return '';
  
  const now = new Date();
  const diffMs = dateObj.getTime() - now.getTime();
  const diffMinutes = Math.round(diffMs / (1000 * 60));
  const diffHours = Math.round(diffMs / (1000 * 60 * 60));
  const diffDays = Math.round(diffMs / (1000 * 60 * 60 * 24));
  
  if (Math.abs(diffMinutes) < 60) {
    if (diffMinutes === 0) return 'ahora';
    return diffMinutes > 0 ? `en ${diffMinutes}m` : `hace ${Math.abs(diffMinutes)}m`;
  }
  
  if (Math.abs(diffHours) < 24) {
    return diffHours > 0 ? `en ${diffHours}h` : `hace ${Math.abs(diffHours)}h`;
  }
  
  if (Math.abs(diffDays) < 7) {
    return diffDays > 0 ? `en ${diffDays}d` : `hace ${Math.abs(diffDays)}d`;
  }
  
  return formatDate(dateObj);
}

/**
 * Check if a date is overdue
 */
export function isOverdue(date: string | Date | undefined): boolean {
  if (!date) return false;
  
  const dateObj = ensureDate(date);
  if (!dateObj) return false;
  
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const dueDate = new Date(dateObj.getFullYear(), dateObj.getMonth(), dateObj.getDate());
  
  return dueDate < today;
}

/**
 * Check if a date is due today
 */
export function isDueToday(date: string | Date | undefined): boolean {
  if (!date) return false;
  
  const dateObj = ensureDate(date);
  if (!dateObj) return false;
  
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const dueDate = new Date(dateObj.getFullYear(), dateObj.getMonth(), dateObj.getDate());
  
  return dueDate.getTime() === today.getTime();
}

/**
 * Check if a date is due soon (within next 3 days)
 */
export function isDueSoon(date: string | Date | undefined): boolean {
  if (!date) return false;
  
  const dateObj = ensureDate(date);
  if (!dateObj) return false;
  
  const now = new Date();
  const threeDaysFromNow = new Date(now.getTime() + (3 * 24 * 60 * 60 * 1000));
  
  return dateObj <= threeDaysFromNow && dateObj >= now;
}
