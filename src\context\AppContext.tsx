import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { Board, Card, List, User, Activity, AppNotification, Label, Team, TeamInvitation } from '../types';
import { NotificationService } from '../services/NotificationService';

interface AppState {
  currentUser: User;
  boards: Board[];
  currentBoard: Board | null;
  activities: Activity[];
  notifications: AppNotification[];
  sidebarOpen: boolean;
  searchQuery: string;
  draggedCard: Card | null;
  labels: Label[];
  users: User[];
  teams: Team[];
  currentTeam: Team | null;
  teamInvitations: TeamInvitation[];
  cardFilter: 'all' | 'assigned' | 'created' | 'due-soon';
}

type AppAction =
  | { type: 'SET_CURRENT_BOARD'; payload: Board }
  | { type: 'ADD_BOARD'; payload: Board }
  | { type: 'UPDATE_BOARD'; payload: Board }
  | { type: 'DELETE_BOARD'; payload: string }
  | { type: 'ADD_LIST'; payload: { boardId: string; list: List } }
  | { type: 'UPDATE_LIST'; payload: { boardId: string; list: List } }
  | { type: 'DELETE_LIST'; payload: { boardId: string; listId: string } }
  | { type: 'ADD_CARD'; payload: { boardId: string; listId: string; card: Card } }
  | { type: 'UPDATE_CARD'; payload: { boardId: string; listId: string; card: Card } }
  | { type: 'MOVE_CARD'; payload: { boardId: string; cardId: string; fromListId: string; toListId: string; newPosition: number } }
  | { type: 'MOVE_LIST'; payload: { boardId: string; listId: string; newPosition: number } }
  | { type: 'DELETE_CARD'; payload: { boardId: string; listId: string; cardId: string } }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_SIDEBAR_OPEN'; payload: boolean }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_DRAGGED_CARD'; payload: Card | null }
  | { type: 'ADD_ACTIVITY'; payload: Activity }
  | { type: 'ADD_NOTIFICATION'; payload: AppNotification }
  | { type: 'MARK_NOTIFICATION_READ'; payload: string }
  | { type: 'SET_CURRENT_USER'; payload: User }
  | { type: 'CLEAN_DUPLICATES' }
  | { type: 'ADD_LIST'; payload: { boardId: string; list: List } }
  | { type: 'UPDATE_LIST'; payload: { boardId: string; list: List } }
  | { type: 'DELETE_LIST'; payload: { boardId: string; listId: string } }
  | { type: 'ADD_LABEL'; payload: Label }
  | { type: 'UPDATE_LABEL'; payload: Label }
  | { type: 'DELETE_LABEL'; payload: string }
  | { type: 'SET_CURRENT_TEAM'; payload: Team | null }
  | { type: 'CREATE_TEAM'; payload: Team }
  | { type: 'UPDATE_TEAM'; payload: Team }
  | { type: 'DELETE_TEAM'; payload: string }
  | { type: 'ADD_TEAM_MEMBER'; payload: { teamId: string; member: TeamMember } }
  | { type: 'REMOVE_TEAM_MEMBER'; payload: { teamId: string; userId: string } }
  | { type: 'UPDATE_TEAM_MEMBER_ROLE'; payload: { teamId: string; userId: string; role: 'admin' | 'normal' | 'observer' } }
  | { type: 'CREATE_TEAM_INVITATION'; payload: TeamInvitation }
  | { type: 'UPDATE_TEAM_INVITATION'; payload: TeamInvitation }
  | { type: 'DELETE_TEAM_INVITATION'; payload: string }
  | { type: 'SET_CARD_FILTER'; payload: 'all' | 'assigned' | 'created' | 'due-soon' }
  | { type: 'SET_USERS'; payload: User[] }
  | { type: 'ADD_USER'; payload: User }
  | { type: 'UPDATE_USER'; payload: User }
  | { type: 'DELETE_USER'; payload: string };

const initialState: AppState = {
  currentUser: {
    id: '1',
    name: 'Juan Pérez',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
    role: 'admin'
  },
  boards: [],
  currentBoard: null,
  activities: [],
  notifications: [],
  sidebarOpen: false,
  searchQuery: '',
  draggedCard: null,
  labels: [
    { id: '1', name: 'Alta Prioridad', color: '#ef4444' },
    { id: '2', name: 'Prioridad Media', color: '#f97316' },
    { id: '3', name: 'Baja Prioridad', color: '#22c55e' },
    { id: '4', name: 'Error', color: '#dc2626' },
    { id: '5', name: 'Funcionalidad', color: '#3b82f6' },
    { id: '6', name: 'Documentación', color: '#8b5cf6' }
  ],
  users: [
    {
      id: '1',
      name: 'Juan Pérez',
      email: '<EMAIL>',
      avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
      role: 'admin'
    },
    {
      id: '2',
      name: 'María García',
      email: '<EMAIL>',
      avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
      role: 'member'
    },
    {
      id: '3',
      name: 'Carlos López',
      email: '<EMAIL>',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
      role: 'member'
    },
    {
      id: '4',
      name: 'Ana Martínez',
      email: '<EMAIL>',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
      role: 'member'
    },
    {
      id: '5',
      name: 'Luis Rodríguez',
      email: '<EMAIL>',
      avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
      role: 'member'
    }
  ],
  teams: [
    {
      id: '1',
      name: 'Equipo de Desarrollo',
      description: 'Equipo principal de desarrollo de productos',
      visibility: 'team',
      avatar: '',
      members: [
        {
          userId: '1',
          role: 'admin',
          joinedAt: new Date('2024-01-01'),
          invitedBy: '1'
        },
        {
          userId: '2',
          role: 'normal',
          joinedAt: new Date('2024-01-02'),
          invitedBy: '1'
        },
        {
          userId: '3',
          role: 'normal',
          joinedAt: new Date('2024-01-03'),
          invitedBy: '1'
        }
      ],
      boards: ['1'],
      settings: {
        allowMembersToCreateBoards: true,
        allowMembersToInvite: true,
        defaultBoardVisibility: 'team',
        requireApprovalForJoining: false
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date()
    }
  ],
  currentTeam: null,
  teamInvitations: [],
  cardFilter: 'all'
};

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_CURRENT_BOARD':
      return { ...state, currentBoard: action.payload };
    
    case 'ADD_BOARD':
      // Check if board already exists to prevent duplicates
      const existingBoard = state.boards.find(b => b.id === action.payload.id);
      if (existingBoard) {
        return state; // Don't add if already exists
      }
      return { ...state, boards: [...state.boards, action.payload] };
    
    case 'UPDATE_BOARD':
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.id ? action.payload : board
        ),
        currentBoard: state.currentBoard?.id === action.payload.id ? action.payload : state.currentBoard
      };
    
    case 'DELETE_BOARD':
      return {
        ...state,
        boards: state.boards.filter(board => board.id !== action.payload),
        currentBoard: state.currentBoard?.id === action.payload ? null : state.currentBoard
      };
    
    case 'ADD_LIST':
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.boardId
            ? { ...board, lists: [...board.lists, action.payload.list], updatedAt: new Date() }
            : board
        ),
        currentBoard: state.currentBoard?.id === action.payload.boardId
          ? { ...state.currentBoard, lists: [...state.currentBoard.lists, action.payload.list], updatedAt: new Date() }
          : state.currentBoard
      };

    case 'UPDATE_LIST':
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.boardId
            ? {
                ...board,
                lists: board.lists.map(list =>
                  list.id === action.payload.list.id ? action.payload.list : list
                ),
                updatedAt: new Date()
              }
            : board
        ),
        currentBoard: state.currentBoard?.id === action.payload.boardId
          ? {
              ...state.currentBoard,
              lists: state.currentBoard.lists.map(list =>
                list.id === action.payload.list.id ? action.payload.list : list
              ),
              updatedAt: new Date()
            }
          : state.currentBoard
      };

    case 'DELETE_LIST':
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.boardId
            ? {
                ...board,
                lists: board.lists.filter(list => list.id !== action.payload.listId),
                updatedAt: new Date()
              }
            : board
        ),
        currentBoard: state.currentBoard?.id === action.payload.boardId
          ? {
              ...state.currentBoard,
              lists: state.currentBoard.lists.filter(list => list.id !== action.payload.listId),
              updatedAt: new Date()
            }
          : state.currentBoard
      };

    case 'ADD_CARD':
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.boardId
            ? {
                ...board,
                lists: board.lists.map(list =>
                  list.id === action.payload.listId
                    ? { ...list, cards: [...list.cards, action.payload.card] }
                    : list
                ),
                updatedAt: new Date()
              }
            : board
        ),
        currentBoard: state.currentBoard?.id === action.payload.boardId
          ? {
              ...state.currentBoard,
              lists: state.currentBoard.lists.map(list =>
                list.id === action.payload.listId
                  ? { ...list, cards: [...list.cards, action.payload.card] }
                  : list
              ),
              updatedAt: new Date()
            }
          : state.currentBoard
      };

    case 'UPDATE_CARD':
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.boardId
            ? {
                ...board,
                lists: board.lists.map(list =>
                  list.id === action.payload.listId
                    ? {
                        ...list,
                        cards: list.cards.map(card =>
                          card.id === action.payload.card.id ? action.payload.card : card
                        )
                      }
                    : list
                )
              }
            : board
        ),
        currentBoard: state.currentBoard?.id === action.payload.boardId
          ? {
              ...state.currentBoard,
              lists: state.currentBoard.lists.map(list =>
                list.id === action.payload.listId
                  ? {
                      ...list,
                      cards: list.cards.map(card =>
                        card.id === action.payload.card.id ? action.payload.card : card
                      )
                    }
                  : list
              )
            }
          : state.currentBoard
      };

    case 'DELETE_CARD':
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.boardId
            ? {
                ...board,
                lists: board.lists.map(list =>
                  list.id === action.payload.listId
                    ? {
                        ...list,
                        cards: list.cards.filter(card => card.id !== action.payload.cardId)
                      }
                    : list
                )
              }
            : board
        ),
        currentBoard: state.currentBoard?.id === action.payload.boardId
          ? {
              ...state.currentBoard,
              lists: state.currentBoard.lists.map(list =>
                list.id === action.payload.listId
                  ? {
                      ...list,
                      cards: list.cards.filter(card => card.id !== action.payload.cardId)
                    }
                  : list
              )
            }
          : state.currentBoard
      };
    
    case 'MOVE_CARD':
      const { boardId, cardId, fromListId, toListId, newPosition } = action.payload;
      return {
        ...state,
        boards: state.boards.map(board => {
          if (board.id !== boardId) return board;
          
          const fromList = board.lists.find(list => list.id === fromListId);
          const toList = board.lists.find(list => list.id === toListId);
          const card = fromList?.cards.find(c => c.id === cardId);
          
          if (!fromList || !toList || !card) return board;
          
          const updatedFromList = {
            ...fromList,
            cards: fromList.cards.filter(c => c.id !== cardId)
          };
          
          const updatedCard = { ...card, position: newPosition };
          const updatedToList = {
            ...toList,
            cards: [...toList.cards, updatedCard].sort((a, b) => a.position - b.position)
          };
          
          return {
            ...board,
            lists: board.lists.map(list => {
              if (list.id === fromListId) return updatedFromList;
              if (list.id === toListId) return updatedToList;
              return list;
            })
          };
        }),
        currentBoard: state.currentBoard?.id === boardId
          ? state.boards.find(board => board.id === boardId) || state.currentBoard
          : state.currentBoard
      };

    case 'MOVE_LIST':
      const { boardId: listBoardId, listId, newPosition: listNewPosition } = action.payload;
      return {
        ...state,
        boards: state.boards.map(board => {
          if (board.id !== listBoardId) return board;

          const listToMove = board.lists.find(list => list.id === listId);
          if (!listToMove) return board;

          // Remove the list from its current position
          const otherLists = board.lists.filter(list => list.id !== listId);

          // Insert the list at the new position
          const updatedLists = [...otherLists];
          updatedLists.splice(listNewPosition, 0, { ...listToMove, position: listNewPosition });

          // Update positions for all lists
          const reorderedLists = updatedLists.map((list, index) => ({
            ...list,
            position: index
          }));

          return {
            ...board,
            lists: reorderedLists,
            updatedAt: new Date()
          };
        }),
        currentBoard: state.currentBoard?.id === listBoardId
          ? state.boards.find(board => board.id === listBoardId) || state.currentBoard
          : state.currentBoard
      };

    case 'TOGGLE_SIDEBAR':
      return { ...state, sidebarOpen: !state.sidebarOpen };

    case 'SET_SIDEBAR_OPEN':
      return { ...state, sidebarOpen: action.payload };

    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload };
    
    case 'SET_DRAGGED_CARD':
      return { ...state, draggedCard: action.payload };
    
    case 'ADD_ACTIVITY':
      return { ...state, activities: [action.payload, ...state.activities] };
    
    case 'ADD_NOTIFICATION':
      return { ...state, notifications: [action.payload, ...state.notifications] };
    
    case 'MARK_NOTIFICATION_READ':
      return {
        ...state,
        notifications: state.notifications.map(notification =>
          notification.id === action.payload ? { ...notification, read: true } : notification
        )
      };

    case 'SET_CURRENT_USER':
      return { ...state, currentUser: action.payload };

    case 'CLEAN_DUPLICATES':
      // Remove duplicate boards based on ID
      const uniqueBoards = state.boards.filter((board, index, self) =>
        index === self.findIndex(b => b.id === board.id)
      );
      return { ...state, boards: uniqueBoards };

    case 'ADD_LABEL':
      return { ...state, labels: [...state.labels, action.payload] };

    case 'UPDATE_LABEL':
      return {
        ...state,
        labels: state.labels.map(label =>
          label.id === action.payload.id ? action.payload : label
        )
      };

    case 'DELETE_LABEL':
      return {
        ...state,
        labels: state.labels.filter(label => label.id !== action.payload)
      };

    case 'SET_CURRENT_TEAM':
      return { ...state, currentTeam: action.payload };

    case 'CREATE_TEAM':
      return { ...state, teams: [...state.teams, action.payload] };

    case 'UPDATE_TEAM':
      return {
        ...state,
        teams: state.teams.map(team =>
          team.id === action.payload.id ? action.payload : team
        ),
        currentTeam: state.currentTeam?.id === action.payload.id ? action.payload : state.currentTeam
      };

    case 'DELETE_TEAM':
      return {
        ...state,
        teams: state.teams.filter(team => team.id !== action.payload),
        currentTeam: state.currentTeam?.id === action.payload ? null : state.currentTeam,
        boards: state.boards.filter(board => board.teamId !== action.payload)
      };

    case 'ADD_TEAM_MEMBER':
      return {
        ...state,
        teams: state.teams.map(team =>
          team.id === action.payload.teamId
            ? { ...team, members: [...team.members, action.payload.member] }
            : team
        )
      };

    case 'REMOVE_TEAM_MEMBER':
      return {
        ...state,
        teams: state.teams.map(team =>
          team.id === action.payload.teamId
            ? { ...team, members: team.members.filter(member => member.userId !== action.payload.userId) }
            : team
        )
      };

    case 'UPDATE_TEAM_MEMBER_ROLE':
      return {
        ...state,
        teams: state.teams.map(team =>
          team.id === action.payload.teamId
            ? {
                ...team,
                members: team.members.map(member =>
                  member.userId === action.payload.userId
                    ? { ...member, role: action.payload.role }
                    : member
                )
              }
            : team
        )
      };

    case 'CREATE_TEAM_INVITATION':
      return { ...state, teamInvitations: [...state.teamInvitations, action.payload] };

    case 'UPDATE_TEAM_INVITATION':
      return {
        ...state,
        teamInvitations: state.teamInvitations.map(invitation =>
          invitation.id === action.payload.id ? action.payload : invitation
        )
      };

    case 'DELETE_TEAM_INVITATION':
      return {
        ...state,
        teamInvitations: state.teamInvitations.filter(invitation => invitation.id !== action.payload)
      };

    case 'SET_CARD_FILTER':
      return { ...state, cardFilter: action.payload };

    case 'SET_USERS':
      return { ...state, users: action.payload };

    case 'ADD_USER':
      return { ...state, users: [...state.users, action.payload] };

    case 'UPDATE_USER':
      return {
        ...state,
        users: state.users.map(user =>
          user.id === action.payload.id ? action.payload : user
        )
      };

    case 'DELETE_USER':
      return {
        ...state,
        users: state.users.filter(user => user.id !== action.payload)
      };

    default:
      return state;
  }
}

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

interface AppProviderProps {
  children: ReactNode;
  currentUser?: User;
}

export function AppProvider({ children, currentUser }: AppProviderProps) {
  const [state, dispatch] = useReducer(appReducer, {
    ...initialState,
    currentUser: currentUser || initialState.currentUser
  });
  // const persistence = usePersistence(); // DEPRECATED: No longer using localStorage
  const notificationService = NotificationService.getInstance();

  // Update current user when it changes
  useEffect(() => {
    if (currentUser) {
      dispatch({ type: 'SET_CURRENT_USER', payload: currentUser });
    }
  }, [currentUser]);

  // DEPRECATED: No longer loading from localStorage - data is loaded via DatabaseAppContext
  // useEffect(() => {
    // Prevent multiple executions
    if (state.boards.length > 0) {
      return;
    }

    // Perform localStorage maintenance before loading data
    try {
      const { isCriticallyFull, progressiveCleanup } = require('../utils/storageUtils');

      // Check if localStorage is critically full
      if (isCriticallyFull()) {
        console.warn('localStorage critically full, performing progressive cleanup');
        const success = progressiveCleanup();
        if (success) {
          console.log('Progressive cleanup completed successfully');
          // Don't reload, just continue with cleaned data
        }
      }

      // Regular maintenance
      const { StorageService } = require('../hooks/useLocalStorage');
      StorageService.performMaintenance();
    } catch (error) {
      console.warn('Failed to perform localStorage maintenance:', error);
      // Don't clear everything automatically, just log the error
      // The user can manually clean if needed through the StorageManager
    }

    // const savedData = persistence.loadAppData(); // DEPRECATED: No longer using localStorage

    // DEPRECATED: No longer loading from localStorage
    /*
    if (savedData.boards.length > 0 && state.boards.length === 0) {
      savedData.boards.forEach(board => {
        dispatch({ type: 'ADD_BOARD', payload: board });
      });

      // Set current board if exists
      if (savedData.currentBoardId) {
        const currentBoard = savedData.boards.find(b => b.id === savedData.currentBoardId);
        if (currentBoard) {
          dispatch({ type: 'SET_CURRENT_BOARD', payload: currentBoard });
        }
      }
    } else if (savedData.boards.length === 0 && state.boards.length === 0) {
    */

    // Create a default board if no boards exist (for demo purposes)
    if (state.boards.length === 0) {
      // Create a default board if no boards exist
      const defaultBoard: Board = {
        id: 'default-board',
        title: 'Mi Primer Tablero',
        description: 'Tablero de ejemplo para comenzar',
        background: 'bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500',
        lists: [
          {
            id: 'list-1',
            title: 'Por Hacer',
            cards: [
              {
                id: 'card-1',
                title: '¡Bienvenido a SapiaFlow!',
                description: 'Esta es tu primera tarjeta. Haz clic para editarla.',
                labels: [{ id: '5', name: 'Funcionalidad', color: '#3b82f6' }],
                dueDate: undefined,
                assignedMembers: currentUser ? [currentUser] : [],
                checklist: [
                  { id: 'check-1', text: 'Explorar la interfaz', completed: false },
                  { id: 'check-2', text: 'Crear una nueva tarjeta', completed: false }
                ],
                comments: [],
                attachments: [],
                position: 0,
                archived: false,
                createdAt: new Date(),
                updatedAt: new Date()
              }
            ],
            position: 0
          },
          {
            id: 'list-2',
            title: 'En Progreso',
            cards: [],
            position: 1
          },
          {
            id: 'list-3',
            title: 'Completado',
            cards: [],
            position: 2
          }
        ],
        members: currentUser ? [
          {
            ...currentUser,
            role: 'admin' as const,
            joinedAt: new Date()
          },
          {
            id: 'demo-user-1',
            name: 'Ana García',
            email: '<EMAIL>',
            avatar: 'https://ui-avatars.com/api/?name=Ana+Garcia&background=3b82f6&color=fff&size=128',
            role: 'member' as const,
            joinedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
          },
          {
            id: 'demo-user-2',
            name: 'Carlos López',
            email: '<EMAIL>',
            avatar: 'https://ui-avatars.com/api/?name=Carlos+Lopez&background=10b981&color=fff&size=128',
            role: 'observer' as const,
            joinedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) // 3 days ago
          }
        ] : [],
        teamId: '1', // Belongs to the development team
        labels: state.labels,
        isPublic: false,
        isFavorite: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      dispatch({ type: 'ADD_BOARD', payload: defaultBoard });
      dispatch({ type: 'SET_CURRENT_BOARD', payload: defaultBoard });
    }

    // DEPRECATED: No longer loading from localStorage
    /*
    // Load activities
    if (savedData.activities.length > 0) {
      savedData.activities.forEach(activity => {
        dispatch({ type: 'ADD_ACTIVITY', payload: activity });
      });
    }

    // Load notifications
    if (savedData.notifications.length > 0) {
      savedData.notifications.forEach(notification => {
        dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
      });
    }

    // Load user preferences
    dispatch({ type: 'SET_SIDEBAR_OPEN', payload: savedData.userPreferences.sidebarOpen });
    */

    // DEPRECATED: No longer loading users from UserService
    /*
    const userService = UserService.getInstance();
    const allUsers = userService.getAllUsers();
    dispatch({ type: 'SET_USERS', payload: allUsers });
    */

    // Clean any duplicates that might have been created
    dispatch({ type: 'CLEAN_DUPLICATES' });
  // }, []); // Empty dependency array to run only once

  // DEPRECATED: No longer auto-saving to localStorage - data is persisted via API
  /*
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (state.boards.length > 0) {
        persistence.saveBoards(state.boards);
      }
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [state.boards, persistence]);

  useEffect(() => {
    persistence.saveCurrentBoardId(state.currentBoard?.id || null);
  }, [state.currentBoard, persistence]);

  useEffect(() => {
    persistence.saveActivities(state.activities);
  }, [state.activities, persistence]);

  useEffect(() => {
    persistence.saveNotifications(state.notifications);
  }, [state.notifications, persistence]);
  */

  // DEPRECATED: No longer saving preferences to localStorage
  /*
  useEffect(() => {
    persistence.saveUserPreferences({
      sidebarOpen: state.sidebarOpen,
      theme: 'light',
      language: 'es'
    });
  }, [state.sidebarOpen, persistence]);
  */

  // Check for due date notifications every hour
  useEffect(() => {
    const checkDueDates = () => {
      if (state.currentBoard) {
        const allCards = state.currentBoard.lists.flatMap(list => list.cards);
        notificationService.checkDueDateNotifications(allCards, state.currentBoard.id);
      }
    };

    // Check immediately
    checkDueDates();

    // Then check every hour
    const interval = setInterval(checkDueDates, 60 * 60 * 1000);

    return () => clearInterval(interval);
  }, [state.currentBoard, notificationService]);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    // Instead of throwing an error, try to use the compatibility hook
    try {
      const { useAppCompat } = require('../hooks/useAppCompat');
      return useAppCompat();
    } catch (error) {
      throw new Error('useApp debe ser usado dentro de un AppProvider o DatabaseAppProvider');
    }
  }
  return context;
}