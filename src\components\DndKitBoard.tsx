import React, { useState } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  TouchSensor,
  useSensor,
  useSensors,
  closestCorners,
  pointerWithin,
  rectIntersection,
  UniqueIdentifier,
  CollisionDetection,
} from '@dnd-kit/core';
import {
  SortableContext,
  arrayMove,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { useUserPermissions } from '../hooks/useUserPermissions';
import { useResponsive } from '../hooks/useResponsive';
import { useAuth } from '../context/AuthContext';
import ApiService from '../services/ApiService';
import { DndKitList } from './DndKitList';
import { DndKitCard } from './DndKitCard';
import { AddListForm } from './AddListForm';
import { CardModal } from './CardModal';
// import { SearchAndFilter } from './SearchAndFilter'; // Temporarily disabled
import { SearchResults } from './SearchResults';
import { BoardManager } from './BoardManager';
import { DragDropHelp } from './DragDropHelp';
import { useSearch, SearchFilters } from '../hooks/useSearch';
import { Board as BoardType, List as ListType, Card as CardType } from '../types';
import { Plus } from 'lucide-react';

export function DndKitBoard() {
  // ALL HOOKS MUST BE AT THE TOP - NO CONDITIONAL EXECUTION
  const context = useDatabaseApp();
  const { isAdmin } = useUserPermissions();
  const { isMobile, isTablet } = useResponsive();
  const { user: currentUser } = useAuth();

  // State hooks
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  const [selectedCard, setSelectedCard] = useState<CardType | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [showBoardManager, setShowBoardManager] = useState(false);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    query: '',
    labels: [],
    members: [],
    dueDate: '',
    hasChecklist: null,
    hasAttachments: null
  });

  // Sensor hooks - Balanced for both click and drag functionality
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: isMobile ? 8 : 5, // Reduced distance for easier dragging
        delay: isMobile ? 100 : 50, // Shorter delay for better drag responsiveness
        tolerance: isMobile ? 8 : 5,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 100, // Shorter delay for touch
        tolerance: 8,
      },
    })
  );

  // Get context values (safe to call after hooks)
  const { state, getCurrentBoard, updateBoard, addCard, updateCard, updateList, deleteList } = context || {};
  const currentBoard = getCurrentBoard ? getCurrentBoard() : null;
  const searchQuery = state?.searchQuery || '';
  const cardFilter = state?.cardFilter || 'all';

  // Search functionality - MUST be before any conditional returns
  const { filteredCards, highlightText } = useSearch(currentBoard, {
    ...searchFilters,
    query: searchQuery
  });

  // Early validation AFTER all hooks
  if (!context) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-600 mb-2">
            Cargando aplicación...
          </h2>
          <p className="text-gray-500">
            Inicializando contexto de la aplicación.
          </p>
        </div>
      </div>
    );
  }

  // Update search filters when global search query changes - MUST be before any conditional returns
  React.useEffect(() => {
    const query = searchQuery || '';
    setSearchFilters(prev => ({ ...prev, query }));
    setShowSearchResults(query.length > 0 ||
      searchFilters.labels.length > 0 ||
      searchFilters.members.length > 0 ||
      searchFilters.dueDate !== '' ||
      searchFilters.hasChecklist !== null ||
      searchFilters.hasAttachments !== null
    );
  }, [searchQuery, searchFilters.labels, searchFilters.members, searchFilters.dueDate, searchFilters.hasChecklist, searchFilters.hasAttachments]);

  // Filter cards based on current filter - MUST be before any conditional returns
  const filterCards = React.useCallback((cards: CardType[], currentUserId: string | undefined): CardType[] => {
    if (!currentUserId) return cards;

    switch (cardFilter) {
      case 'assigned':
        return cards.filter(card =>
          card.assignedMembers.some(member => member.id === currentUserId)
        );
      case 'created':
        return cards.filter(card => {
          if (!card.createdAt) return false;
          const cardDate = new Date(card.createdAt);
          const today = new Date();
          return cardDate.toDateString() === today.toDateString();
        });
      case 'due-soon':
        return cards.filter(card => {
          if (!card.dueDate) return false;
          const dueDate = new Date(card.dueDate);
          const today = new Date();
          const diffTime = dueDate.getTime() - today.getTime();
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return diffDays <= 7 && diffDays >= 0;
        });
      case 'overdue':
        return cards.filter(card => {
          if (!card.dueDate) return false;
          const dueDate = new Date(card.dueDate);
          const today = new Date();
          return dueDate < today;
        });
      default:
        return cards;
    }
  }, [cardFilter]);

  // Early return if no board is selected
  if (!currentBoard) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-600 mb-2">
            No hay tablero seleccionado
          </h2>
          <p className="text-gray-500">
            Selecciona un tablero desde el menú lateral para comenzar.
          </p>
        </div>
      </div>
    );
  }

  // Custom collision detection
  const collisionDetectionStrategy: CollisionDetection = (args) => {
    const { active, droppableContainers } = args;

    if (!active.data.current) return [];

    // For lists, use closestCorners to allow reordering
    if (active.data.current.type === 'list') {
      return closestCorners(args);
    }

    // For cards, use pointerWithin first, then rectIntersection as fallback
    const pointerCollisions = pointerWithin(args);
    if (pointerCollisions.length > 0) {
      return pointerCollisions;
    }

    return rectIntersection(args);
  };



  // Apply filters to board lists
  const getFilteredBoard = () => {
    if (!currentBoard) return null;

    // If no filter is applied, return original board
    if (cardFilter === 'all') return currentBoard;

    // Apply card filter to each list
    const filteredLists = currentBoard.lists.map(list => ({
      ...list,
      cards: filterCards(list.cards, currentUser?.id)
    }));

    return {
      ...currentBoard,
      lists: filteredLists
    };
  };

  const filteredBoard = getFilteredBoard();

  const handleAddCard = async (listId: string, title: string) => {
    if (!currentBoard || !addCard) return;

    try {
      await addCard(currentBoard.id, listId, {
        title,
        description: '',
        labels: [],
        assignedMembers: [],
        checklist: [],
        comments: [],
        attachments: [],
        position: currentBoard.lists.find(l => l.id === listId)?.cards.length || 0,
        archived: false
      });
    } catch (error) {
      console.error('Error adding card:', error);
    }
  };

  const handleCardClick = (card: CardType) => {
    setSelectedCard(card);
    setShowModal(true);
  };

  const handleCardUpdate = async (updatedCard: CardType) => {
    if (!currentBoard || !updateCard) return;

    try {
      await updateCard(currentBoard.id, updatedCard.id, updatedCard);
    } catch (error) {
      console.error('Error updating card:', error);
    }
  };

  const handleListUpdate = async (updatedList: ListType) => {
    if (!currentBoard || !updateList) return;

    try {
      await updateList(currentBoard.id, updatedList);
    } catch (error) {
      console.error('Error updating list:', error);
    }
  };

  const handleListDelete = async (listId: string) => {
    if (!currentBoard || !deleteList) return;

    try {
      await deleteList(currentBoard.id, listId);
    } catch (error) {
      console.error('Error deleting list:', error);
    }
  };

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id);
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    if (!over || !currentBoard) return;

    const activeId = active.id;
    const overId = over.id;
    const activeType = active.data.current?.type;
    const overType = over.data.current?.type;

    // Only handle card movements here, not list movements
    if (activeType !== 'card') return;

    // Find the containers
    const activeContainer = findContainer(activeId);
    const overContainer = findContainer(overId) || findListById(overId);

    if (!activeContainer || !overContainer || activeContainer === overContainer) {
      return;
    }

    // Moving a card between lists
    const activeIndex = activeContainer.cards.findIndex(card => card.id === activeId);
    const activeCard = activeContainer.cards[activeIndex];
    if (!activeCard) return;

    // Remove card from active container
    const newActiveCards = activeContainer.cards.filter(card => card.id !== activeId);

    // Add card to over container
    const newOverCards = [...overContainer.cards];

    // Determine insert position
    let insertIndex = newOverCards.length;
    if (overType === 'card') {
      const overIndex = newOverCards.findIndex(card => card.id === overId);
      insertIndex = overIndex >= 0 ? overIndex : newOverCards.length;
    }

    newOverCards.splice(insertIndex, 0, { ...activeCard, position: insertIndex });

    // Update positions
    newActiveCards.forEach((card, index) => {
      card.position = index;
    });
    newOverCards.forEach((card, index) => {
      card.position = index;
    });

    // Update the board
    const updatedLists = currentBoard.lists.map(list => {
      if (list.id === activeContainer.id) {
        return { ...list, cards: newActiveCards };
      }
      if (list.id === overContainer.id) {
        return { ...list, cards: newOverCards };
      }
      return list;
    });

    if (updateBoard) {
      updateBoard(currentBoard.id, { lists: updatedLists }).catch(error => {
        console.error('Error updating board:', error);
      });
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over || !currentBoard) return;

    const activeId = active.id;
    const overId = over.id;
    const activeType = active.data.current?.type;

    // Handle list reordering
    if (activeType === 'list') {
      const oldIndex = currentBoard.lists.findIndex(list => list.id === activeId);
      const newIndex = currentBoard.lists.findIndex(list => list.id === overId);

      if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {
        const newLists = arrayMove(currentBoard.lists, oldIndex, newIndex);
        // Update positions
        newLists.forEach((list, index) => {
          list.position = index;
        });

        if (updateBoard) {
          updateBoard(currentBoard.id, { lists: newLists }).catch(error => {
            console.error('Error updating board:', error);
          });
        }
      }
      return;
    }

    // Handle card movements
    if (activeType === 'card') {
      const activeContainer = findContainer(activeId);
      const overContainer = findContainer(overId) || findListById(overId);

      if (!activeContainer) return;

      // If moving between different lists
      if (overContainer && activeContainer.id !== overContainer.id) {
        try {
          const newPosition = overContainer.cards.length;
          await ApiService.moveCard(
            currentBoard.id,
            activeId.toString(),
            activeContainer.id,
            overContainer.id,
            newPosition
          );

          // Reload board data to reflect changes
          if (context?.loadBoards) {
            await context.loadBoards();
          }
        } catch (error) {
          console.error('Error moving card between lists:', error);
        }
      }
      // If reordering within the same list
      else if (activeContainer && activeId !== overId) {
        const oldIndex = activeContainer.cards.findIndex(card => card.id === activeId);
        const newIndex = activeContainer.cards.findIndex(card => card.id === overId);

        if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {
          const newCards = arrayMove(activeContainer.cards, oldIndex, newIndex);
          // Update positions
          newCards.forEach((card, index) => {
            card.position = index;
          });

          const updatedLists = currentBoard.lists.map(list =>
            list.id === activeContainer.id ? { ...list, cards: newCards } : list
          );

          if (updateBoard) {
            updateBoard(currentBoard.id, { lists: updatedLists }).catch(error => {
              console.error('Error updating board:', error);
            });
          }
        }
      }
    }
  };

  const findContainer = (id: UniqueIdentifier): ListType | undefined => {
    if (!currentBoard) return undefined;

    // Check if id is a card and return its parent list
    return currentBoard.lists.find(list =>
      list.cards.some(card => card.id === id)
    );
  };

  const findListById = (id: UniqueIdentifier): ListType | undefined => {
    if (!currentBoard) return undefined;
    return currentBoard.lists.find(list => list.id === id);
  };

  const getActiveItem = () => {
    if (!activeId || !currentBoard) return null;

    // Check if it's a list
    const list = currentBoard.lists.find(list => list.id === activeId);
    if (list) return list;

    // Check if it's a card
    for (const list of currentBoard.lists) {
      const card = list.cards.find(card => card.id === activeId);
      if (card) return card;
    }

    return null;
  };

  if (!currentBoard) {
    return (
      <>
        <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
          <div className="text-center">
            <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Plus className="w-12 h-12 text-blue-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Bienvenido a SapiaFlow</h2>
            <p className="text-gray-600 mb-6">
              {(state?.boards?.length || 0) === 0
                ? 'Crea tu primer tablero para comenzar a organizar tus tareas'
                : 'Selecciona un tablero de la barra lateral para comenzar'
              }
            </p>
            <div className="space-y-3">
              {isAdmin() ? (
                <button
                  onClick={() => setShowBoardManager(true)}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 mx-auto"
                >
                  <Plus className="w-5 h-5" />
                  <span>Crear Tu Primer Tablero</span>
                </button>
              ) : (
                <div className="text-center p-4 bg-orange-50 rounded-lg border border-orange-200">
                  <p className="text-orange-700 font-medium">Solo los administradores pueden crear tableros</p>
                  <p className="text-orange-600 text-sm mt-1">Contacta a un administrador para solicitar acceso a tableros</p>
                </div>
              )}
              {(state?.boards?.length || 0) > 0 && (
                <p className="text-sm text-gray-500">
                  O selecciona un tablero existente desde la barra lateral
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Board Manager Modal */}
        <BoardManager
          isOpen={showBoardManager}
          onClose={() => setShowBoardManager(false)}
        />
      </>
    );
  }

  const activeItem = getActiveItem();

  return (
    <>
      {/* Search and Filter Bar - Temporarily disabled to avoid conflicts with Header search */}
      {/* {(searchQuery || showSearchResults) && (
        <SearchAndFilter />
      )} */}

      {/* Main Content */}
      {showSearchResults ? (
        <SearchResults
          cards={filteredCards}
          query={searchQuery}
          onCardClick={handleCardClick}
          highlightText={highlightText}
        />
      ) : (
        <div className={`board-container flex-1 overflow-x-auto h-full ${currentBoard.background} ${isMobile ? 'p-3' : 'p-6'}`}>
          {/* Filter Indicator */}
          {cardFilter !== 'all' && (
            <div className="mb-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-700">
                    Filtro activo: {
                      cardFilter === 'assigned' ? 'Tarjetas asignadas a mí' :
                      cardFilter === 'created' ? 'Tarjetas creadas hoy' :
                      cardFilter === 'due-soon' ? 'Tarjetas que vencen pronto' :
                      'Filtro personalizado'
                    }
                  </span>
                </div>
                <button
                  onClick={() => {
                    // Note: cardFilter is not part of userPreferences in DatabaseAppContext
                    // This would need to be handled differently
                    console.log('Clear filter clicked');
                  }}
                  className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                >
                  Limpiar filtro
                </button>
              </div>
            </div>
          )}

          <DndContext
            sensors={sensors}
            collisionDetection={collisionDetectionStrategy}
            onDragStart={handleDragStart}
            onDragOver={handleDragOver}
            onDragEnd={handleDragEnd}
          >
            <div className={`flex ${isMobile ? 'space-x-3' : 'space-x-6'} min-w-max h-full`}>
              {/* Lists Container - Only for list reordering */}
              <SortableContext
                items={filteredBoard?.lists?.map(list => list.id) || []}
                strategy={horizontalListSortingStrategy}
              >
                {filteredBoard?.lists
                  .sort((a, b) => a.position - b.position)
                  .map((list) => (
                    <DndKitList
                      key={list.id}
                      list={list}
                      onAddCard={handleAddCard}
                      onCardClick={handleCardClick}
                      onCardUpdate={handleCardUpdate}
                      onListUpdate={handleListUpdate}
                      onListDelete={handleListDelete}
                    />
                  ))}
              </SortableContext>

              {/* Add New List */}
              <AddListForm boardId={filteredBoard?.id || ''} />
            </div>

            <DragOverlay dropAnimation={null}>
              {activeItem ? (
                activeItem.hasOwnProperty('cards') ? (
                  <DndKitList
                    list={activeItem as ListType}
                    onAddCard={handleAddCard}
                    onCardClick={handleCardClick}
                    onCardUpdate={handleCardUpdate}
                    onListUpdate={handleListUpdate}
                    onListDelete={handleListDelete}
                    isDragOverlay
                  />
                ) : (
                  <DndKitCard
                    card={activeItem as CardType}
                    onClick={() => {}}
                    onUpdate={handleCardUpdate}
                    isDragOverlay
                  />
                )
              ) : null}
            </DragOverlay>
          </DndContext>
        </div>
      )}

      {/* Card Modal */}
      {showModal && selectedCard && (
        <CardModal
          card={selectedCard}
          onClose={() => {
            setShowModal(false);
            setSelectedCard(null);
          }}
          onUpdate={(updatedCard) => {
            if (!currentBoard) return;

            console.log('🔄 DndKitBoard onUpdate called:', {
              cardId: updatedCard.id,
              description: updatedCard.description
            });

            // Find the list containing the card
            const listWithCard = currentBoard.lists.find(list =>
              list.cards.some(card => card.id === updatedCard.id)
            );

            if (listWithCard && updateCard) {
              console.log('📝 Calling updateCard:', {
                boardId: currentBoard.id,
                listId: listWithCard.id,
                cardId: updatedCard.id
              });

              updateCard(currentBoard.id, listWithCard.id, updatedCard)
                .then(() => {
                  console.log('✅ Card updated successfully');
                  // Update the selected card to reflect changes in the modal
                  setSelectedCard(updatedCard);
                })
                .catch(error => {
                  console.error('❌ Error updating card:', error);
                });
            } else {
              console.error('❌ Could not find list or updateCard function:', {
                listWithCard: !!listWithCard,
                updateCard: !!updateCard
              });
            }
          }}
        />
      )}

      {/* Drag and Drop Help */}
      <DragDropHelp />
    </>
  );
}
