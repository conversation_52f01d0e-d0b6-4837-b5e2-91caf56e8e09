import { User, UserFormData } from '../types';
import { SimpleAuthService } from './SimpleAuthService';
import { getApiUrl } from '../config/api';

const API_BASE_URL = getApiUrl();

class ApiService {
  // Get current user from SimpleAuthService
  private static getCurrentUser(): User | null {
    return SimpleAuthService.getInstance().getCurrentUser();
  }

  private static async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      // Handle empty responses (like 204 No Content)
      if (response.status === 204 || response.headers.get('content-length') === '0') {
        return null;
      }

      // Check if response has content before parsing JSON
      const text = await response.text();
      if (!text) {
        return null;
      }

      return JSON.parse(text);
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Auth methods
  static async login(credentials: { email: string; password: string }): Promise<{
    success: boolean;
    user?: User;
    error?: string;
  }> {
    try {
      console.log('🔐 ApiService.login called with:', { email: credentials.email, password: '***' });
      const body = JSON.stringify(credentials);
      console.log('📦 Request body:', body);

      const response = await this.request<{ success: boolean; user?: User; error?: string }>(
        '/auth/login',
        {
          method: 'POST',
          body: body,
        }
      );
      console.log('✅ ApiService.login response:', { success: response.success });
      return response;
    } catch (error) {
      console.error('❌ ApiService.login error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed',
      };
    }
  }

  // User methods
  static async getAllUsers(): Promise<User[]> {
    try {
      const currentUser = this.getCurrentUser();
      const userId = currentUser?.id;

      if (!userId) {
        console.warn('No current user found for getAllUsers request');
        return [];
      }

      return await this.request<User[]>(`/users?userId=${userId}`);
    } catch (error) {
      console.error('Failed to fetch users:', error);
      return [];
    }
  }

  static async createUser(userData: UserFormData): Promise<User> {
    const currentUser = this.getCurrentUser();
    const userId = currentUser?.id;

    if (!userId) {
      throw new Error('No current user found for createUser request');
    }

    return await this.request<User>(`/users?userId=${userId}`, {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  static async updateUser(id: string, userData: Partial<UserFormData>): Promise<User> {
    const currentUser = this.getCurrentUser();
    const userId = currentUser?.id;

    if (!userId) {
      throw new Error('No current user found for updateUser request');
    }

    return await this.request<User>(`/users/${id}?userId=${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  static async deleteUser(id: string): Promise<void> {
    const currentUser = this.getCurrentUser();
    const userId = currentUser?.id;

    if (!userId) {
      throw new Error('No current user found for deleteUser request');
    }

    await this.request(`/users/${id}?userId=${userId}`, {
      method: 'DELETE',
    });
  }

  static async getUserById(id: string): Promise<User | null> {
    try {
      return await this.request<User>(`/users/${id}`);
    } catch (error) {
      return null;
    }
  }

  static async getUserByEmail(email: string): Promise<User | null> {
    try {
      return await this.request<User>(`/users/email/${encodeURIComponent(email)}`);
    } catch (error) {
      return null;
    }
  }

  static async getUserByUsername(username: string): Promise<User | null> {
    try {
      return await this.request<User>(`/users/username/${encodeURIComponent(username)}`);
    } catch (error) {
      return null;
    }
  }

  // Team methods
  static async getAllTeams(): Promise<any[]> {
    try {
      const currentUser = this.getCurrentUser();
      const userId = currentUser?.id;

      if (!userId) {
        console.warn('No current user found for getAllTeams request');
        return [];
      }

      return await this.request<any[]>(`/teams?userId=${userId}`);
    } catch (error) {
      console.error('Failed to fetch teams:', error);
      return [];
    }
  }

  static async createTeam(teamData: any): Promise<any> {
    const currentUser = this.getCurrentUser();
    const userId = currentUser?.id;

    if (!userId) {
      throw new Error('No current user found for createTeam request');
    }

    return await this.request<any>(`/teams?userId=${userId}`, {
      method: 'POST',
      body: JSON.stringify(teamData),
    });
  }

  static async updateTeam(id: string, teamData: any): Promise<any> {
    const currentUser = this.getCurrentUser();
    const userId = currentUser?.id;

    if (!userId) {
      throw new Error('No current user found for updateTeam request');
    }

    return await this.request<any>(`/teams/${id}?userId=${userId}`, {
      method: 'PUT',
      body: JSON.stringify(teamData),
    });
  }

  static async deleteTeam(id: string): Promise<void> {
    const currentUser = this.getCurrentUser();
    const userId = currentUser?.id;

    if (!userId) {
      throw new Error('No current user found for deleteTeam request');
    }

    await this.request(`/teams/${id}?userId=${userId}`, {
      method: 'DELETE',
    });
  }

  // Board methods
  static async getAllBoards(userId?: string): Promise<any[]> {
    try {
      const url = userId ? `/boards?userId=${userId}` : '/boards';
      return await this.request<any[]>(url);
    } catch (error) {
      console.error('Failed to fetch boards:', error);
      return [];
    }
  }

  static async createBoard(boardData: any): Promise<any> {
    const currentUser = this.getCurrentUser();
    const userId = currentUser?.id;

    if (!userId) {
      throw new Error('No current user found for createBoard request');
    }

    return await this.request<any>(`/boards?userId=${userId}`, {
      method: 'POST',
      body: JSON.stringify(boardData),
    });
  }

  static async updateBoard(id: string, boardData: any): Promise<any> {
    return await this.request<any>(`/boards/${id}`, {
      method: 'PUT',
      body: JSON.stringify(boardData),
    });
  }

  static async deleteBoard(id: string): Promise<void> {
    await this.request(`/boards/${id}`, {
      method: 'DELETE',
    });
  }

  static async addMemberToBoard(boardId: string, userId: string, role: string = 'member'): Promise<any> {
    return await this.request(`/boards/${boardId}/members`, {
      method: 'POST',
      body: JSON.stringify({ userId, role }),
    });
  }

  static async removeMemberFromBoard(boardId: string, userId: string): Promise<any> {
    return await this.request(`/boards/${boardId}/members/${userId}`, {
      method: 'DELETE',
    });
  }

  static async updateMemberRole(boardId: string, userId: string, role: string): Promise<any> {
    return await this.request(`/boards/${boardId}/members/${userId}/role`, {
      method: 'PUT',
      body: JSON.stringify({ role }),
    });
  }

  // List methods
  static async createList(boardId: string, listData: any): Promise<any> {
    return await this.request<any>(`/boards/${boardId}/lists`, {
      method: 'POST',
      body: JSON.stringify(listData),
    });
  }

  static async updateList(boardId: string, listId: string, listData: any): Promise<any> {
    return await this.request<any>(`/boards/${boardId}/lists/${listId}`, {
      method: 'PUT',
      body: JSON.stringify(listData),
    });
  }

  static async deleteList(boardId: string, listId: string): Promise<any> {
    return await this.request<any>(`/boards/${boardId}/lists/${listId}`, {
      method: 'DELETE',
    });
  }

  // Card methods
  static async createCard(boardId: string, listId: string, cardData: any): Promise<any> {
    return await this.request<any>(`/boards/${boardId}/lists/${listId}/cards`, {
      method: 'POST',
      body: JSON.stringify(cardData),
    });
  }

  static async updateCard(boardId: string, listId: string, cardId: string, cardData: any): Promise<any> {
    return await this.request<any>(`/boards/${boardId}/lists/${listId}/cards/${cardId}`, {
      method: 'PUT',
      body: JSON.stringify(cardData),
    });
  }

  static async moveCard(boardId: string, cardId: string, sourceListId: string, targetListId: string, newPosition: number): Promise<any> {
    return await this.request<any>(`/boards/${boardId}/cards/${cardId}/move`, {
      method: 'PUT',
      body: JSON.stringify({ sourceListId, targetListId, newPosition }),
    });
  }

  static async copyCard(boardId: string, listId: string, cardId: string, title?: string): Promise<any> {
    return await this.request<any>(`/boards/${boardId}/lists/${listId}/cards/${cardId}/copy`, {
      method: 'POST',
      body: JSON.stringify({ title }),
    });
  }

  // User preferences
  static async getUserPreferences(userId: string): Promise<any> {
    try {
      return await this.request<any>(`/users/${userId}/preferences`);
    } catch (error) {
      return {
        sidebarOpen: false,
        theme: 'light',
        language: 'es'
      };
    }
  }

  static async saveUserPreferences(userId: string, preferences: any): Promise<void> {
    await this.request(`/users/${userId}/preferences`, {
      method: 'PUT',
      body: JSON.stringify(preferences),
    });
  }

  // Activities
  static async getActivities(boardId?: string): Promise<any[]> {
    try {
      const endpoint = boardId ? `/activities?boardId=${boardId}` : '/activities';
      return await this.request<any[]>(endpoint);
    } catch (error) {
      console.error('Failed to fetch activities:', error);
      return [];
    }
  }

  static async createActivity(activityData: any): Promise<any> {
    return await this.request<any>('/activities', {
      method: 'POST',
      body: JSON.stringify(activityData),
    });
  }

  // Notifications
  static async getNotifications(userId: string): Promise<any[]> {
    try {
      return await this.request<any[]>(`/notifications?userId=${userId}`);
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
      return [];
    }
  }

  static async markNotificationAsRead(notificationId: string): Promise<void> {
    await this.request(`/notifications/${notificationId}/read`, {
      method: 'PUT',
    });
  }

  // Labels
  static async getLabels(boardId?: string): Promise<any[]> {
    try {
      const endpoint = boardId ? `/labels?boardId=${boardId}` : '/labels';
      return await this.request<any[]>(endpoint);
    } catch (error) {
      return [
        { id: '1', name: 'Alta Prioridad', color: '#ef4444' },
        { id: '2', name: 'Prioridad Media', color: '#f97316' },
        { id: '3', name: 'Baja Prioridad', color: '#22c55e' },
        { id: '4', name: 'Error', color: '#dc2626' },
        { id: '5', name: 'Funcionalidad', color: '#3b82f6' },
        { id: '6', name: 'Documentación', color: '#8b5cf6' }
      ];
    }
  }

  static async createLabel(boardId: string, labelData: { name: string; color: string }): Promise<any> {
    return await this.request(`/boards/${boardId}/labels`, {
      method: 'POST',
      body: JSON.stringify(labelData),
    });
  }

  // Health check
  static async healthCheck(): Promise<{ status: string; message: string }> {
    try {
      const healthUrl = API_BASE_URL.replace('/api', '/health');
      const response = await fetch(healthUrl);
      return await response.json();
    } catch (error) {
      throw new Error('API server is not running');
    }
  }
}

export default ApiService;
