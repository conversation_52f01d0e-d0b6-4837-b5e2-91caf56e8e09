import React from 'react';
import { Board } from '../types';
import { 
  Users, 
  Calendar, 
  CheckSquare, 
  AlertCircle, 
  TrendingUp,
  Clock,
  Target,
  Activity
} from 'lucide-react';

interface BoardStatsProps {
  board: Board;
}

export function BoardStats({ board }: BoardStatsProps) {
  const allCards = board.lists.flatMap(list => list.cards);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  
  // Calculate statistics
  const stats = {
    totalCards: allCards.length,
    totalMembers: board.members.length,
    completedCards: allCards.filter(card => {
      const totalTasks = card.checklist.length;
      const completedTasks = card.checklist.filter(item => item.completed).length;
      return totalTasks > 0 && completedTasks === totalTasks;
    }).length,
    overdueTasks: allCards.filter(card => 
      card.dueDate && card.dueDate < today
    ).length,
    dueTodayTasks: allCards.filter(card => {
      if (!card.dueDate) return false;
      const cardDate = new Date(card.dueDate.getFullYear(), card.dueDate.getMonth(), card.dueDate.getDate());
      return cardDate.getTime() === today.getTime();
    }).length,
    totalComments: allCards.reduce((sum, card) => sum + card.comments.length, 0),
    totalAttachments: allCards.reduce((sum, card) => sum + card.attachments.length, 0),
    activeMembers: board.members.filter(member => 
      allCards.some(card => card.assignedMembers.some(am => am.id === member.id))
    ).length
  };

  const completionRate = stats.totalCards > 0 ? (stats.completedCards / stats.totalCards) * 100 : 0;

  const statItems = [
    {
      label: 'Total de Tarjetas',
      value: stats.totalCards,
      icon: Target,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      label: 'Tarjetas Completadas',
      value: stats.completedCards,
      icon: CheckSquare,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      percentage: completionRate.toFixed(1) + '%'
    },
    {
      label: 'Tareas Vencidas',
      value: stats.overdueTasks,
      icon: AlertCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    },
    {
      label: 'Vencen Hoy',
      value: stats.dueTodayTasks,
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      label: 'Miembros Activos',
      value: stats.activeMembers,
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      subtitle: `de ${stats.totalMembers} total`
    },
    {
      label: 'Actividad Total',
      value: stats.totalComments + stats.totalAttachments,
      icon: Activity,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      subtitle: `${stats.totalComments} comentarios, ${stats.totalAttachments} archivos`
    }
  ];

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Estadísticas del Tablero</h3>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Calendar className="w-4 h-4" />
          <span>Actualizado: {board.updatedAt.toLocaleDateString()}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {statItems.map((item, index) => (
          <div key={index} className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${item.bgColor}`}>
                <item.icon className={`w-5 h-5 ${item.color}`} />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="text-2xl font-bold text-gray-900">{item.value}</span>
                  {item.percentage && (
                    <span className="text-sm text-green-600 font-medium">
                      {item.percentage}
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600">{item.label}</p>
                {item.subtitle && (
                  <p className="text-xs text-gray-500 mt-1">{item.subtitle}</p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Progress Bar */}
      <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Progreso General</span>
          <span className="text-sm text-gray-600">{completionRate.toFixed(1)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${completionRate}%` }}
          />
        </div>
        <p className="text-xs text-gray-500 mt-2">
          {stats.completedCards} de {stats.totalCards} tarjetas completadas
        </p>
      </div>

      {/* Quick Actions */}
      <div className="mt-6 flex flex-wrap gap-2">
        <button className="flex items-center space-x-1 px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm hover:bg-blue-200 transition-colors">
          <TrendingUp className="w-3 h-3" />
          <span>Ver Tendencias</span>
        </button>
        <button className="flex items-center space-x-1 px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm hover:bg-green-200 transition-colors">
          <CheckSquare className="w-3 h-3" />
          <span>Tareas Completadas</span>
        </button>
        <button className="flex items-center space-x-1 px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-sm hover:bg-orange-200 transition-colors">
          <Clock className="w-3 h-3" />
          <span>Próximos Vencimientos</span>
        </button>
      </div>
    </div>
  );
}
