import { useMemo } from 'react';
import { Card, Board } from '../types';

interface SearchFilters {
  query: string;
  labels: string[];
  members: string[];
  dueDate: 'overdue' | 'today' | 'week' | 'none' | '';
  hasChecklist: boolean | null;
  hasAttachments: boolean | null;
}

export function useSearch(board: Board | null, filters: SearchFilters) {
  const filteredCards = useMemo(() => {
    if (!board) return [];

    const allCards = board.lists.flatMap(list => 
      list.cards.map(card => ({
        ...card,
        listId: list.id,
        listTitle: list.title
      }))
    );

    return allCards.filter(card => {
      // Text search - search in title, description, and comments
      if (filters.query) {
        const searchLower = filters.query.toLowerCase();
        const matchesTitle = card.title.toLowerCase().includes(searchLower);
        const matchesDescription = card.description?.toLowerCase().includes(searchLower) || false;
        const matchesComments = card.comments.some(comment => 
          comment.text.toLowerCase().includes(searchLower) ||
          comment.author.name.toLowerCase().includes(searchLower)
        );
        const matchesChecklist = card.checklist.some(item =>
          item.text.toLowerCase().includes(searchLower)
        );
        
        if (!matchesTitle && !matchesDescription && !matchesComments && !matchesChecklist) {
          return false;
        }
      }

      // Label filter
      if (filters.labels.length > 0) {
        const hasMatchingLabel = card.labels.some(label => 
          filters.labels.includes(label.id)
        );
        if (!hasMatchingLabel) return false;
      }

      // Member filter
      if (filters.members.length > 0) {
        const hasMatchingMember = card.assignedMembers.some(member => 
          filters.members.includes(member.id)
        );
        if (!hasMatchingMember) return false;
      }

      // Due date filter
      if (filters.dueDate) {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
        const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

        switch (filters.dueDate) {
          case 'overdue':
            if (!card.dueDate || card.dueDate >= today) return false;
            break;
          case 'today':
            if (!card.dueDate || card.dueDate < today || card.dueDate >= tomorrow) return false;
            break;
          case 'week':
            if (!card.dueDate || card.dueDate < today || card.dueDate > weekFromNow) return false;
            break;
          case 'none':
            if (card.dueDate) return false;
            break;
        }
      }

      // Checklist filter
      if (filters.hasChecklist !== null) {
        const hasChecklist = card.checklist.length > 0;
        if (filters.hasChecklist !== hasChecklist) return false;
      }

      // Attachments filter
      if (filters.hasAttachments !== null) {
        const hasAttachments = card.attachments.length > 0;
        if (filters.hasAttachments !== hasAttachments) return false;
      }

      return true;
    });
  }, [board, filters]);

  const searchStats = useMemo(() => {
    if (!board) return { total: 0, filtered: 0, overdue: 0, dueToday: 0, completed: 0 };

    const allCards = board.lists.flatMap(list => list.cards);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);

    return {
      total: allCards.length,
      filtered: filteredCards.length,
      overdue: allCards.filter(card => card.dueDate && card.dueDate < today).length,
      dueToday: allCards.filter(card => 
        card.dueDate && card.dueDate >= today && card.dueDate < tomorrow
      ).length,
      completed: allCards.filter(card => {
        const totalTasks = card.checklist.length;
        const completedTasks = card.checklist.filter(item => item.completed).length;
        return totalTasks > 0 && completedTasks === totalTasks;
      }).length
    };
  }, [board, filteredCards]);

  const highlightText = (text: string, query: string) => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) 
        ? `<mark class="bg-yellow-200 px-1 rounded">${part}</mark>`
        : part
    ).join('');
  };

  return {
    filteredCards,
    searchStats,
    highlightText
  };
}

export type { SearchFilters };
