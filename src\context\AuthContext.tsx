import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { SimpleAuthService, LoginCredentials } from '../services/SimpleAuthService';
import { User } from '../types';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => void;
  updateProfile: (updates: Partial<User>) => Promise<boolean>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<boolean>;
  demoLogin: (userType: 'admin' | 'member') => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [authService] = useState(() => SimpleAuthService.getInstance());

  useEffect(() => {
    // Subscribe to auth state changes
    const unsubscribe = authService.subscribe((user) => {
      setUser(user);
    });

    // Check user persistence periodically
    const checkUserPersistence = () => {
      authService.checkUserPersistence();
    };

    const persistenceInterval = setInterval(checkUserPersistence, 30000);

    return () => {
      unsubscribe();
      clearInterval(persistenceInterval);
    };
  }, [authService]);

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await authService.login(credentials);
      if (!result.success) {
        setError(result.error || 'Error de inicio de sesión');
        return false;
      }
      return true;
    } catch (error) {
      setError('Error de conexión');
      return false;
    } finally {
      setIsLoading(false);
    }
  };



  const logout = () => {
    authService.logout();
  };

  const updateProfile = async (updates: Partial<User>): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await authService.updateProfile(updates);
      if (!result.success) {
        setError(result.error || 'Error al actualizar el perfil');
        return false;
      }
      return true;
    } catch (error) {
      setError('Error de conexión');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await authService.changePassword(currentPassword, newPassword);
      if (!result.success) {
        setError(result.error || 'Error al cambiar la contraseña');
        return false;
      }
      return true;
    } catch (error) {
      setError('Error de conexión');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const demoLogin = async (userType: 'admin' | 'member' = 'admin') => {
    setIsLoading(true);
    try {
      await authService.demoLogin(userType);
    } finally {
      setIsLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    error,
    login,
    logout,
    updateProfile,
    changePassword,
    demoLogin,
    clearError
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  return context;
}
