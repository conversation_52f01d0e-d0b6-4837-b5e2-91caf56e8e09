import React, { useState } from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { MentionInput } from './MentionInput';
import { MentionText } from './MentionText';
import { getAvailableUsersForMentions } from '../utils/mentionUtils';

/**
 * Test component to demonstrate @mention restriction functionality
 * This component shows how @mentions are filtered based on team assignment
 */
export function MentionTestComponent() {
  const { getCurrentBoard, state } = useDatabaseApp();
  const currentBoard = getCurrentBoard();
  const [testText, setTestText] = useState('');

  if (!currentBoard) {
    return (
      <div className="p-4 bg-yellow-100 border border-yellow-400 rounded-lg">
        <p className="text-yellow-800">No hay tablero seleccionado para probar las menciones.</p>
      </div>
    );
  }

  const availableUsers = getAvailableUsersForMentions(currentBoard, state.teams);
  const allBoardMembers = currentBoard.members || [];
  const boardTeam = currentBoard.teamId ? state.teams.find(team => team.id === currentBoard.teamId) : null;

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-2xl mx-auto">
      <h2 className="text-xl font-bold text-gray-900 mb-4">
        🧪 Prueba de Restricción de @Menciones
      </h2>

      {/* Board Info */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-2">Información del Tablero</h3>
        <p><strong>Tablero:</strong> {currentBoard.title}</p>
        <p><strong>Asignado a equipo:</strong> {boardTeam ? boardTeam.name : 'No asignado'}</p>
        <p><strong>Total miembros del tablero:</strong> {allBoardMembers.length}</p>
        <p><strong>Usuarios disponibles para @menciones:</strong> {availableUsers.length}</p>
        
        {boardTeam && (
          <div className="mt-2">
            <p><strong>Miembros del equipo:</strong></p>
            <ul className="list-disc list-inside ml-4 text-sm">
              {boardTeam.members.map(member => {
                const user = state.users.find(u => u.id === member.userId);
                return (
                  <li key={member.userId}>
                    {user?.name || 'Usuario desconocido'} ({member.role})
                  </li>
                );
              })}
            </ul>
          </div>
        )}
      </div>

      {/* Available Users for Mentions */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-900 mb-2">
          Usuarios Disponibles para @Menciones
        </h3>
        {availableUsers.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {availableUsers.map(user => (
              <div key={user.id} className="flex items-center space-x-2 text-sm">
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-6 h-6 rounded-full"
                />
                <span>{user.name}</span>
                <span className="text-gray-500">(@{user.name})</span>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-blue-800">No hay usuarios disponibles para mencionar.</p>
        )}
      </div>

      {/* Test Input */}
      <div className="mb-6">
        <h3 className="font-semibold text-gray-900 mb-2">
          Prueba las @Menciones
        </h3>
        <p className="text-sm text-gray-600 mb-3">
          Escribe @ seguido del nombre de un usuario. Solo verás sugerencias de usuarios 
          {boardTeam ? ' que son miembros del equipo asignado al tablero' : ' que son miembros del tablero'}.
        </p>
        <MentionInput
          value={testText}
          onChange={setTestText}
          placeholder="Escribe @ para probar las menciones..."
          multiline={true}
          rows={3}
        />
      </div>

      {/* Preview */}
      {testText && (
        <div className="mb-6 p-4 bg-green-50 rounded-lg">
          <h3 className="font-semibold text-green-900 mb-2">Vista Previa</h3>
          <MentionText text={testText} />
        </div>
      )}

      {/* Restriction Status */}
      <div className="p-4 bg-gray-100 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-2">Estado de la Restricción</h3>
        {boardTeam ? (
          <div className="text-green-800">
            ✅ <strong>Restricción Activa:</strong> Solo se pueden mencionar miembros del equipo "{boardTeam.name}"
          </div>
        ) : (
          <div className="text-blue-800">
            ℹ️ <strong>Sin Restricción:</strong> Se pueden mencionar todos los miembros del tablero
          </div>
        )}
      </div>
    </div>
  );
}
