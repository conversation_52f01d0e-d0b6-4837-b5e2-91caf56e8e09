import React from 'react';

interface DropZoneProps {
  position: number;
  onDragOver: (e: React.DragEvent, targetId: string, targetType?: 'card' | 'list') => void;
  onDragLeave: () => void;
  onDrop: (e: React.DragEvent, targetId: string) => void;
  isDragOver: boolean;
  dragType: 'card' | 'list' | null;
}

export function DropZone({ 
  position, 
  onDragOver, 
  onDragLeave, 
  onDrop, 
  isDragOver,
  dragType 
}: DropZoneProps) {
  // Only show drop zone when dragging a list
  if (dragType !== 'list') {
    return null;
  }

  return (
    <div
      className={`w-3 min-h-96 transition-all duration-200 flex items-center justify-center ${
        isDragOver
          ? 'bg-blue-300 border-2 border-blue-500 border-dashed rounded-lg shadow-lg w-8'
          : 'bg-transparent hover:bg-blue-100 hover:border-2 hover:border-blue-300 hover:border-dashed hover:rounded-lg hover:w-6'
      }`}
      onDragOver={(e) => onDragOver(e, position.toString(), 'list')}
      onDragLeave={onDragLeave}
      onDrop={(e) => onDrop(e, position.toString())}
    >
      {isDragOver && (
        <div className="text-blue-700 text-xs font-bold transform -rotate-90 whitespace-nowrap">
          Soltar
        </div>
      )}
    </div>
  );
}
