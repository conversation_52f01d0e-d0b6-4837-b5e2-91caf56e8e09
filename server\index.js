import express from 'express';
import cors from 'cors';
import { PrismaClient } from '@prisma/client';

const app = express();
const prisma = new PrismaClient();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: '*', // Allow all origins
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-user-id']
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// User preferences endpoints
app.get('/api/users/:userId/preferences', async (req, res) => {
  try {
    const { userId } = req.params;

    console.log('📋 Getting user preferences:', { userId });

    let preferences = await prisma.userPreferences.findUnique({
      where: { userId },
      include: {
        lastBoard: {
          select: {
            id: true,
            title: true
          }
        }
      }
    });

    // Create default preferences if they don't exist
    if (!preferences) {
      preferences = await prisma.userPreferences.create({
        data: {
          userId,
          sidebarOpen: false,
          theme: 'light',
          language: 'es'
        },
        include: {
          lastBoard: {
            select: {
              id: true,
              title: true
            }
          }
        }
      });
    }

    console.log('✅ User preferences retrieved:', preferences.id);
    res.json(preferences);
  } catch (error) {
    console.error('❌ Get user preferences error:', error);
    res.status(500).json({ error: 'Error al obtener preferencias del usuario' });
  }
});

app.put('/api/users/:userId/preferences', async (req, res) => {
  try {
    const { userId } = req.params;
    const { sidebarOpen, theme, language, lastBoardId } = req.body;

    console.log('📋 Updating user preferences:', { userId, lastBoardId });

    const preferences = await prisma.userPreferences.upsert({
      where: { userId },
      update: {
        ...(sidebarOpen !== undefined && { sidebarOpen }),
        ...(theme !== undefined && { theme }),
        ...(language !== undefined && { language }),
        ...(lastBoardId !== undefined && { lastBoardId }),
        updatedAt: new Date()
      },
      create: {
        userId,
        sidebarOpen: sidebarOpen ?? false,
        theme: theme ?? 'light',
        language: language ?? 'es',
        lastBoardId: lastBoardId ?? null
      },
      include: {
        lastBoard: {
          select: {
            id: true,
            title: true
          }
        }
      }
    });

    console.log('✅ User preferences updated:', preferences.id);
    res.json(preferences);
  } catch (error) {
    console.error('❌ Update user preferences error:', error);
    res.status(500).json({ error: 'Error al actualizar preferencias del usuario' });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Trello API Server is running' });
});

// Simple test endpoint
app.get('/api/test', (req, res) => {
  res.json({ success: true, message: 'API is working', timestamp: new Date().toISOString() });
});

// Debug endpoint
app.post('/api/debug/login', (req, res) => {
  console.log('🐛 Debug login - Headers:', req.headers);
  console.log('🐛 Debug login - Body:', req.body);
  console.log('🐛 Debug login - Raw body type:', typeof req.body);
  res.json({
    success: true,
    debug: {
      headers: req.headers,
      body: req.body,
      bodyType: typeof req.body
    }
  });
});

// Auth routes
app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('🔐 Login request received');
    console.log('📦 Request body:', req.body);
    console.log('📦 Request headers:', req.headers);

    const { email, password } = req.body;

    if (!email || !password) {
      console.log('❌ Missing email or password');
      return res.status(400).json({ success: false, error: 'Email y contraseña son requeridos' });
    }

    console.log('🔐 Login attempt:', { email, password: '***' });

    // Find user by email or username
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { email: email.toLowerCase() },
          { username: email.toLowerCase() }
        ]
      },
      include: {
        permissions: true
      }
    });

    console.log('👤 User found:', user ? `${user.name} (${user.email})` : 'null');

    if (!user) {
      console.log('❌ User not found for email/username:', email);
      return res.status(401).json({ success: false, error: 'Usuario no encontrado' });
    }

    if (!user.isActive) {
      console.log('❌ User is not active');
      return res.status(401).json({ success: false, error: 'Usuario desactivado. Contacta al administrador.' });
    }

    // Simple password check (in production, use proper hashing)
    console.log('🔑 Password check:', { provided: password, stored: user.password, match: user.password === password });
    if (user.password !== password) {
      console.log('❌ Password mismatch');
      return res.status(401).json({ success: false, error: 'Contraseña incorrecta' });
    }

    // Convert user to frontend format
    const userResponse = {
      id: user.id,
      username: user.username,
      name: user.name,
      email: user.email,
      phone: user.phone || '',
      password: user.password,
      role: user.role.toLowerCase(),
      avatar: user.avatar || '',
      isActive: user.isActive,
      permissions: user.permissions ? {
        canCreateBoards: user.permissions.canCreateBoards,
        canEditCards: user.permissions.canEditCards,
        canDeleteCards: user.permissions.canDeleteCards,
        canManageUsers: user.permissions.canManageUsers,
        canViewReports: user.permissions.canViewReports,
        canManageSettings: user.permissions.canManageSettings,
        canInviteUsers: user.permissions.canInviteUsers,
        canArchiveCards: user.permissions.canArchiveCards,
      } : {
        canCreateBoards: false,
        canEditCards: true,
        canDeleteCards: false,
        canManageUsers: false,
        canViewReports: true,
        canManageSettings: false,
        canInviteUsers: false,
        canArchiveCards: true,
      },
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    console.log('✅ Login successful, sending response');
    res.json({ success: true, user: userResponse });
  } catch (error) {
    console.error('❌ Login error:', error);
    res.status(500).json({ success: false, error: 'Error de conexión' });
  }
});

// Users routes
app.get('/api/users', async (req, res) => {
  try {
    const userId = req.query.userId || req.headers['x-user-id'];

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get requesting user to check permissions
    const requestingUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!requestingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    console.log('👥 Getting users for:', { userId, role: requestingUser.role });

    const users = await prisma.user.findMany({
      include: {
        permissions: true,
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    let filteredUsers = users;

    // Apply role-based filtering
    if (requestingUser.role !== 'ADMIN') {
      // MEMBER/OBSERVER users should only see users from their boards/teams
      const userBoards = await prisma.board.findMany({
        where: {
          members: {
            some: {
              userId: userId
            }
          }
        },
        include: {
          members: {
            include: {
              user: true
            }
          }
        }
      });

      const userTeams = await prisma.team.findMany({
        where: {
          members: {
            some: {
              userId: userId
            }
          }
        },
        include: {
          members: {
            include: {
              user: true
            }
          }
        }
      });

      const visibleUserIds = new Set();

      // Add current user
      visibleUserIds.add(userId);

      // Add users from accessible boards
      userBoards.forEach(board => {
        board.members.forEach(member => {
          visibleUserIds.add(member.userId);
        });
      });

      // Add users from teams where current user is member
      userTeams.forEach(team => {
        team.members.forEach(member => {
          visibleUserIds.add(member.userId);
        });
      });

      filteredUsers = users.filter(user => visibleUserIds.has(user.id));
      console.log(`🔒 Filtered users for ${requestingUser.role}: ${filteredUsers.length}/${users.length}`);
    } else {
      console.log(`🛡️ Admin user - showing all ${users.length} users`);
    }

    const usersResponse = filteredUsers.map(user => ({
      id: user.id,
      username: user.username,
      name: user.name,
      email: user.email,
      phone: user.phone || '',
      password: user.password,
      role: user.role.toLowerCase(),
      avatar: user.avatar || '',
      isActive: user.isActive,
      permissions: user.permissions ? {
        canCreateBoards: user.permissions.canCreateBoards,
        canEditCards: user.permissions.canEditCards,
        canDeleteCards: user.permissions.canDeleteCards,
        canManageUsers: user.permissions.canManageUsers,
        canViewReports: user.permissions.canViewReports,
        canManageSettings: user.permissions.canManageSettings,
        canInviteUsers: user.permissions.canInviteUsers,
        canArchiveCards: user.permissions.canArchiveCards,
      } : {
        canCreateBoards: false,
        canEditCards: true,
        canDeleteCards: false,
        canManageUsers: false,
        canViewReports: true,
        canManageSettings: false,
        canInviteUsers: false,
        canArchiveCards: true,
      },
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }));

    res.json(usersResponse);
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ error: 'Error fetching users' });
  }
});

// Special endpoint for admins to get ALL users (for team management)
app.get('/api/users/admin/all', async (req, res) => {
  try {
    const requestingUserId = req.headers['x-user-id'];

    if (!requestingUserId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Verify requesting user is admin
    const requestingUser = await prisma.user.findUnique({
      where: { id: requestingUserId }
    });

    if (!requestingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (requestingUser.role !== 'ADMIN') {
      console.log('❌ Non-admin user attempted to access admin-only endpoint:', { requestingUserId, role: requestingUser.role });
      return res.status(403).json({ error: 'Solo los administradores pueden acceder a esta funcionalidad' });
    }

    console.log('🛡️ Admin requesting all users for team management:', requestingUserId);

    const users = await prisma.user.findMany({
      include: {
        permissions: true,
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    const usersResponse = users.map(user => ({
      id: user.id,
      username: user.username,
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role.toLowerCase(),
      avatar: user.avatar,
      isActive: user.isActive,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      permissions: user.permissions
    }));

    console.log(`✅ Admin fetched all ${usersResponse.length} users for team management`);
    res.json(usersResponse);
  } catch (error) {
    console.error('Get all users for admin error:', error);
    res.status(500).json({ error: 'Error fetching all users' });
  }
});

app.post('/api/users', async (req, res) => {
  try {
    const { username, name, email, phone, password, role } = req.body;
    const requestingUserId = req.query.userId || req.headers['x-user-id'];

    // CRITICAL: Only ADMIN users can create users
    if (!requestingUserId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const requestingUser = await prisma.user.findUnique({
      where: { id: requestingUserId }
    });

    if (!requestingUser) {
      return res.status(404).json({ error: 'Requesting user not found' });
    }

    if (requestingUser.role !== 'ADMIN') {
      console.log('❌ Non-admin user attempted to create user:', { requestingUserId, role: requestingUser.role });
      return res.status(403).json({ error: 'Solo los administradores pueden crear usuarios' });
    }

    console.log('👤 Admin creating user:', { requestingUserId, newUserEmail: email });

    const user = await prisma.user.create({
      data: {
        username,
        name,
        email,
        phone,
        password,
        role: role.toUpperCase(),
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=3b82f6&color=fff&size=128`,
        permissions: {
          create: {
            canCreateBoards: role === 'admin',
            canEditCards: true,
            canDeleteCards: role === 'admin',
            canManageUsers: role === 'admin',
            canViewReports: true,
            canManageSettings: role === 'admin',
            canInviteUsers: role === 'admin',
            canArchiveCards: true,
          }
        }
      },
      include: {
        permissions: true,
      }
    });

    const userResponse = {
      id: user.id,
      username: user.username,
      name: user.name,
      email: user.email,
      phone: user.phone || '',
      password: user.password,
      role: user.role.toLowerCase(),
      avatar: user.avatar || '',
      isActive: user.isActive,
      permissions: user.permissions ? {
        canCreateBoards: user.permissions.canCreateBoards,
        canEditCards: user.permissions.canEditCards,
        canDeleteCards: user.permissions.canDeleteCards,
        canManageUsers: user.permissions.canManageUsers,
        canViewReports: user.permissions.canViewReports,
        canManageSettings: user.permissions.canManageSettings,
        canInviteUsers: user.permissions.canInviteUsers,
        canArchiveCards: user.permissions.canArchiveCards,
      } : {
        canCreateBoards: false,
        canEditCards: true,
        canDeleteCards: false,
        canManageUsers: false,
        canViewReports: true,
        canManageSettings: false,
        canInviteUsers: false,
        canArchiveCards: true,
      },
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    res.status(201).json(userResponse);
  } catch (error) {
    console.error('Create user error:', error);
    if (error.code === 'P2002') {
      res.status(400).json({ error: 'Email or username already exists' });
    } else {
      res.status(500).json({ error: 'Error creating user' });
    }
  }
});

// Update user
app.put('/api/users/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { username, name, email, phone, password, role, isActive } = req.body;
    const requestingUserId = req.query.userId || req.headers['x-user-id'];

    // CRITICAL: Only ADMIN users can update users (except own profile)
    if (!requestingUserId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const requestingUser = await prisma.user.findUnique({
      where: { id: requestingUserId }
    });

    if (!requestingUser) {
      return res.status(404).json({ error: 'Requesting user not found' });
    }

    // Allow users to update their own profile, but only admins can update others
    const isUpdatingSelf = requestingUserId === id;
    const isAdmin = requestingUser.role === 'ADMIN';

    if (!isUpdatingSelf && !isAdmin) {
      console.log('❌ Non-admin user attempted to update another user:', { requestingUserId, targetUserId: id });
      return res.status(403).json({ error: 'Solo puedes actualizar tu propio perfil o ser administrador' });
    }

    // Only admins can change roles
    if (role && role !== requestingUser.role && !isAdmin) {
      console.log('❌ Non-admin user attempted to change role:', { requestingUserId, newRole: role });
      return res.status(403).json({ error: 'Solo los administradores pueden cambiar roles' });
    }

    console.log('🔄 Updating user:', { id, name, email, role, isActive, requestingUserId, isAdmin });

    const user = await prisma.user.update({
      where: { id },
      data: {
        ...(username && { username }),
        ...(name && { name }),
        ...(email && { email }),
        ...(phone && { phone }),
        ...(password && { password }),
        ...(role && { role: role.toUpperCase() }),
        ...(isActive !== undefined && { isActive }),
      },
      include: {
        permissions: true,
      }
    });

    const userResponse = {
      id: user.id,
      username: user.username,
      name: user.name,
      email: user.email,
      phone: user.phone || '',
      password: user.password,
      role: user.role.toLowerCase(),
      avatar: user.avatar || '',
      isActive: user.isActive,
      permissions: user.permissions ? {
        canCreateBoards: user.permissions.canCreateBoards,
        canEditCards: user.permissions.canEditCards,
        canDeleteCards: user.permissions.canDeleteCards,
        canManageUsers: user.permissions.canManageUsers,
        canViewReports: user.permissions.canViewReports,
        canManageSettings: user.permissions.canManageSettings,
        canInviteUsers: user.permissions.canInviteUsers,
        canArchiveCards: user.permissions.canArchiveCards,
      } : {
        canCreateBoards: false,
        canEditCards: true,
        canDeleteCards: false,
        canManageUsers: false,
        canViewReports: true,
        canManageSettings: false,
        canInviteUsers: false,
        canArchiveCards: true,
      },
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    console.log('✅ User updated successfully:', user.name);
    res.json(userResponse);
  } catch (error) {
    console.error('❌ Update user error:', error);
    if (error.code === 'P2002') {
      res.status(400).json({ error: 'Email or username already exists' });
    } else if (error.code === 'P2025') {
      res.status(404).json({ error: 'User not found' });
    } else {
      res.status(500).json({ error: 'Error updating user' });
    }
  }
});

// Delete user
app.delete('/api/users/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const requestingUserId = req.query.userId || req.headers['x-user-id'];

    // CRITICAL: Only ADMIN users can delete users
    if (!requestingUserId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const requestingUser = await prisma.user.findUnique({
      where: { id: requestingUserId }
    });

    if (!requestingUser) {
      return res.status(404).json({ error: 'Requesting user not found' });
    }

    if (requestingUser.role !== 'ADMIN') {
      console.log('❌ Non-admin user attempted to delete user:', { requestingUserId, targetUserId: id });
      return res.status(403).json({ error: 'Solo los administradores pueden eliminar usuarios' });
    }

    // Prevent self-deletion
    if (requestingUserId === id) {
      console.log('❌ Admin attempted to delete themselves:', requestingUserId);
      return res.status(400).json({ error: 'No puedes eliminar tu propia cuenta' });
    }

    console.log('🗑️ Admin deleting user:', { requestingUserId, targetUserId: id });

    await prisma.user.delete({
      where: { id }
    });

    console.log('✅ User deleted successfully');
    res.json({ success: true, message: 'User deleted successfully' });
  } catch (error) {
    console.error('❌ Delete user error:', error);
    if (error.code === 'P2025') {
      res.status(404).json({ error: 'User not found' });
    } else {
      res.status(500).json({ error: 'Error deleting user' });
    }
  }
});

// Teams endpoints
app.get('/api/teams', async (req, res) => {
  try {
    const userId = req.query.userId || req.headers['x-user-id'];

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get requesting user to check permissions
    const requestingUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!requestingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    console.log('👥 Getting teams for:', { userId, role: requestingUser.role });

    let teams;

    // Apply role-based filtering for teams
    if (requestingUser.role === 'ADMIN') {
      // ADMIN users can see all teams
      teams = await prisma.team.findMany({
        include: {
          settings: true,
          members: {
            include: {
              user: true
            }
          },
          boards: true,
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
      console.log(`🛡️ Admin user - showing all ${teams.length} teams`);
    } else {
      // MEMBER/OBSERVER users can only see teams where they are members
      teams = await prisma.team.findMany({
        where: {
          members: {
            some: {
              userId: userId
            }
          }
        },
        include: {
          settings: true,
          members: {
            include: {
              user: true
            }
          },
          boards: true,
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
      console.log(`🔒 Filtered teams for ${requestingUser.role}: ${teams.length} teams where user is member`);
    }

    const teamsResponse = teams.map(team => ({
      id: team.id,
      name: team.name,
      description: team.description || '',
      avatar: team.avatar || '',
      visibility: team.visibility.toLowerCase(),
      members: team.members.map(member => ({
        userId: member.userId,
        role: member.role.toLowerCase(),
        joinedAt: member.joinedAt,
        invitedBy: member.invitedBy,
      })),
      boards: team.boards.map(board => board.id),
      settings: team.settings ? {
        allowMembersToCreateBoards: team.settings.allowMembersToCreateBoards,
        allowMembersToInvite: team.settings.allowMembersToInvite,
        defaultBoardVisibility: team.settings.defaultBoardVisibility.toLowerCase(),
        requireApprovalForJoining: team.settings.requireApprovalForJoining,
      } : {
        allowMembersToCreateBoards: false,
        allowMembersToInvite: false,
        defaultBoardVisibility: 'team',
        requireApprovalForJoining: true,
      },
      createdAt: team.createdAt,
      updatedAt: team.updatedAt,
    }));

    res.json(teamsResponse);
  } catch (error) {
    console.error('❌ Get teams error:', error);
    res.status(500).json({ error: 'Error fetching teams' });
  }
});

app.post('/api/teams', async (req, res) => {
  try {
    const { name, description, avatar, visibility, settings } = req.body;
    const creatorId = req.query.userId || req.headers['x-user-id'];

    if (!creatorId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('🏢 Creating team:', { name, visibility, creatorId });

    // Verificar que el usuario existe y es administrador
    const creator = await prisma.user.findUnique({
      where: { id: creatorId }
    });

    if (!creator) {
      console.log('❌ Creator not found:', creatorId);
      return res.status(404).json({ error: 'Usuario no encontrado' });
    }

    if (creator.role !== 'ADMIN') {
      console.log('❌ User is not admin:', { userId: creatorId, role: creator.role });
      return res.status(403).json({ error: 'Solo los administradores pueden crear equipos' });
    }

    const team = await prisma.team.create({
      data: {
        name,
        description,
        avatar,
        visibility: visibility.toUpperCase(),
        settings: {
          create: {
            allowMembersToCreateBoards: settings?.allowMembersToCreateBoards || false,
            allowMembersToInvite: settings?.allowMembersToInvite || false,
            defaultBoardVisibility: 'TEAM',
            requireApprovalForJoining: settings?.requireApprovalForJoining || true,
          }
        },
        members: {
          create: {
            userId: creatorId,
            role: 'ADMIN',
          }
        }
      },
      include: {
        settings: true,
        members: {
          include: {
            user: true
          }
        },
        boards: true,
      }
    });

    const teamResponse = {
      id: team.id,
      name: team.name,
      description: team.description || '',
      avatar: team.avatar || '',
      visibility: team.visibility.toLowerCase(),
      members: team.members.map(member => ({
        userId: member.userId,
        role: member.role.toLowerCase(),
        joinedAt: member.joinedAt,
        invitedBy: member.invitedBy,
      })),
      boards: team.boards.map(board => board.id),
      settings: team.settings ? {
        allowMembersToCreateBoards: team.settings.allowMembersToCreateBoards,
        allowMembersToInvite: team.settings.allowMembersToInvite,
        defaultBoardVisibility: team.settings.defaultBoardVisibility.toLowerCase(),
        requireApprovalForJoining: team.settings.requireApprovalForJoining,
      } : {
        allowMembersToCreateBoards: false,
        allowMembersToInvite: false,
        defaultBoardVisibility: 'team',
        requireApprovalForJoining: true,
      },
      createdAt: team.createdAt,
      updatedAt: team.updatedAt,
    };

    console.log('✅ Team created successfully:', team.name);
    res.status(201).json(teamResponse);
  } catch (error) {
    console.error('❌ Create team error:', error);
    if (error.code === 'P2002') {
      res.status(400).json({ error: 'Team name already exists' });
    } else {
      res.status(500).json({ error: 'Error creating team' });
    }
  }
});

// Update team
app.put('/api/teams/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, avatar, visibility, settings, members } = req.body;
    const requestingUserId = req.query.userId || req.headers['x-user-id'];

    if (!requestingUserId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get requesting user to check permissions
    const requestingUser = await prisma.user.findUnique({
      where: { id: requestingUserId }
    });

    if (!requestingUser) {
      return res.status(404).json({ error: 'Requesting user not found' });
    }

    // Check if user has permission to update this team
    const isAdmin = requestingUser.role === 'ADMIN';

    // Get team to check membership
    const existingTeam = await prisma.team.findUnique({
      where: { id },
      include: {
        members: true
      }
    });

    if (!existingTeam) {
      return res.status(404).json({ error: 'Equipo no encontrado' });
    }

    const isTeamMember = existingTeam.members.some(member => member.userId === requestingUserId);

    // Only admins or team members can update teams
    if (!isAdmin && !isTeamMember) {
      console.log('❌ User lacks permission to update team:', { requestingUserId, teamId: id });
      return res.status(403).json({ error: 'No tienes permisos para modificar este equipo' });
    }

    // Only admins can change certain settings (visibility, etc.)
    if ((visibility || settings) && !isAdmin) {
      console.log('❌ Non-admin user attempted to change team settings:', { requestingUserId, role: requestingUser.role });
      return res.status(403).json({ error: 'Solo los administradores pueden cambiar la configuración del equipo' });
    }

    console.log('🏢 Updating team:', { id, name, membersCount: members?.length, requestingUserId, isAdmin });

    // Start a transaction to handle members update
    const result = await prisma.$transaction(async (tx) => {
      // Update team basic info
      const updatedTeam = await tx.team.update({
        where: { id },
        data: {
          ...(name && { name }),
          ...(description !== undefined && { description }),
          ...(avatar !== undefined && { avatar }),
          ...(visibility && { visibility: visibility.toUpperCase() }),
          ...(settings && {
            settings: {
              upsert: {
                create: {
                  allowMembersToCreateBoards: settings.allowMembersToCreateBoards || false,
                  allowMembersToInvite: settings.allowMembersToInvite || false,
                  defaultBoardVisibility: 'TEAM',
                  requireApprovalForJoining: settings.requireApprovalForJoining || true,
                },
                update: {
                  allowMembersToCreateBoards: settings.allowMembersToCreateBoards || false,
                  allowMembersToInvite: settings.allowMembersToInvite || false,
                  defaultBoardVisibility: 'TEAM',
                  requireApprovalForJoining: settings.requireApprovalForJoining || true,
                }
              }
            }
          })
        },
        include: {
          settings: true,
          members: {
            include: {
              user: true
            }
          },
          boards: true,
        }
      });

      // Update members if provided
      if (members && Array.isArray(members)) {
        // Delete all existing members
        await tx.teamMember.deleteMany({
          where: { teamId: id }
        });

        // Add new members
        if (members.length > 0) {
          await tx.teamMember.createMany({
            data: members.map(member => ({
              teamId: id,
              userId: member.userId,
              role: member.role.toUpperCase(),
              joinedAt: member.joinedAt ? new Date(member.joinedAt) : new Date(),
              invitedBy: member.invitedBy || null
            }))
          });
        }

        // Fetch updated team with new members
        return await tx.team.findUnique({
          where: { id },
          include: {
            settings: true,
            members: {
              include: {
                user: true
              }
            },
            boards: true,
          }
        });
      }

      return updatedTeam;
    });

    const teamResponse = {
      id: result.id,
      name: result.name,
      description: result.description || '',
      avatar: result.avatar || '',
      visibility: result.visibility.toLowerCase(),
      members: result.members.map(member => ({
        userId: member.userId,
        role: member.role.toLowerCase(),
        joinedAt: member.joinedAt,
        invitedBy: member.invitedBy,
      })),
      boards: result.boards.map(board => board.id),
      settings: result.settings ? {
        allowMembersToCreateBoards: result.settings.allowMembersToCreateBoards,
        allowMembersToInvite: result.settings.allowMembersToInvite,
        defaultBoardVisibility: result.settings.defaultBoardVisibility.toLowerCase(),
        requireApprovalForJoining: result.settings.requireApprovalForJoining,
      } : {
        allowMembersToCreateBoards: false,
        allowMembersToInvite: false,
        defaultBoardVisibility: 'team',
        requireApprovalForJoining: true,
      },
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    };

    console.log('✅ Team updated successfully:', result.name);
    res.json(teamResponse);
  } catch (error) {
    console.error('❌ Update team error:', error);
    res.status(500).json({ error: 'Error updating team' });
  }
});

// Delete team
app.delete('/api/teams/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🗑️ Deleting team:', id);

    // Start a transaction to handle cascading deletes
    const result = await prisma.$transaction(async (tx) => {
      // First, get the team to check if it exists
      const team = await tx.team.findUnique({
        where: { id },
        include: {
          members: true,
          boards: true,
        }
      });

      if (!team) {
        throw new Error('Team not found');
      }

      // Update all boards to remove teamId (set to null)
      if (team.boards.length > 0) {
        await tx.board.updateMany({
          where: { teamId: id },
          data: { teamId: null }
        });
        console.log(`📋 Updated ${team.boards.length} boards to remove team association`);
      }

      // Delete team members
      await tx.teamMember.deleteMany({
        where: { teamId: id }
      });
      console.log(`👥 Deleted ${team.members.length} team members`);

      // Delete team settings
      await tx.teamSettings.deleteMany({
        where: { teamId: id }
      });
      console.log('⚙️ Deleted team settings');

      // Finally, delete the team
      const deletedTeam = await tx.team.delete({
        where: { id }
      });

      return deletedTeam;
    });

    console.log('✅ Team deleted successfully:', result.name);
    res.json({ message: 'Team deleted successfully', id: result.id });
  } catch (error) {
    console.error('❌ Delete team error:', error);
    if (error.message === 'Team not found') {
      res.status(404).json({ error: 'Team not found' });
    } else {
      res.status(500).json({ error: 'Error deleting team' });
    }
  }
});

// Initialize default users
async function initializeUsers() {
  try {
    const existingUsers = await prisma.user.count();
    if (existingUsers > 0) {
      console.log('✅ Users already exist in database');
      return;
    }

    const defaultUsers = [
      {
        username: 'admin',
        name: 'Administrador',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'admin123',
        role: 'ADMIN',
      },
      {
        username: 'ana.garcia',
        name: 'Ana García',
        email: '<EMAIL>',
        phone: '+1234567891',
        password: 'ana123',
        role: 'MEMBER',
      },
      {
        username: 'carlos.lopez',
        name: 'Carlos López',
        email: '<EMAIL>',
        phone: '+1234567892',
        password: 'carlos123',
        role: 'OBSERVER',
      }
    ];

    for (const userData of defaultUsers) {
      await prisma.user.create({
        data: {
          ...userData,
          avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.name)}&background=3b82f6&color=fff&size=128`,
          permissions: {
            create: {
              canCreateBoards: userData.role === 'ADMIN',
              canEditCards: true,
              canDeleteCards: userData.role === 'ADMIN',
              canManageUsers: userData.role === 'ADMIN',
              canViewReports: true,
              canManageSettings: userData.role === 'ADMIN',
              canInviteUsers: userData.role === 'ADMIN',
              canArchiveCards: true,
            }
          }
        }
      });
      console.log(`✅ User created: ${userData.name} (${userData.email})`);
    }

    console.log('🎉 Default users created successfully');
  } catch (error) {
    console.error('❌ Error initializing users:', error);
  }
}

// Function to create default board if none exist
async function createDefaultBoardIfNeeded(userId) {
  try {
    const boardCount = await prisma.board.count();

    if (boardCount === 0) {
      console.log('📋 No boards found, creating default board...');
      const defaultBoard = await prisma.board.create({
        data: {
          title: 'Mi Primer Tablero',
          description: 'Tablero de ejemplo para comenzar',
          background: 'bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500',
          isPublic: false,
          isFavorite: false,
          visibility: 'PRIVATE',
          createdById: userId,
          members: {
            create: {
              userId: userId,
              role: 'ADMIN',
            }
          },
          lists: {
            create: [
              {
                title: 'Por Hacer',
                position: 1,
                cards: {
                  create: [
                    {
                      title: '¡Bienvenido a SapiaFlow!',
                      description: 'Esta es tu primera tarjeta. Haz clic para editarla.',
                      position: 1,
                    }
                  ]
                }
              },
              {
                title: 'En Progreso',
                position: 2,
              },
              {
                title: 'Completado',
                position: 3,
              }
            ]
          }
        },
        include: {
          members: {
            include: {
              user: true
            }
          },
          lists: {
            include: {
              cards: true
            }
          },
          labels: true,
        }
      });

      console.log('✅ Default board created:', defaultBoard.title);
      return defaultBoard;
    }

    return null;
  } catch (error) {
    console.error('❌ Error creating default board:', error);
    return null;
  }
}

// Boards endpoints
app.get('/api/boards', async (req, res) => {
  try {
    // Get user ID from query params or headers (you might want to get this from auth token)
    const userId = req.query.userId || req.headers['x-user-id'];

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get user to check role
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    console.log('📋 Getting boards for user:', { userId, role: user.role });

    // Create default board if none exist and we have a user ID
    await createDefaultBoardIfNeeded(userId);

    let boards;

    // ADMIN users can see all boards
    if (user.role === 'ADMIN') {
      boards = await prisma.board.findMany({
        include: {
          members: {
            include: {
              user: true
            }
          },
          lists: {
            include: {
              cards: {
                include: {
                  labels: {
                    include: {
                      label: true
                    }
                  },
                  assignedMembers: {
                    include: {
                      user: true
                    }
                  },
                  checklist: true,
                  comments: {
                    include: {
                      author: true
                    }
                  },
                  attachments: true,
                }
              }
            }
          },
          labels: true,
          team: true,
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
    } else {
      // MEMBER and OBSERVER users can only see boards where they are members
      boards = await prisma.board.findMany({
        where: {
          members: {
            some: {
              userId: userId
            }
          }
        },
        include: {
          members: {
            include: {
              user: true
            }
          },
          lists: {
            include: {
              cards: {
                include: {
                  labels: {
                    include: {
                      label: true
                    }
                  },
                  assignedMembers: {
                    include: {
                      user: true
                    }
                  },
                  checklist: true,
                  comments: {
                    include: {
                      author: true
                    }
                  },
                  attachments: true,
                }
              }
            }
          },
          labels: true,
          team: true,
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
    }

    console.log(`✅ Found ${boards.length} boards for user ${user.role}:`, user.name);

    const boardsResponse = boards.map(board => ({
      id: board.id,
      title: board.title,
      description: board.description || '',
      background: board.background,
      isPublic: board.isPublic,
      isFavorite: board.isFavorite,
      visibility: board.visibility.toLowerCase(),
      teamId: board.teamId,
      members: board.members.map(member => ({
        id: member.user.id,
        name: member.user.name,
        email: member.user.email,
        username: member.user.username,
        avatar: member.user.avatar,
        role: member.role.toLowerCase(),
        joinedAt: member.joinedAt,
        invitedBy: member.invitedBy,
      })),
      lists: board.lists.map(list => ({
        id: list.id,
        title: list.title,
        position: list.position,
        archived: list.archived,
        cards: list.cards.map(card => ({
          id: card.id,
          title: card.title,
          description: card.description || '',
          position: card.position,
          archived: card.archived,
          cover: card.cover,
          dueDate: card.dueDate,
          labels: card.labels.map(cl => ({
            id: cl.label.id,
            name: cl.label.name,
            color: cl.label.color,
          })),
          assignedMembers: card.assignedMembers.map(am => ({
            id: am.user.id,
            name: am.user.name,
            email: am.user.email,
            avatar: am.user.avatar
          })),
          checklist: card.checklist.map(item => ({
            id: item.id,
            text: item.text,
            completed: item.completed,
          })),
          comments: card.comments.map(comment => ({
            id: comment.id,
            text: comment.text,
            author: {
              id: comment.author.id,
              name: comment.author.name,
              avatar: comment.author.avatar,
            },
            createdAt: comment.createdAt,
          })),
          attachments: card.attachments.map(attachment => ({
            id: attachment.id,
            name: attachment.name,
            url: attachment.url,
            type: attachment.type.toLowerCase(),
            size: attachment.size,
          })),
          createdAt: card.createdAt,
          updatedAt: card.updatedAt,
        }))
      })),
      labels: board.labels.map(label => ({
        id: label.id,
        name: label.name,
        color: label.color,
      })),
      createdAt: board.createdAt,
      updatedAt: board.updatedAt,
    }));

    res.json(boardsResponse);
  } catch (error) {
    console.error('❌ Get boards error:', error);
    res.status(500).json({ error: 'Error fetching boards' });
  }
});

app.post('/api/boards', async (req, res) => {
  try {
    const { title, description, background, isPublic, isFavorite, visibility, teamId, creatorId } = req.body;
    const requestingUserId = req.query.userId || req.headers['x-user-id'] || creatorId;

    if (!requestingUserId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('📋 Creating board:', { title, visibility, creatorId, teamId, requestingUserId });

    // CRITICAL: Verify user exists and has permission to create boards
    const requestingUser = await prisma.user.findUnique({
      where: { id: requestingUserId }
    });

    if (!requestingUser) {
      return res.status(404).json({ error: 'Usuario no encontrado' });
    }

    // CRITICAL: Only ADMIN users can create boards
    if (requestingUser.role !== 'ADMIN') {
      console.log('❌ Non-admin user attempted to create board:', { userId: requestingUserId, role: requestingUser.role });
      return res.status(403).json({ error: 'Solo los administradores pueden crear tableros' });
    }

    console.log('✅ Admin user creating board:', { userId: requestingUserId, role: requestingUser.role });

    // Si se proporciona teamId, verificar que el equipo existe
    if (teamId) {
      const team = await prisma.team.findUnique({
        where: { id: teamId }
      });

      if (!team) {
        console.log('❌ Team not found:', teamId);
        return res.status(404).json({ error: 'Equipo no encontrado' });
      }
    }

    const board = await prisma.board.create({
      data: {
        title,
        description,
        background,
        isPublic,
        isFavorite,
        visibility: visibility?.toUpperCase() || 'PRIVATE',
        teamId: teamId || null, // Asegurar que sea null si no se proporciona
        createdById: creatorId,
        members: {
          create: {
            userId: creatorId,
            role: 'ADMIN',
          }
        }
      },
      include: {
        members: {
          include: {
            user: true
          }
        },
        lists: true,
        labels: true,
        team: true,
      }
    });

    const boardResponse = {
      id: board.id,
      title: board.title,
      description: board.description || '',
      background: board.background,
      isPublic: board.isPublic,
      isFavorite: board.isFavorite,
      visibility: board.visibility.toLowerCase(),
      teamId: board.teamId,
      members: board.members.map(member => ({
        id: member.user.id,
        name: member.user.name,
        email: member.user.email,
        username: member.user.username,
        avatar: member.user.avatar,
        role: member.role.toLowerCase(),
        joinedAt: member.joinedAt,
        invitedBy: member.invitedBy,
      })),
      lists: [],
      labels: [],
      createdAt: board.createdAt,
      updatedAt: board.updatedAt,
    };

    console.log('✅ Board created successfully:', board.title);
    res.status(201).json(boardResponse);
  } catch (error) {
    console.error('❌ Create board error:', error);
    res.status(500).json({ error: 'Error creating board' });
  }
});

// Update board
app.put('/api/boards/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, background, isPublic, isFavorite, visibility, teamId, lists } = req.body;
    const userId = req.query.userId || req.headers['x-user-id'];

    console.log('🔄 Updating board:', { id, title, teamId, userId });

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get user to check permissions
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // First check if board exists
    const existingBoard = await prisma.board.findUnique({
      where: { id },
      include: {
        members: true,
        lists: {
          include: {
            cards: true
          }
        }
      }
    });

    if (!existingBoard) {
      console.log('❌ Board not found:', id);
      return res.status(404).json({ error: 'Board not found' });
    }

    // Check if user has permission to update this board
    const isAdmin = user.role === 'ADMIN';
    const isBoardMember = existingBoard.members.some(member => member.userId === userId);

    if (!isAdmin && !isBoardMember) {
      console.log('❌ User lacks permission to update board:', { userId, boardId: id });
      return res.status(403).json({ error: 'No tienes permisos para modificar este tablero' });
    }

    // CRITICAL: Only ADMIN users can modify team assignments
    if (teamId !== undefined && teamId !== existingBoard.teamId) {
      if (!isAdmin) {
        console.log('❌ Non-admin user attempted to modify team assignment:', { userId, role: user.role });
        return res.status(403).json({ error: 'Solo los administradores pueden modificar la asignación de equipos' });
      }

      // Validate that the team exists if teamId is provided
      if (teamId) {
        const team = await prisma.team.findUnique({
          where: { id: teamId }
        });

        if (!team) {
          return res.status(404).json({ error: 'Equipo no encontrado' });
        }
      }
    }

    // Update board basic info
    const updatedBoard = await prisma.board.update({
      where: { id },
      data: {
        ...(title && { title }),
        ...(description && { description }),
        ...(background && { background }),
        ...(isPublic !== undefined && { isPublic }),
        ...(isFavorite !== undefined && { isFavorite }),
        ...(visibility && { visibility: visibility.toUpperCase() }),
        ...(teamId && { teamId }),
      },
      include: {
        members: {
          include: {
            user: true
          }
        },
        lists: {
          include: {
            cards: {
              include: {
                labels: {
                  include: {
                    label: true
                  }
                },
                assignedMembers: {
                  include: {
                    user: true
                  }
                },
                checklist: true,
                comments: {
                  include: {
                    author: true
                  }
                },
                attachments: true,
              }
            }
          }
        },
        labels: true,
        team: true,
      }
    });

    // Transform to frontend format
    const boardResponse = {
      id: updatedBoard.id,
      title: updatedBoard.title,
      description: updatedBoard.description,
      background: updatedBoard.background,
      isPublic: updatedBoard.isPublic,
      isFavorite: updatedBoard.isFavorite,
      visibility: updatedBoard.visibility.toLowerCase(),
      teamId: updatedBoard.teamId,
      createdAt: updatedBoard.createdAt,
      updatedAt: updatedBoard.updatedAt,
      members: updatedBoard.members.map(member => ({
        id: member.user.id,
        name: member.user.name,
        email: member.user.email,
        avatar: member.user.avatar,
        role: member.role.toLowerCase(),
        joinedAt: member.createdAt
      })),
      lists: updatedBoard.lists.map(list => ({
        id: list.id,
        title: list.title,
        position: list.position,
        cards: list.cards.map(card => ({
          id: card.id,
          title: card.title,
          description: card.description,
          position: card.position,
          dueDate: card.dueDate,
          createdAt: card.createdAt,
          updatedAt: card.updatedAt,
          labels: card.labels.map(cl => ({
            id: cl.label.id,
            name: cl.label.name,
            color: cl.label.color
          })),
          assignedMembers: card.assignedMembers.map(am => ({
            id: am.user.id,
            name: am.user.name,
            email: am.user.email,
            avatar: am.user.avatar
          })),
          checklist: card.checklist.map(item => ({
            id: item.id,
            text: item.text,
            completed: item.completed
          })),
          comments: card.comments.map(comment => ({
            id: comment.id,
            text: comment.text,
            createdAt: comment.createdAt,
            author: {
              id: comment.author.id,
              name: comment.author.name,
              avatar: comment.author.avatar
            }
          })),
          attachments: card.attachments.map(att => ({
            id: att.id,
            name: att.name,
            url: att.url,
            type: att.type,
            size: att.size
          }))
        }))
      })),
      labels: updatedBoard.labels.map(label => ({
        id: label.id,
        name: label.name,
        color: label.color
      }))
    };

    console.log('✅ Board updated successfully:', updatedBoard.title);
    res.json(boardResponse);
  } catch (error) {
    console.error('❌ Update board error:', error);
    res.status(500).json({ error: 'Error updating board' });
  }
});

// Add member to board
app.post('/api/boards/:boardId/members', async (req, res) => {
  try {
    const { boardId } = req.params;
    const { userId, role = 'MEMBER' } = req.body;

    console.log('👥 Adding member to board:', { boardId, userId, role });

    // Check if board exists
    const board = await prisma.board.findUnique({
      where: { id: boardId }
    });

    if (!board) {
      return res.status(404).json({ error: 'Board not found' });
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if user is already a member
    const existingMember = await prisma.boardMember.findUnique({
      where: {
        boardId_userId: {
          boardId,
          userId
        }
      }
    });

    if (existingMember) {
      return res.status(400).json({ error: 'User is already a member of this board' });
    }

    // Add the member
    await prisma.boardMember.create({
      data: {
        boardId,
        userId,
        role: role.toUpperCase()
      }
    });

    // Return updated board with members
    const updatedBoard = await prisma.board.findUnique({
      where: { id: boardId },
      include: {
        members: {
          include: {
            user: true
          }
        },
        lists: {
          include: {
            cards: {
              include: {
                checklist: true,
                comments: {
                  include: {
                    author: true
                  }
                },
                assignedMembers: {
                  include: {
                    user: true
                  }
                },
                labels: {
                  include: {
                    label: true
                  }
                },
                attachments: true
              }
            }
          }
        },
        labels: true
      }
    });

    // Transform to frontend format
    const boardResponse = {
      id: updatedBoard.id,
      title: updatedBoard.title,
      description: updatedBoard.description || '',
      background: updatedBoard.background,
      isPublic: updatedBoard.isPublic,
      isFavorite: updatedBoard.isFavorite,
      visibility: updatedBoard.visibility.toLowerCase(),
      teamId: updatedBoard.teamId,
      members: updatedBoard.members.map(member => ({
        id: member.user.id,
        name: member.user.name,
        email: member.user.email,
        username: member.user.username,
        avatar: member.user.avatar,
        role: member.role.toLowerCase(),
        joinedAt: member.joinedAt,
        invitedBy: member.invitedBy,
      })),
      lists: updatedBoard.lists.map(list => ({
        id: list.id,
        title: list.title,
        position: list.position,
        archived: list.archived,
        cards: list.cards.map(card => ({
          id: card.id,
          title: card.title,
          description: card.description || '',
          position: card.position,
          archived: card.archived,
          cover: card.cover,
          dueDate: card.dueDate,
          labels: (card.labels || []).map(cl => ({
            id: cl.label.id,
            name: cl.label.name,
            color: cl.label.color,
          })),
          assignedMembers: (card.assignedMembers || []).map(am => ({
            id: am.user.id,
            name: am.user.name,
            email: am.user.email,
            avatar: am.user.avatar
          })),
          checklist: (card.checklist || []).map(item => ({
            id: item.id,
            text: item.text,
            completed: item.completed,
          })),
          comments: (card.comments || []).map(comment => ({
            id: comment.id,
            text: comment.text,
            createdAt: comment.createdAt,
            author: {
              id: comment.author.id,
              name: comment.author.name,
              avatar: comment.author.avatar,
            }
          })),
          attachments: (card.attachments || []).map(attachment => ({
            id: attachment.id,
            name: attachment.name,
            url: attachment.url,
            type: attachment.type.toLowerCase(),
            size: attachment.size,
          })),
          createdAt: card.createdAt,
          updatedAt: card.updatedAt,
        }))
      })),
      labels: updatedBoard.labels.map(label => ({
        id: label.id,
        name: label.name,
        color: label.color,
      })),
      createdAt: updatedBoard.createdAt,
      updatedAt: updatedBoard.updatedAt,
    };

    console.log('✅ Member added to board successfully');
    res.json(boardResponse);
  } catch (error) {
    console.error('❌ Add member to board error:', error);
    res.status(500).json({ error: 'Error adding member to board' });
  }
});

// Remove member from board
app.delete('/api/boards/:boardId/members/:userId', async (req, res) => {
  try {
    const { boardId, userId } = req.params;

    console.log('👥 Removing member from board:', { boardId, userId });

    // Check if member exists
    const existingMember = await prisma.boardMember.findUnique({
      where: {
        boardId_userId: {
          boardId,
          userId
        }
      }
    });

    if (!existingMember) {
      return res.status(404).json({ error: 'Member not found in this board' });
    }

    // Remove the member
    await prisma.boardMember.delete({
      where: {
        boardId_userId: {
          boardId,
          userId
        }
      }
    });

    // Return updated board with members
    const updatedBoard = await prisma.board.findUnique({
      where: { id: boardId },
      include: {
        members: {
          include: {
            user: true
          }
        },
        lists: {
          include: {
            cards: {
              include: {
                checklist: true,
                comments: {
                  include: {
                    author: true
                  }
                },
                assignedMembers: {
                  include: {
                    user: true
                  }
                },
                labels: {
                  include: {
                    label: true
                  }
                },
                attachments: true
              }
            }
          }
        },
        labels: true
      }
    });

    // Transform to frontend format
    const boardResponse = {
      id: updatedBoard.id,
      title: updatedBoard.title,
      description: updatedBoard.description || '',
      background: updatedBoard.background,
      isPublic: updatedBoard.isPublic,
      isFavorite: updatedBoard.isFavorite,
      visibility: updatedBoard.visibility.toLowerCase(),
      teamId: updatedBoard.teamId,
      members: updatedBoard.members.map(member => ({
        id: member.user.id,
        name: member.user.name,
        email: member.user.email,
        username: member.user.username,
        avatar: member.user.avatar,
        role: member.role.toLowerCase(),
        joinedAt: member.joinedAt,
        invitedBy: member.invitedBy,
      })),
      lists: updatedBoard.lists.map(list => ({
        id: list.id,
        title: list.title,
        position: list.position,
        archived: list.archived,
        cards: list.cards.map(card => ({
          id: card.id,
          title: card.title,
          description: card.description || '',
          position: card.position,
          archived: card.archived,
          cover: card.cover,
          dueDate: card.dueDate,
          labels: (card.labels || []).map(cl => ({
            id: cl.label.id,
            name: cl.label.name,
            color: cl.label.color,
          })),
          assignedMembers: (card.assignedMembers || []).map(am => ({
            id: am.user.id,
            name: am.user.name,
            email: am.user.email,
            avatar: am.user.avatar
          })),
          checklist: (card.checklist || []).map(item => ({
            id: item.id,
            text: item.text,
            completed: item.completed,
          })),
          comments: (card.comments || []).map(comment => ({
            id: comment.id,
            text: comment.text,
            createdAt: comment.createdAt,
            author: {
              id: comment.author.id,
              name: comment.author.name,
              avatar: comment.author.avatar,
            }
          })),
          attachments: (card.attachments || []).map(attachment => ({
            id: attachment.id,
            name: attachment.name,
            url: attachment.url,
            type: attachment.type.toLowerCase(),
            size: attachment.size,
          })),
          createdAt: card.createdAt,
          updatedAt: card.updatedAt,
        }))
      })),
      labels: updatedBoard.labels.map(label => ({
        id: label.id,
        name: label.name,
        color: label.color,
      })),
      createdAt: updatedBoard.createdAt,
      updatedAt: updatedBoard.updatedAt,
    };

    console.log('✅ Member removed from board successfully');
    res.json(boardResponse);
  } catch (error) {
    console.error('❌ Remove member from board error:', error);
    res.status(500).json({ error: 'Error removing member from board' });
  }
});

// Create label for board
app.post('/api/boards/:boardId/labels', async (req, res) => {
  try {
    const { boardId } = req.params;
    const { name, color } = req.body;

    console.log('🏷️ Creating label for board:', { boardId, name, color });

    // Check if board exists
    const board = await prisma.board.findUnique({
      where: { id: boardId }
    });

    if (!board) {
      return res.status(404).json({ error: 'Board not found' });
    }

    // Create the label
    const newLabel = await prisma.label.create({
      data: {
        name,
        color,
        boardId
      }
    });

    console.log('✅ Label created successfully:', newLabel.name);
    res.json({
      id: newLabel.id,
      name: newLabel.name,
      color: newLabel.color
    });
  } catch (error) {
    console.error('❌ Create label error:', error);
    res.status(500).json({ error: 'Error creating label' });
  }
});

// Delete board
app.delete('/api/boards/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🗑️ Deleting board:', id);

    // Start a transaction to handle cascading deletes
    const result = await prisma.$transaction(async (tx) => {
      // First, get the board to check if it exists and get related data
      const board = await tx.board.findUnique({
        where: { id },
        include: {
          lists: {
            include: {
              cards: {
                include: {
                  checklist: true,
                  comments: true,
                  assignedMembers: true,
                  attachments: true
                }
              }
            }
          },
          members: true,
          labels: true,
          activities: true
        }
      });

      if (!board) {
        throw new Error('Board not found');
      }

      // Delete all card-related data first
      for (const list of board.lists) {
        for (const card of list.cards) {
          // Delete card attachments
          await tx.attachment.deleteMany({
            where: { cardId: card.id }
          });

          // Delete card assigned members
          await tx.cardAssignment.deleteMany({
            where: { cardId: card.id }
          });

          // Delete card comments
          await tx.comment.deleteMany({
            where: { cardId: card.id }
          });

          // Delete checklist items
          await tx.checklistItem.deleteMany({
            where: { cardId: card.id }
          });

          // Delete card labels
          await tx.cardLabel.deleteMany({
            where: { cardId: card.id }
          });

          // Delete the card
          await tx.card.delete({
            where: { id: card.id }
          });
        }

        // Delete the list
        await tx.list.delete({
          where: { id: list.id }
        });
      }

      // Delete board members
      await tx.boardMember.deleteMany({
        where: { boardId: id }
      });

      // Delete board labels
      await tx.label.deleteMany({
        where: { boardId: id }
      });

      // Delete board activities
      await tx.activity.deleteMany({
        where: { boardId: id }
      });

      // Finally, delete the board
      const deletedBoard = await tx.board.delete({
        where: { id }
      });

      return deletedBoard;
    });

    console.log('✅ Board deleted successfully:', result.title);
    res.status(204).send();
  } catch (error) {
    console.error('❌ Delete board error:', error);
    if (error.message === 'Board not found') {
      res.status(404).json({ error: 'Board not found' });
    } else {
      res.status(500).json({ error: 'Error deleting board' });
    }
  }
});

// Card endpoints
app.post('/api/boards/:boardId/lists/:listId/cards', async (req, res) => {
  try {
    const { boardId, listId } = req.params;
    const { title, description, position } = req.body;

    console.log('📝 Adding card to list:', { boardId, listId, title });

    // Check if board and list exist
    const board = await prisma.board.findUnique({
      where: { id: boardId },
      include: {
        lists: {
          where: { id: listId }
        }
      }
    });

    if (!board || board.lists.length === 0) {
      return res.status(404).json({ error: 'Board or list not found' });
    }

    // Get the next position for the card
    const lastCard = await prisma.card.findFirst({
      where: { listId },
      orderBy: { position: 'desc' }
    });
    const nextPosition = lastCard ? lastCard.position + 1 : 1;

    // Create the card
    const card = await prisma.card.create({
      data: {
        title,
        description: description || '',
        position: position || nextPosition,
        listId,
      },
      include: {
        labels: {
          include: {
            label: true
          }
        },
        assignedMembers: {
          include: {
            user: true
          }
        },
        checklist: true,
        comments: {
          include: {
            author: true
          }
        },
        attachments: true,
      }
    });

    // Transform to frontend format
    const cardResponse = {
      id: card.id,
      title: card.title,
      description: card.description,
      position: card.position,
      dueDate: card.dueDate,
      createdAt: card.createdAt,
      updatedAt: card.updatedAt,
      labels: card.labels.map(cl => ({
        id: cl.label.id,
        name: cl.label.name,
        color: cl.label.color
      })),
      assignedMembers: card.assignedMembers.map(am => ({
        id: am.user.id,
        name: am.user.name,
        email: am.user.email,
        avatar: am.user.avatar
      })),
      checklist: card.checklist.map(item => ({
        id: item.id,
        text: item.text,
        completed: item.completed
      })),
      comments: card.comments.map(comment => ({
        id: comment.id,
        text: comment.text,
        createdAt: comment.createdAt,
        author: {
          id: comment.author.id,
          name: comment.author.name,
          avatar: comment.author.avatar
        }
      })),
      attachments: card.attachments.map(att => ({
        id: att.id,
        name: att.name,
        url: att.url,
        type: att.type,
        size: att.size
      }))
    };

    console.log('✅ Card created successfully:', card.title);
    res.status(201).json(cardResponse);
  } catch (error) {
    console.error('❌ Create card error:', error);
    res.status(500).json({ error: 'Error creating card' });
  }
});

// List endpoints
app.post('/api/boards/:boardId/lists', async (req, res) => {
  try {
    const { boardId } = req.params;
    const { title, position } = req.body;

    console.log('📋 Adding list to board:', { boardId, title });

    // Check if board exists
    const board = await prisma.board.findUnique({
      where: { id: boardId }
    });

    if (!board) {
      return res.status(404).json({ error: 'Board not found' });
    }

    // Get the next position for the list
    const lastList = await prisma.list.findFirst({
      where: { boardId },
      orderBy: { position: 'desc' }
    });
    const nextPosition = lastList ? lastList.position + 1 : 1;

    // Create the list
    const list = await prisma.list.create({
      data: {
        title,
        position: position || nextPosition,
        boardId,
      },
      include: {
        cards: {
          include: {
            labels: {
              include: {
                label: true
              }
            },
            assignedMembers: {
              include: {
                user: true
              }
            },
            checklist: true,
            comments: {
              include: {
                author: true
              }
            },
            attachments: true,
          }
        }
      }
    });

    // Transform to frontend format
    const listResponse = {
      id: list.id,
      title: list.title,
      position: list.position,
      cards: list.cards.map(card => ({
        id: card.id,
        title: card.title,
        description: card.description,
        position: card.position,
        dueDate: card.dueDate,
        createdAt: card.createdAt,
        updatedAt: card.updatedAt,
        labels: card.labels.map(cl => ({
          id: cl.label.id,
          name: cl.label.name,
          color: cl.label.color
        })),
        assignedMembers: card.assignedMembers.map(am => ({
          id: am.user.id,
          name: am.user.name,
          email: am.user.email,
          avatar: am.user.avatar
        })),
        checklist: card.checklist.map(item => ({
          id: item.id,
          text: item.text,
          completed: item.completed
        })),
        comments: card.comments.map(comment => ({
          id: comment.id,
          text: comment.text,
          createdAt: comment.createdAt,
          author: {
            id: comment.author.id,
            name: comment.author.name,
            avatar: comment.author.avatar
          }
        })),
        attachments: card.attachments.map(att => ({
          id: att.id,
          name: att.name,
          url: att.url,
          type: att.type,
          size: att.size
        }))
      }))
    };

    console.log('✅ List created successfully:', list.title);
    res.status(201).json(listResponse);
  } catch (error) {
    console.error('❌ Create list error:', error);
    res.status(500).json({ error: 'Error creating list' });
  }
});

// Update list
app.put('/api/boards/:boardId/lists/:listId', async (req, res) => {
  try {
    const { boardId, listId } = req.params;
    const { title, position } = req.body;

    console.log('🔄 Updating list:', { boardId, listId, title });

    // Check if board exists
    const board = await prisma.board.findUnique({
      where: { id: boardId }
    });

    if (!board) {
      return res.status(404).json({ error: 'Board not found' });
    }

    // Check if list exists
    const existingList = await prisma.list.findUnique({
      where: { id: listId }
    });

    if (!existingList) {
      return res.status(404).json({ error: 'List not found' });
    }

    // Update the list
    const updatedList = await prisma.list.update({
      where: { id: listId },
      data: {
        title: title || existingList.title,
        position: position !== undefined ? position : existingList.position
      },
      include: {
        cards: {
          include: {
            labels: {
              include: {
                label: true
              }
            },
            assignedMembers: {
              include: {
                user: true
              }
            },
            checklist: true,
            comments: {
              include: {
                author: true
              }
            },
            attachments: true,
          }
        }
      }
    });

    // Transform to frontend format
    const listResponse = {
      id: updatedList.id,
      title: updatedList.title,
      position: updatedList.position,
      updatedAt: updatedList.updatedAt,
      cards: updatedList.cards.map(card => ({
        id: card.id,
        title: card.title,
        description: card.description,
        position: card.position,
        dueDate: card.dueDate,
        createdAt: card.createdAt,
        updatedAt: card.updatedAt,
        labels: card.labels.map(cl => ({
          id: cl.label.id,
          name: cl.label.name,
          color: cl.label.color
        })),
        assignedMembers: card.assignedMembers.map(am => ({
          id: am.user.id,
          name: am.user.name,
          email: am.user.email,
          avatar: am.user.avatar
        })),
        checklist: card.checklist.map(item => ({
          id: item.id,
          text: item.text,
          completed: item.completed
        })),
        comments: card.comments.map(comment => ({
          id: comment.id,
          text: comment.text,
          createdAt: comment.createdAt,
          author: {
            id: comment.author.id,
            name: comment.author.name,
            avatar: comment.author.avatar
          }
        })),
        attachments: card.attachments.map(att => ({
          id: att.id,
          name: att.name,
          url: att.url,
          type: att.type,
          size: att.size
        }))
      }))
    };

    console.log('✅ List updated successfully:', updatedList.title);
    res.json(listResponse);
  } catch (error) {
    console.error('❌ Update list error:', error);
    res.status(500).json({ error: 'Error updating list' });
  }
});

// Delete list
app.delete('/api/boards/:boardId/lists/:listId', async (req, res) => {
  try {
    const { boardId, listId } = req.params;

    console.log('🗑️ Deleting list:', { boardId, listId });

    // Check if board exists
    const board = await prisma.board.findUnique({
      where: { id: boardId }
    });

    if (!board) {
      return res.status(404).json({ error: 'Board not found' });
    }

    // Check if list exists
    const existingList = await prisma.list.findUnique({
      where: { id: listId },
      include: {
        cards: true
      }
    });

    if (!existingList) {
      return res.status(404).json({ error: 'List not found' });
    }

    // Delete all cards in the list first (cascade delete)
    // This will also delete related data like checklist items, comments, etc.
    await prisma.card.deleteMany({
      where: { listId }
    });

    // Delete the list
    await prisma.list.delete({
      where: { id: listId }
    });

    console.log('✅ List deleted successfully:', existingList.title);
    res.json({
      message: 'List deleted successfully',
      deletedList: {
        id: existingList.id,
        title: existingList.title,
        cardsDeleted: existingList.cards.length
      }
    });
  } catch (error) {
    console.error('❌ Delete list error:', error);
    res.status(500).json({ error: 'Error deleting list' });
  }
});

// Update member role in board
app.put('/api/boards/:boardId/members/:userId/role', async (req, res) => {
  try {
    const { boardId, userId } = req.params;
    const { role } = req.body;

    console.log('👥 Updating member role:', { boardId, userId, role });

    // Check if board exists
    const board = await prisma.board.findUnique({
      where: { id: boardId }
    });

    if (!board) {
      return res.status(404).json({ error: 'Board not found' });
    }

    // Check if member exists
    const existingMember = await prisma.boardMember.findUnique({
      where: {
        boardId_userId: {
          boardId,
          userId
        }
      }
    });

    if (!existingMember) {
      return res.status(404).json({ error: 'Member not found in this board' });
    }

    // Update the member role
    await prisma.boardMember.update({
      where: {
        boardId_userId: {
          boardId,
          userId
        }
      },
      data: {
        role: role.toUpperCase()
      }
    });

    // Return updated board with members
    const updatedBoard = await prisma.board.findUnique({
      where: { id: boardId },
      include: {
        members: {
          include: {
            user: true
          }
        },
        lists: {
          include: {
            cards: {
              include: {
                labels: {
                  include: {
                    label: true
                  }
                },
                assignedMembers: {
                  include: {
                    user: true
                  }
                },
                checklist: true,
                comments: {
                  include: {
                    author: true
                  }
                },
                attachments: true,
              }
            }
          }
        },
        labels: true,
        team: true,
      }
    });

    // Transform to frontend format
    const boardResponse = {
      id: updatedBoard.id,
      title: updatedBoard.title,
      description: updatedBoard.description,
      background: updatedBoard.background,
      isPublic: updatedBoard.isPublic,
      isFavorite: updatedBoard.isFavorite,
      visibility: updatedBoard.visibility.toLowerCase(),
      teamId: updatedBoard.teamId,
      createdAt: updatedBoard.createdAt,
      updatedAt: updatedBoard.updatedAt,
      members: updatedBoard.members.map(member => ({
        id: member.user.id,
        name: member.user.name,
        email: member.user.email,
        avatar: member.user.avatar,
        role: member.role.toLowerCase(),
        joinedAt: member.createdAt
      })),
      lists: updatedBoard.lists.map(list => ({
        id: list.id,
        title: list.title,
        position: list.position,
        cards: list.cards.map(card => ({
          id: card.id,
          title: card.title,
          description: card.description,
          position: card.position,
          dueDate: card.dueDate,
          createdAt: card.createdAt,
          updatedAt: card.updatedAt,
          labels: card.labels.map(cl => ({
            id: cl.label.id,
            name: cl.label.name,
            color: cl.label.color
          })),
          assignedMembers: card.assignedMembers.map(am => ({
            id: am.user.id,
            name: am.user.name,
            email: am.user.email,
            avatar: am.user.avatar
          })),
          checklist: card.checklist.map(item => ({
            id: item.id,
            text: item.text,
            completed: item.completed
          })),
          comments: card.comments.map(comment => ({
            id: comment.id,
            text: comment.text,
            createdAt: comment.createdAt,
            author: {
              id: comment.author.id,
              name: comment.author.name,
              avatar: comment.author.avatar
            }
          })),
          attachments: card.attachments.map(att => ({
            id: att.id,
            name: att.name,
            url: att.url,
            type: att.type,
            size: att.size
          }))
        }))
      })),
      labels: updatedBoard.labels.map(label => ({
        id: label.id,
        name: label.name,
        color: label.color
      }))
    };

    console.log('✅ Member role updated successfully:', { userId, newRole: role });
    res.json(boardResponse);
  } catch (error) {
    console.error('❌ Update member role error:', error);
    res.status(500).json({ error: 'Error updating member role' });
  }
});

// Update card
app.put('/api/boards/:boardId/lists/:listId/cards/:cardId', async (req, res) => {
  try {
    const { boardId, listId, cardId } = req.params;
    const { title, description, dueDate, position, cover, labels, checklist, comments, assignedMembers, attachments } = req.body;

    console.log('🔄 Updating card:', { boardId, listId, cardId, title, hasChecklist: !!checklist, hasComments: !!comments, hasLabels: !!labels });

    // Check if card exists
    const existingCard = await prisma.card.findUnique({
      where: { id: cardId },
      include: {
        list: {
          include: {
            board: true
          }
        }
      }
    });

    if (!existingCard || existingCard.list.boardId !== boardId || existingCard.listId !== listId) {
      console.log('❌ Card not found or doesn\'t belong to specified board/list:', cardId);
      return res.status(404).json({ error: 'Card not found' });
    }

    // Handle checklist updates
    if (checklist !== undefined) {
      // Delete existing checklist items
      await prisma.checklistItem.deleteMany({
        where: { cardId }
      });

      // Create new checklist items
      if (checklist && checklist.length > 0) {
        await prisma.checklistItem.createMany({
          data: checklist.map((item, index) => ({
            id: item.id || `${cardId}_checklist_${index}`,
            text: item.text,
            completed: item.completed || false,
            cardId,
          }))
        });
      }
    }

    // Handle comments updates
    if (comments !== undefined) {
      // Delete existing comments
      await prisma.comment.deleteMany({
        where: { cardId }
      });

      // Create new comments
      if (comments && comments.length > 0) {
        await prisma.comment.createMany({
          data: comments.map((comment, index) => ({
            id: comment.id || `${cardId}_comment_${index}`,
            text: comment.text,
            authorId: comment.author?.id || comment.authorId,
            cardId,
            createdAt: comment.createdAt ? new Date(comment.createdAt) : new Date(),
          }))
        });
      }
    }

    // Handle labels updates
    if (labels !== undefined) {
      // Delete existing label assignments
      await prisma.cardLabel.deleteMany({
        where: { cardId }
      });

      // Create new label assignments
      if (labels && labels.length > 0) {
        await prisma.cardLabel.createMany({
          data: labels.map((label) => ({
            cardId,
            labelId: label.id,
          }))
        });
      }
    }

    // Handle assigned members updates
    if (assignedMembers !== undefined) {
      // Delete existing assignments
      await prisma.cardAssignment.deleteMany({
        where: { cardId }
      });

      // Create new assignments
      if (assignedMembers && assignedMembers.length > 0) {
        await prisma.cardAssignment.createMany({
          data: assignedMembers.map((member) => ({
            cardId,
            userId: member.id,
          }))
        });
      }
    }

    // Update card basic fields
    const updatedCard = await prisma.card.update({
      where: { id: cardId },
      data: {
        ...(title !== undefined && { title }),
        ...(description !== undefined && { description }),
        ...(dueDate !== undefined && { dueDate: dueDate ? new Date(dueDate) : null }),
        ...(position !== undefined && { position }),
        ...(cover !== undefined && { cover }),
      },
      include: {
        labels: {
          include: {
            label: true
          }
        },
        assignedMembers: {
          include: {
            user: true
          }
        },
        checklist: true,
        comments: {
          include: {
            author: true
          }
        },
        attachments: true,
      }
    });

    // Transform to frontend format
    const cardResponse = {
      id: updatedCard.id,
      title: updatedCard.title,
      description: updatedCard.description,
      position: updatedCard.position,
      cover: updatedCard.cover,
      dueDate: updatedCard.dueDate,
      createdAt: updatedCard.createdAt,
      updatedAt: updatedCard.updatedAt,
      labels: updatedCard.labels.map(cl => ({
        id: cl.label.id,
        name: cl.label.name,
        color: cl.label.color
      })),
      assignedMembers: updatedCard.assignedMembers.map(am => ({
        id: am.user.id,
        name: am.user.name,
        email: am.user.email,
        avatar: am.user.avatar
      })),
      checklist: updatedCard.checklist.map(item => ({
        id: item.id,
        text: item.text,
        completed: item.completed
      })),
      comments: updatedCard.comments.map(comment => ({
        id: comment.id,
        text: comment.text,
        createdAt: comment.createdAt,
        author: {
          id: comment.author.id,
          name: comment.author.name,
          avatar: comment.author.avatar
        }
      })),
      attachments: updatedCard.attachments.map(att => ({
        id: att.id,
        name: att.name,
        url: att.url,
        type: att.type,
        size: att.size
      }))
    };

    console.log('✅ Card updated successfully:', updatedCard.title);
    res.json(cardResponse);
  } catch (error) {
    console.error('❌ Update card error:', error);
    res.status(500).json({ error: 'Error updating card' });
  }
});

// Move card between lists
app.put('/api/boards/:boardId/cards/:cardId/move', async (req, res) => {
  try {
    const { boardId, cardId } = req.params;
    const { sourceListId, targetListId, newPosition } = req.body;

    console.log('🔄 Moving card:', { boardId, cardId, sourceListId, targetListId, newPosition });

    // Check if card exists
    const existingCard = await prisma.card.findUnique({
      where: { id: cardId },
      include: {
        list: {
          include: {
            board: true
          }
        }
      }
    });

    if (!existingCard || existingCard.list.boardId !== boardId) {
      console.log('❌ Card not found or doesn\'t belong to specified board:', cardId);
      return res.status(404).json({ error: 'Card not found' });
    }

    // Update card's list and position
    await prisma.card.update({
      where: { id: cardId },
      data: {
        listId: targetListId,
        position: newPosition || 0,
      }
    });

    // Get updated board data
    const updatedBoard = await prisma.board.findUnique({
      where: { id: boardId },
      include: {
        lists: {
          include: {
            cards: {
              include: {
                labels: {
                  include: {
                    label: true
                  }
                },
                assignedMembers: {
                  include: {
                    user: true
                  }
                },
                checklist: true,
                comments: {
                  include: {
                    author: true
                  }
                },
                attachments: true,
              }
            }
          }
        }
      }
    });

    console.log('✅ Card moved successfully');
    res.json({ success: true, board: updatedBoard });
  } catch (error) {
    console.error('❌ Move card error:', error);
    res.status(500).json({ error: 'Error moving card' });
  }
});

// Copy card
app.post('/api/boards/:boardId/lists/:listId/cards/:cardId/copy', async (req, res) => {
  try {
    const { boardId, listId, cardId } = req.params;
    const { title } = req.body;

    console.log('📋 Copying card:', { boardId, listId, cardId, title });

    // Get original card
    const originalCard = await prisma.card.findUnique({
      where: { id: cardId },
      include: {
        labels: {
          include: {
            label: true
          }
        },
        assignedMembers: {
          include: {
            user: true
          }
        },
        checklist: true,
        attachments: true,
      }
    });

    if (!originalCard) {
      return res.status(404).json({ error: 'Original card not found' });
    }

    // Get next position
    const lastCard = await prisma.card.findFirst({
      where: { listId },
      orderBy: { position: 'desc' }
    });
    const nextPosition = lastCard ? lastCard.position + 1 : 1;

    // Create copied card
    const copiedCard = await prisma.card.create({
      data: {
        title: title || `${originalCard.title} (copia)`,
        description: originalCard.description,
        position: nextPosition,
        listId,
        dueDate: originalCard.dueDate,
        cover: originalCard.cover,
      },
      include: {
        labels: {
          include: {
            label: true
          }
        },
        assignedMembers: {
          include: {
            user: true
          }
        },
        checklist: true,
        comments: {
          include: {
            author: true
          }
        },
        attachments: true,
      }
    });

    // Copy checklist items
    if (originalCard.checklist.length > 0) {
      await prisma.checklistItem.createMany({
        data: originalCard.checklist.map((item, index) => ({
          text: item.text,
          completed: false, // Reset completion status
          cardId: copiedCard.id,
        }))
      });
    }

    // Copy assigned members
    if (originalCard.assignedMembers.length > 0) {
      await prisma.cardAssignment.createMany({
        data: originalCard.assignedMembers.map((assignment) => ({
          cardId: copiedCard.id,
          userId: assignment.userId,
        }))
      });
    }

    // Copy labels
    if (originalCard.labels.length > 0) {
      await prisma.cardLabel.createMany({
        data: originalCard.labels.map((cardLabel) => ({
          cardId: copiedCard.id,
          labelId: cardLabel.labelId,
        }))
      });
    }

    // Transform to frontend format
    const cardResponse = {
      id: copiedCard.id,
      title: copiedCard.title,
      description: copiedCard.description,
      position: copiedCard.position,
      dueDate: copiedCard.dueDate,
      createdAt: copiedCard.createdAt,
      updatedAt: copiedCard.updatedAt,
      labels: [],
      assignedMembers: [],
      checklist: [],
      comments: [],
      attachments: []
    };

    console.log('✅ Card copied successfully:', copiedCard.title);
    res.status(201).json(cardResponse);
  } catch (error) {
    console.error('❌ Copy card error:', error);
    res.status(500).json({ error: 'Error copying card' });
  }
});

// Start server
app.listen(PORT, async () => {
  console.log(`🚀 Trello API Server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);

  // Initialize users on startup
  await initializeUsers();
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down server...');
  await prisma.$disconnect();
  process.exit(0);
});
