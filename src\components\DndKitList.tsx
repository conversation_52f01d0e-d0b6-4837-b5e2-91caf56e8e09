import React, { useState, useEffect } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useDroppable } from '@dnd-kit/core';
import { useResponsive } from '../hooks/useResponsive';
import { List as ListType, Card as CardType } from '../types';
import { DndKitCard } from './DndKitCard';
import { Plus, MoreHorizontal, GripVertical, Edit3, Trash2, X } from 'lucide-react';

interface DndKitListProps {
  list: ListType;
  onAddCard: (listId: string, title: string) => void;
  onCardClick: (card: CardType) => void;
  onCardUpdate?: (card: CardType) => void;
  onListUpdate?: (list: ListType) => void;
  onListDelete?: (listId: string) => void;
  isDragOverlay?: boolean;
}

export function DndKitList({
  list,
  onAddCard,
  onCardClick,
  onCardUpdate,
  onListUpdate,
  onListDelete,
  isDragOverlay = false
}: DndKitListProps) {
  const [isAddingCard, setIsAddingCard] = useState(false);
  const [newCardTitle, setNewCardTitle] = useState('');
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [listTitle, setListTitle] = useState(list.title);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const { isMobile } = useResponsive();

  const {
    attributes,
    listeners,
    setNodeRef: setSortableNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: list.id,
    data: {
      type: 'list',
      list,
    },
    disabled: isDragOverlay, // Disable sorting when used as overlay
  });

  const { setNodeRef: setDroppableNodeRef } = useDroppable({
    id: list.id,
    data: {
      type: 'list',
      accepts: ['card'],
    },
    disabled: isDragOverlay, // Disable dropping when used as overlay
  });

  const style = {
    transform: CSS.Translate.toString(transform),
    transition,
  };

  // Sync title when list prop changes
  useEffect(() => {
    setListTitle(list.title);
  }, [list.title]);

  const handleAddCard = () => {
    if (newCardTitle.trim()) {
      onAddCard(list.id, newCardTitle.trim());
      setNewCardTitle('');
      setIsAddingCard(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddCard();
    } else if (e.key === 'Escape') {
      setIsAddingCard(false);
      setNewCardTitle('');
    }
  };

  const handleTitleSave = () => {
    const trimmedTitle = listTitle.trim();
    if (onListUpdate && trimmedTitle !== list.title && trimmedTitle.length > 0) {
      onListUpdate({ ...list, title: trimmedTitle, updatedAt: new Date() });
    } else if (trimmedTitle.length === 0) {
      // Don't allow empty titles
      setListTitle(list.title);
    }
    setIsEditingTitle(false);
  };

  const handleTitleCancel = () => {
    setListTitle(list.title);
    setIsEditingTitle(false);
  };

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    e.stopPropagation();
    if (e.key === 'Enter') {
      handleTitleSave();
    } else if (e.key === 'Escape') {
      handleTitleCancel();
    }
  };

  const handleDeleteList = () => {
    if (onListDelete) {
      onListDelete(list.id);
    }
    setShowDeleteConfirm(false);
  };

  // Combine refs for both sortable and droppable
  const setNodeRef = (node: HTMLElement | null) => {
    setSortableNodeRef(node);
    setDroppableNodeRef(node);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`list-container flex flex-col ${isMobile ? 'w-64 min-w-64' : 'w-72 min-w-72'} max-w-sm bg-gray-50 rounded-lg shadow-sm transition-all duration-200 ${
        isDragging && !isDragOverlay ? 'opacity-50' : ''
      } ${isDragOverlay ? 'rotate-3 shadow-2xl' : 'hover:shadow-md'}`}
    >
      {/* List Header */}
      <div className={`flex items-center justify-between ${isMobile ? 'p-2' : 'p-3'} border-b bg-white rounded-t-lg group relative`}>
        {/* Drag Handle */}
        <div
          className="absolute left-0 top-0 w-8 h-full cursor-grab active:cursor-grabbing flex items-center justify-center hover:bg-gray-50 transition-colors"
          {...attributes}
          {...listeners}
          title="Arrastra para mover la lista"
        >
          <GripVertical className="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
        </div>

        {/* Title Section */}
        <div className="flex items-center space-x-2 min-w-0 flex-1 ml-8">
          {isEditingTitle ? (
            <input
              type="text"
              value={listTitle}
              onChange={(e) => setListTitle(e.target.value)}
              onBlur={handleTitleSave}
              onKeyDown={handleTitleKeyDown}
              onClick={(e) => e.stopPropagation()}
              className={`font-semibold text-gray-900 bg-white border-2 border-blue-400 rounded-lg px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm ${isMobile ? 'text-sm' : ''}`}
              autoFocus
              placeholder="Título de la lista"
            />
          ) : (
            <h2
              className={`font-semibold text-gray-900 truncate cursor-text hover:bg-gray-50 hover:shadow-sm rounded-lg px-3 py-2 transition-all duration-200 border border-transparent hover:border-gray-200 ${isMobile ? 'text-sm' : ''}`}
              onClick={(e) => {
                e.stopPropagation();
                setIsEditingTitle(true);
              }}
              title="Haz clic para editar el título"
            >
              {list.title}
            </h2>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-1 flex-shrink-0">
          <span className={`text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full ${isMobile ? 'text-xs' : 'text-sm'}`}>
            {list.cards.length}
          </span>

          {/* Edit Button */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              setIsEditingTitle(true);
            }}
            className="p-1 hover:bg-gray-100 rounded transition-colors"
            title="Editar título"
          >
            <Edit3 className="w-4 h-4 text-gray-500 hover:text-gray-700" />
          </button>

          {/* Delete Button */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowDeleteConfirm(true);
            }}
            className="p-1 hover:bg-red-100 rounded transition-colors"
            title="Eliminar lista"
          >
            <Trash2 className="w-4 h-4 text-gray-500 hover:text-red-600" />
          </button>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setShowDeleteConfirm(false)}
            />

            {/* Modal */}
            <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white border border-gray-200 rounded-lg shadow-xl p-6 z-50 max-w-md w-full mx-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Eliminar Lista
                </h3>
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                >
                  <X className="w-5 h-5 text-gray-500" />
                </button>
              </div>

              <div className="text-sm text-gray-900 mb-3">
                ¿Estás seguro de que quieres eliminar la lista <strong>"{list.title}"</strong>?
              </div>

              <div className="text-sm text-gray-600 mb-6 bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <div className="w-4 h-4 text-red-500 mt-0.5">⚠️</div>
                  <div>
                    <div className="font-medium text-red-800 mb-1">Esta acción no se puede deshacer</div>
                    <div className="text-red-700">
                      {list.cards.length === 0
                        ? "Se eliminará la lista permanentemente."
                        : `Se eliminará la lista y todas sus ${list.cards.length} tarjeta${list.cards.length !== 1 ? 's' : ''} permanentemente.`
                      }
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleDeleteList}
                  className="px-4 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
                >
                  Eliminar Lista
                </button>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Cards Container */}
      <div className={`flex-1 ${isMobile ? 'p-2' : 'p-3'} min-h-32`}>
        <SortableContext
          items={list.cards.map(card => card.id)}
          strategy={verticalListSortingStrategy}
        >
          <div className={`${isMobile ? 'space-y-1' : 'space-y-2'}`}>
            {list.cards
              .sort((a, b) => a.position - b.position)
              .map((card) => (
                <DndKitCard
                  key={card.id}
                  card={card}
                  onClick={() => onCardClick(card)}
                  onUpdate={onCardUpdate}
                />
              ))}
          </div>
        </SortableContext>

        {/* Add Card Form */}
        {isAddingCard ? (
          <div className="mt-3">
            <textarea
              value={newCardTitle}
              onChange={(e) => setNewCardTitle(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Ingresa un título para esta tarjeta..."
              className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
              autoFocus
            />
            <div className="flex items-center space-x-2 mt-2">
              <button
                onClick={handleAddCard}
                className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Agregar Tarjeta
              </button>
              <button
                onClick={() => {
                  setIsAddingCard(false);
                  setNewCardTitle('');
                }}
                className="px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Cancelar
              </button>
            </div>
          </div>
        ) : (
          <button
            onClick={() => setIsAddingCard(true)}
            className="w-full mt-3 p-3 text-gray-600 hover:bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors flex items-center justify-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Agregar una tarjeta</span>
          </button>
        )}
      </div>
    </div>
  );
}
