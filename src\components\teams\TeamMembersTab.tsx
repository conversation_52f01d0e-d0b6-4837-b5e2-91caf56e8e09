import React, { useState } from 'react';
import { useDatabaseApp } from '../../context/DatabaseAppContext';
import { Team, TeamMember, User } from '../../types';
import { TeamMemberSelector } from './TeamMemberSelector';
import {
  UserPlus,
  Crown,
  Shield,
  Eye,
  Mail,
  MoreHorizontal,
  Search,
  Filter,
  Trash2,
  Edit3,
  Calendar,
  Check,
  X
} from 'lucide-react';

interface TeamMembersTabProps {
  team: Team;
  currentUserId: string;
  onUpdateTeam: (team: Team) => void;
}

export function TeamMembersTab({ team, currentUserId, onUpdateTeam }: TeamMembersTabProps) {
  const { state, dispatch } = useDatabaseApp();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterRole, setFilterRole] = useState<'all' | 'admin' | 'normal' | 'observer'>('all');
  const [showMemberSelector, setShowMemberSelector] = useState(false);
  const [editingMember, setEditingMember] = useState<string | null>(null);

  const currentUserMembership = team.members.find(member => member.userId === currentUserId);
  const isCurrentUserAdmin = currentUserMembership?.role === 'admin';

  // Filter members
  const filteredMembers = team.members.filter(member => {
    const user = state.users.find(u => u.id === member.userId);
    if (!user) return false;

    const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (!matchesSearch) return false;
    
    if (filterRole === 'all') return true;
    return member.role === filterRole;
  });

  const handleRoleChange = (memberId: string, newRole: 'admin' | 'normal' | 'observer') => {
    if (!isCurrentUserAdmin) return;

    const updatedTeam = {
      ...team,
      members: team.members.map(member =>
        member.userId === memberId ? { ...member, role: newRole } : member
      ),
      updatedAt: new Date()
    };

    onUpdateTeam(updatedTeam);
    setEditingMember(null);
  };

  const handleRemoveMember = (memberId: string) => {
    if (!isCurrentUserAdmin || memberId === currentUserId) return;

    const updatedTeam = {
      ...team,
      members: team.members.filter(member => member.userId !== memberId),
      updatedAt: new Date()
    };

    onUpdateTeam(updatedTeam);
  };

  const handleToggleMember = (user: User) => {
    if (!isCurrentUserAdmin) return;

    const isCurrentlyMember = team.members.some(member => member.userId === user.id);

    if (isCurrentlyMember) {
      // Remove member
      const updatedTeam: Team = {
        ...team,
        members: team.members.filter(member => member.userId !== user.id),
        updatedAt: new Date()
      };
      onUpdateTeam(updatedTeam);
    } else {
      // Add member with default role
      const newMember: TeamMember = {
        userId: user.id,
        role: 'normal',
        joinedAt: new Date()
      };

      const updatedTeam: Team = {
        ...team,
        members: [...team.members, newMember],
        updatedAt: new Date()
      };
      onUpdateTeam(updatedTeam);
    }
  };

  // Get current team members as User objects for the selector
  const currentTeamMembers = team.members.map(member => {
    const user = state.users.find(u => u.id === member.userId);
    return user;
  }).filter(Boolean) as User[];

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'normal':
        return <Shield className="w-4 h-4 text-blue-500" />;
      case 'observer':
        return <Eye className="w-4 h-4 text-gray-500" />;
      default:
        return null;
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Administrador';
      case 'normal':
        return 'Miembro';
      case 'observer':
        return 'Observador';
      default:
        return 'Sin rol';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'text-yellow-700 bg-yellow-100';
      case 'normal':
        return 'text-blue-700 bg-blue-100';
      case 'observer':
        return 'text-gray-700 bg-gray-100';
      default:
        return 'text-gray-700 bg-gray-100';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Miembros del Equipo ({team.members.length})
          </h3>
          <p className="text-sm text-gray-500">
            Gestiona los miembros y sus roles en el equipo
          </p>
        </div>
        
        {isCurrentUserAdmin && (
          <div className="relative">
            <button
              onClick={() => setShowMemberSelector(!showMemberSelector)}
              className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <UserPlus className="w-4 h-4" />
              <span>Gestionar Miembros</span>
            </button>

            {showMemberSelector && (
              <TeamMemberSelector
                assignedMembers={currentTeamMembers}
                onToggleMember={handleToggleMember}
                onClose={() => setShowMemberSelector(false)}
                teamId={team.id}
              />
            )}
          </div>
        )}
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Buscar miembros..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-gray-400" />
          <select
            value={filterRole}
            onChange={(e) => setFilterRole(e.target.value as any)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">Todos los roles</option>
            <option value="admin">Administradores</option>
            <option value="normal">Miembros</option>
            <option value="observer">Observadores</option>
          </select>
        </div>
      </div>

      {/* Members List */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        {filteredMembers.length === 0 ? (
          <div className="p-8 text-center">
            <Search className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <h4 className="text-lg font-medium text-gray-600 mb-2">No se encontraron miembros</h4>
            <p className="text-gray-500">
              {searchQuery || filterRole !== 'all' 
                ? 'Intenta ajustar tus filtros de búsqueda'
                : 'Este equipo no tiene miembros aún'
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredMembers.map(member => {
              const user = state.users.find(u => u.id === member.userId);
              if (!user) return null;

              const isEditing = editingMember === member.userId;
              const canEdit = isCurrentUserAdmin && member.userId !== currentUserId;

              return (
                <div key={member.userId} className="p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {user.avatar ? (
                        <img
                          src={user.avatar}
                          alt={user.name}
                          className="w-12 h-12 rounded-full object-cover border-2 border-gray-200"
                        />
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center text-white text-lg font-bold border-2 border-gray-200">
                          {user.name.charAt(0).toUpperCase()}
                        </div>
                      )}
                      <div>
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium text-gray-900">{user.name}</h4>
                          {member.userId === currentUserId && (
                            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                              Tú
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-500">{user.email}</p>
                        <div className="flex items-center space-x-3 mt-1">
                          <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(member.role)}`}>
                            {getRoleIcon(member.role)}
                            <span>{getRoleLabel(member.role)}</span>
                          </span>
                          <span className="text-xs text-gray-500 flex items-center space-x-1">
                            <Calendar className="w-3 h-3" />
                            <span>
                              Se unió el {new Date(member.joinedAt).toLocaleDateString('es-ES')}
                            </span>
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {isEditing ? (
                        <div className="flex items-center space-x-2">
                          <select
                            defaultValue={member.role}
                            onChange={(e) => handleRoleChange(member.userId, e.target.value as any)}
                            className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="admin">Administrador</option>
                            <option value="normal">Miembro</option>
                            <option value="observer">Observador</option>
                          </select>
                          <button
                            onClick={() => setEditingMember(null)}
                            className="p-1 text-gray-400 hover:text-gray-600"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ) : (
                        canEdit && (
                          <div className="flex items-center space-x-1">
                            <button
                              onClick={() => setEditingMember(member.userId)}
                              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
                              title="Cambiar rol"
                            >
                              <Edit3 className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleRemoveMember(member.userId)}
                              className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded"
                              title="Remover del equipo"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Role Descriptions */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-3">Roles y Permisos</h4>
        <div className="space-y-2 text-sm">
          <div className="flex items-center space-x-3">
            <Crown className="w-4 h-4 text-yellow-500" />
            <span className="font-medium">Administrador:</span>
            <span className="text-gray-600">Puede gestionar miembros, configuración y eliminar el equipo</span>
          </div>
          <div className="flex items-center space-x-3">
            <Shield className="w-4 h-4 text-blue-500" />
            <span className="font-medium">Miembro:</span>
            <span className="text-gray-600">Puede crear tableros y participar en todas las actividades</span>
          </div>
          <div className="flex items-center space-x-3">
            <Eye className="w-4 h-4 text-gray-500" />
            <span className="font-medium">Observador:</span>
            <span className="text-gray-600">Solo puede ver tableros y comentar, sin crear contenido</span>
          </div>
        </div>
      </div>

      {/* Legacy modal removed - now using TeamMemberSelector */}

    </div>
  );
}
