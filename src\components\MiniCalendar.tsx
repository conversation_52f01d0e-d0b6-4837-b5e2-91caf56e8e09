import React, { useState, useMemo } from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon } from 'lucide-react';

interface MiniCalendarProps {
  onDateSelect?: (date: Date) => void;
  onOpenFullCalendar?: () => void;
}

export function MiniCalendar({ onDateSelect, onOpenFullCalendar }: MiniCalendarProps) {
  const { state } = useDatabaseApp();
  const [currentDate, setCurrentDate] = useState(new Date());

  // Get all cards with due dates
  const cardsWithDates = useMemo(() => {
    const cards: { date: string; count: number }[] = [];
    const dateMap = new Map<string, number>();
    
    state.boards.forEach(board => {
      board.lists.forEach(list => {
        list.cards.forEach(card => {
          if (card.dueDate && !card.archived) {
            const dateStr = new Date(card.dueDate).toDateString();
            dateMap.set(dateStr, (dateMap.get(dateStr) || 0) + 1);
          }
        });
      });
    });

    dateMap.forEach((count, date) => {
      cards.push({ date, count });
    });

    return cards;
  }, [state.boards]);

  // Generate mini calendar days
  const calendarDays = useMemo(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const days = [];
    const currentDay = new Date(startDate);
    
    for (let i = 0; i < 42; i++) {
      const dayStr = currentDay.toDateString();
      const cardData = cardsWithDates.find(c => c.date === dayStr);
      
      days.push({
        date: new Date(currentDay),
        isCurrentMonth: currentDay.getMonth() === month,
        isToday: currentDay.toDateString() === new Date().toDateString(),
        taskCount: cardData?.count || 0
      });
      
      currentDay.setDate(currentDay.getDate() + 1);
    }
    
    return days;
  }, [currentDate, cardsWithDates]);

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(prev.getMonth() + (direction === 'next' ? 1 : -1));
      return newDate;
    });
  };

  const monthNames = [
    'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
    'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
  ];

  const dayNames = ['Do', 'Lu', 'Ma', 'Mi', 'Ju', 'Vi', 'Sa'];

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <button
          onClick={() => navigateMonth('prev')}
          className="p-1 hover:bg-gray-100 rounded"
        >
          <ChevronLeft className="w-4 h-4 text-gray-600" />
        </button>
        
        <button
          onClick={onOpenFullCalendar}
          className="text-sm font-semibold text-gray-900 hover:text-blue-600 transition-colors"
          title="Abrir calendario completo"
        >
          {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
        </button>
        
        <button
          onClick={() => navigateMonth('next')}
          className="p-1 hover:bg-gray-100 rounded"
        >
          <ChevronRight className="w-4 h-4 text-gray-600" />
        </button>
      </div>

      {/* Day Headers */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {dayNames.map((day, index) => (
          <div key={index} className="text-center text-xs font-medium text-gray-500 py-1">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar Days */}
      <div className="grid grid-cols-7 gap-1">
        {calendarDays.map((day, index) => (
          <button
            key={index}
            onClick={() => onDateSelect?.(day.date)}
            className={`relative h-8 text-xs rounded hover:bg-gray-100 transition-colors ${
              day.isCurrentMonth 
                ? 'text-gray-900' 
                : 'text-gray-400'
            } ${
              day.isToday 
                ? 'bg-blue-100 text-blue-600 font-semibold' 
                : ''
            }`}
          >
            {day.date.getDate()}
            
            {/* Task indicator */}
            {day.taskCount > 0 && (
              <div className="absolute bottom-0 right-0 w-2 h-2 bg-red-500 rounded-full transform translate-x-1 translate-y-1">
                {day.taskCount > 9 && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">!</span>
                  </div>
                )}
              </div>
            )}
          </button>
        ))}
      </div>

      {/* Footer */}
      <div className="mt-3 pt-3 border-t border-gray-200">
        <button
          onClick={onOpenFullCalendar}
          className="w-full flex items-center justify-center space-x-2 text-sm text-blue-600 hover:text-blue-700 transition-colors"
        >
          <CalendarIcon className="w-4 h-4" />
          <span>Ver calendario completo</span>
        </button>
      </div>

      {/* Quick Stats */}
      <div className="mt-2 text-xs text-gray-500 text-center">
        {cardsWithDates.length > 0 ? (
          <span>
            {cardsWithDates.reduce((sum, card) => sum + card.count, 0)} tareas programadas
          </span>
        ) : (
          <span>No hay tareas programadas</span>
        )}
      </div>
    </div>
  );
}
