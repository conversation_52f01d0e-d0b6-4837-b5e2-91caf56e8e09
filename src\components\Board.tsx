import React, { useEffect, useState } from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { useDragAndDrop } from '../hooks/useDragAndDrop';
import { useSearch, SearchFilters } from '../hooks/useSearch';
import { List } from './List';
import { CardModal } from './CardModal';
import { SearchAndFilter } from './SearchAndFilter';
import { SearchResults } from './SearchResults';
import { AddListForm } from './AddListForm';
import { BoardManager } from './BoardManager';
import { DropZone } from './DropZone';
import { DragDropHelp } from './DragDropHelp';
import { ListPlaceholder } from './ListPlaceholder';
import { Board as BoardType, List as ListType, Card as CardType, User } from '../types';
import { Plus } from 'lucide-react';

export function Board() {
  const { state, getCurrentBoard } = useDatabaseApp();
  const currentBoard = getCurrentBoard();
  const { searchQuery } = state;
  const [selectedCard, setSelectedCard] = useState<CardType | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [showBoardManager, setShowBoardManager] = useState(false);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    query: '',
    labels: [],
    members: [],
    dueDate: '',
    hasChecklist: null,
    hasAttachments: null
  });
  
  const {
    dragOverItem,
    dragType,
    draggedItem,
    placeholderPosition,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDragLeave,
    handleDrop
  } = useDragAndDrop();

  // Search functionality
  const { filteredCards, searchStats, highlightText } = useSearch(currentBoard, {
    ...searchFilters,
    query: searchQuery
  });

  // Update search filters when global search query changes
  React.useEffect(() => {
    setSearchFilters(prev => ({ ...prev, query: searchQuery }));
    setShowSearchResults(searchQuery.length > 0 ||
      searchFilters.labels.length > 0 ||
      searchFilters.members.length > 0 ||
      searchFilters.dueDate !== '' ||
      searchFilters.hasChecklist !== null ||
      searchFilters.hasAttachments !== null
    );
  }, [searchQuery, searchFilters.labels, searchFilters.members, searchFilters.dueDate, searchFilters.hasChecklist, searchFilters.hasAttachments]);

  // Sample data initialization
  useEffect(() => {
    if (!currentBoard && state.boards.length === 0) {
      const sampleBoard: BoardType = {
        id: '1',
        title: 'Tablero de Desarrollo de Producto',
        description: 'Tablero principal para tareas de desarrollo de producto',
        background: 'bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500',
        isPublic: false,
        isFavorite: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        members: [
          {
            id: '1',
            name: 'Juan Pérez',
            email: '<EMAIL>',
            avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
            role: 'admin'
          },
          {
            id: '2',
            name: 'María García',
            email: '<EMAIL>',
            avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
            role: 'member'
          }
        ],
        labels: [
          { id: '1', name: 'Alta Prioridad', color: '#ef4444' },
          { id: '2', name: 'Funcionalidad', color: '#3b82f6' },
          { id: '3', name: 'Error', color: '#dc2626' }
        ],
        lists: [
          {
            id: '1',
            title: 'Por Hacer',
            position: 0,
            archived: false,
            cards: [
              {
                id: '1',
                title: 'Diseñar nueva página de inicio',
                description: 'Crear una página de inicio moderna y responsiva con mejor UX',
                labels: [{ id: '1', name: 'Alta Prioridad', color: '#ef4444' }],
                dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
                assignedMembers: [
                  {
                    id: '2',
                    name: 'María García',
                    email: '<EMAIL>',
                    avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
                    role: 'member'
                  }
                ],
                checklist: [
                  { id: '1', text: 'Investigar competidores', completed: true },
                  { id: '2', text: 'Crear wireframes', completed: false },
                  { id: '3', text: 'Diseñar mockups', completed: false }
                ],
                comments: [],
                attachments: [],
                position: 0,
                archived: false,
                createdAt: new Date(),
                updatedAt: new Date()
              },
              {
                id: '2',
                title: 'Implementar autenticación de usuarios',
                description: 'Agregar funcionalidad segura de inicio de sesión y registro',
                labels: [{ id: '2', name: 'Funcionalidad', color: '#3b82f6' }],
                assignedMembers: [],
                checklist: [],
                comments: [
                  {
                    id: '1',
                    text: '¿Deberíamos usar OAuth o email/contraseña tradicional?',
                    author: {
                      id: '1',
                      name: 'Juan Pérez',
                      email: '<EMAIL>',
                      avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
                      role: 'admin'
                    },
                    createdAt: new Date(),
                    mentions: []
                  }
                ],
                attachments: [],
                position: 1,
                archived: false,
                createdAt: new Date(),
                updatedAt: new Date()
              }
            ]
          },
          {
            id: '2',
            title: 'En Progreso',
            position: 1,
            archived: false,
            cards: [
              {
                id: '3',
                title: 'Corregir error de navegación móvil',
                description: 'El menú de navegación no funciona correctamente en dispositivos móviles',
                labels: [{ id: '3', name: 'Error', color: '#dc2626' }],
                dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
                assignedMembers: [
                  {
                    id: '1',
                    name: 'Juan Pérez',
                    email: '<EMAIL>',
                    avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
                    role: 'admin'
                  }
                ],
                checklist: [
                  { id: '1', text: 'Identificar el problema', completed: true },
                  { id: '2', text: 'Corregir el error', completed: false },
                  { id: '3', text: 'Probar en múltiples dispositivos', completed: false }
                ],
                comments: [],
                attachments: [],
                position: 0,
                archived: false,
                createdAt: new Date(),
                updatedAt: new Date()
              }
            ]
          },
          {
            id: '3',
            title: 'Revisión',
            position: 2,
            archived: false,
            cards: []
          },
          {
            id: '4',
            title: 'Completado',
            position: 3,
            archived: false,
            cards: [
              {
                id: '4',
                title: 'Configurar repositorio del proyecto',
                description: 'Inicializar repositorio Git con estructura de carpetas adecuada',
                labels: [],
                assignedMembers: [],
                checklist: [
                  { id: '1', text: 'Crear repositorio', completed: true },
                  { id: '2', text: 'Configurar CI/CD', completed: true },
                  { id: '3', text: 'Agregar miembros del equipo', completed: true }
                ],
                comments: [],
                attachments: [],
                position: 0,
                archived: false,
                createdAt: new Date(),
                updatedAt: new Date()
              }
            ]
          }
        ]
      };

      dispatch({ type: 'ADD_BOARD', payload: sampleBoard });
      dispatch({ type: 'SET_CURRENT_BOARD', payload: sampleBoard });
    }
  }, [currentBoard, state.boards.length, dispatch]);

  const handleAddCard = (listId: string, title: string) => {
    if (!currentBoard) return;

    const newCard: CardType = {
      id: Date.now().toString(),
      title,
      description: '',
      labels: [],
      assignedMembers: [],
      checklist: [],
      comments: [],
      attachments: [],
      position: currentBoard.lists.find(l => l.id === listId)?.cards.length || 0,
      archived: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    dispatch({
      type: 'ADD_CARD',
      payload: { boardId: currentBoard.id, listId, card: newCard }
    });
  };

  const handleCardDrop = (card: CardType, toListId: string) => {
    if (!currentBoard) return;

    const fromList = currentBoard.lists.find(list =>
      list.cards.some(c => c.id === card.id)
    );

    if (!fromList || fromList.id === toListId) return;

    const toList = currentBoard.lists.find(list => list.id === toListId);
    if (!toList) return;

    dispatch({
      type: 'MOVE_CARD',
      payload: {
        boardId: currentBoard.id,
        cardId: card.id,
        fromListId: fromList.id,
        toListId: toListId,
        newPosition: toList.cards.length
      }
    });
  };

  const handleListDrop = (list: ListType, newPosition: number) => {
    if (!currentBoard) return;

    // Get the current index of the list in the sorted array
    const sortedLists = currentBoard.lists.sort((a, b) => a.position - b.position);
    const currentIndex = sortedLists.findIndex(l => l.id === list.id);

    // Don't move if it's the same position
    if (currentIndex === newPosition) return;

    dispatch({
      type: 'MOVE_LIST',
      payload: {
        boardId: currentBoard.id,
        listId: list.id,
        newPosition: newPosition
      }
    });
  };

  const handleCardClick = (card: CardType) => {
    setSelectedCard(card);
    setShowModal(true);
  };

  if (!currentBoard) {
    return (
      <>
        <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
          <div className="text-center">
            <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Plus className="w-12 h-12 text-blue-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Bienvenido a SapiaFlow</h2>
            <p className="text-gray-600 mb-6">
              {state.boards.length === 0
                ? 'Crea tu primer tablero para comenzar a organizar tus tareas'
                : 'Selecciona un tablero de la barra lateral para comenzar'
              }
            </p>
            <div className="space-y-3">
              <button
                onClick={() => setShowBoardManager(true)}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 mx-auto"
              >
                <Plus className="w-5 h-5" />
                <span>Crear Tu Primer Tablero</span>
              </button>
              {state.boards.length > 0 && (
                <p className="text-sm text-gray-500">
                  O selecciona un tablero existente desde la barra lateral
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Board Manager Modal */}
        <BoardManager
          isOpen={showBoardManager}
          onClose={() => setShowBoardManager(false)}
        />
      </>
    );
  }

  return (
    <>
      {/* Search and Filter Bar */}
      {(searchQuery || showSearchResults) && (
        <SearchAndFilter />
      )}

      {/* Main Content */}
      {showSearchResults ? (
        <SearchResults
          cards={filteredCards}
          query={searchQuery}
          onCardClick={handleCardClick}
          highlightText={highlightText}
        />
      ) : (
        <div className="flex-1 overflow-x-auto bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 p-6">
          <div className="flex items-start min-w-max space-x-4">
            {(() => {
              const sortedLists = currentBoard.lists.sort((a, b) => a.position - b.position);
              const isDraggingList = dragType === 'list' && draggedItem;
              const draggedListData = isDraggingList ? draggedItem as ListType : null;
              const items: React.ReactNode[] = [];

              // Add placeholder at the beginning if needed
              if (isDraggingList && placeholderPosition === 0) {
                items.push(
                  <ListPlaceholder
                    key="placeholder-0"
                    draggedList={draggedListData}
                    position={0}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={(e, targetId) => handleDrop(e, targetId, handleCardDrop, handleListDrop)}
                    isDragOver={dragOverItem === '0'}
                  />
                );
              }

              sortedLists.forEach((list, index) => {
                // Skip the dragged list (don't render it in its original position)
                if (isDraggingList && draggedListData && list.id === draggedListData.id) {
                  return;
                }

                // Add the list
                items.push(
                  <List
                    key={list.id}
                    list={list}
                    onAddCard={handleAddCard}
                    onDragStart={handleDragStart}
                    onDragEnd={handleDragEnd}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={(e, targetId) => handleDrop(e, targetId, handleCardDrop, handleListDrop)}
                    isDragOver={dragOverItem === list.id}
                    position={index}
                    onCardClick={handleCardClick}
                  />
                );

                // Add placeholder after this list if needed
                if (isDraggingList && placeholderPosition === index + 1) {
                  items.push(
                    <ListPlaceholder
                      key={`placeholder-${index + 1}`}
                      draggedList={draggedListData}
                      position={index + 1}
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}
                      onDrop={(e, targetId) => handleDrop(e, targetId, handleCardDrop, handleListDrop)}
                      isDragOver={dragOverItem === (index + 1).toString()}
                    />
                  );
                }
              });

              // Add drop zones only when not dragging a list
              if (!isDraggingList) {
                const itemsWithDropZones: React.ReactNode[] = [];

                // Drop zone at the beginning
                itemsWithDropZones.push(
                  <DropZone
                    key="dropzone-0"
                    position={0}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={(e, targetId) => handleDrop(e, targetId, handleCardDrop, handleListDrop)}
                    isDragOver={dragOverItem === '0'}
                    dragType={dragType}
                  />
                );

                sortedLists.forEach((list, index) => {
                  itemsWithDropZones.push(
                    <List
                      key={list.id}
                      list={list}
                      onAddCard={handleAddCard}
                      onDragStart={handleDragStart}
                      onDragEnd={handleDragEnd}
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}
                      onDrop={(e, targetId) => handleDrop(e, targetId, handleCardDrop, handleListDrop)}
                      isDragOver={dragOverItem === list.id}
                      position={index}
                      onCardClick={handleCardClick}
                    />
                  );

                  // Drop zone after each list
                  itemsWithDropZones.push(
                    <DropZone
                      key={`dropzone-${index + 1}`}
                      position={index + 1}
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}
                      onDrop={(e, targetId) => handleDrop(e, targetId, handleCardDrop, handleListDrop)}
                      isDragOver={dragOverItem === (index + 1).toString()}
                      dragType={dragType}
                    />
                  );
                });

                return itemsWithDropZones;
              }

              return items;
            })()}

            {/* Add New List */}
            <div className="ml-6">
              <AddListForm boardId={currentBoard.id} />
            </div>
          </div>
        </div>
      )}

      {/* Card Modal */}
      {showModal && selectedCard && (
        <CardModal
          card={selectedCard}
          onClose={() => {
            setShowModal(false);
            setSelectedCard(null);
          }}
          onUpdate={(updatedCard) => {
            if (!currentBoard) return;

            // Find the list containing the card
            const listWithCard = currentBoard.lists.find(list =>
              list.cards.some(card => card.id === updatedCard.id)
            );

            if (listWithCard) {
              dispatch({
                type: 'UPDATE_CARD',
                payload: {
                  boardId: currentBoard.id,
                  listId: listWithCard.id,
                  card: updatedCard
                }
              });

              // Update the selected card to reflect changes in the modal
              setSelectedCard(updatedCard);
            }
          }}
        />
      )}

      {/* Drag and Drop Help */}
      <DragDropHelp />
    </>
  );
}