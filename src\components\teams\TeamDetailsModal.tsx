import React, { useState } from 'react';
import { useDatabaseApp } from '../../context/DatabaseAppContext';
import { useAuth } from '../../context/AuthContext';
import { Team, TeamMember } from '../../types';
import {
  X,
  Users,
  Settings,
  Crown,
  UserPlus,
  Mail,
  MoreHorizontal,
  Edit3,
  Trash2,
  Shield,
  Eye,
  Calendar,
  Activity,
  Grid3X3 as BoardIcon
} from 'lucide-react';
import { TeamMembersTab } from './TeamMembersTab';
import { TeamSettingsTab } from './TeamSettingsTab';
import { TeamBoardsTab } from './TeamBoardsTab';
import { TeamActivityTab } from './TeamActivityTab';

interface TeamDetailsModalProps {
  team: Team;
  onClose: () => void;
  onUpdateTeam: (team: Team) => void;
  onDeleteTeam: (teamId: string) => void;
}

export function TeamDetailsModal({ team, onClose, onUpdateTeam, onDeleteTeam }: TeamDetailsModalProps) {
  const { state } = useDatabaseApp();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'overview' | 'members' | 'boards' | 'settings' | 'activity'>('overview');

  const userMembership = team.members.find(member => member.userId === user?.id);
  const userRole = userMembership?.role || 'observer';
  const isAdmin = userRole === 'admin';
  const canManage = isAdmin;

  const tabs = [
    { id: 'overview', label: 'Resumen', icon: Eye },
    { id: 'members', label: 'Miembros', icon: Users, count: team.members.length },
    { id: 'boards', label: 'Tableros', icon: BoardIcon, count: team.boards.length },
    { id: 'activity', label: 'Actividad', icon: Activity },
    ...(canManage ? [{ id: 'settings', label: 'Configuración', icon: Settings }] : [])
  ];

  const getVisibilityInfo = () => {
    switch (team.visibility) {
      case 'private':
        return { label: 'Privado', color: 'text-red-600 bg-red-100', description: 'Solo miembros invitados' };
      case 'team':
        return { label: 'Equipo', color: 'text-blue-600 bg-blue-100', description: 'Miembros del equipo' };
      case 'organization':
        return { label: 'Organización', color: 'text-purple-600 bg-purple-100', description: 'Toda la organización' };
      case 'public':
        return { label: 'Público', color: 'text-green-600 bg-green-100', description: 'Cualquiera en internet' };
    }
  };

  const visibilityInfo = getVisibilityInfo();

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Team Info */}
      <div className="bg-gray-50 rounded-lg p-6">
        <div className="flex items-start space-x-4">
          {team.avatar ? (
            <img
              src={team.avatar}
              alt={team.name}
              className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
            />
          ) : (
            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center">
              <Users className="w-8 h-8 text-white" />
            </div>
          )}
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-900 mb-2">{team.name}</h3>
            {team.description && (
              <p className="text-gray-600 mb-3">{team.description}</p>
            )}
            <div className="flex items-center space-x-4">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${visibilityInfo.color}`}>
                {visibilityInfo.label}
              </span>
              <span className="text-sm text-gray-500">
                Creado el {new Date(team.createdAt).toLocaleDateString('es-ES', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white border border-gray-200 rounded-lg p-6 text-center">
          <Users className="w-8 h-8 text-blue-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-gray-900">{team.members.length}</div>
          <div className="text-sm text-gray-500">Miembros</div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 text-center">
          <BoardIcon className="w-8 h-8 text-green-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-gray-900">{team.boards.length}</div>
          <div className="text-sm text-gray-500">Tableros</div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 text-center">
          <Activity className="w-8 h-8 text-purple-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-gray-900">
            {team.members.filter(m => m.role === 'admin').length}
          </div>
          <div className="text-sm text-gray-500">Administradores</div>
        </div>
      </div>

      {/* Recent Members */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Miembros Recientes</h4>
        <div className="space-y-3">
          {team.members
            .sort((a, b) => new Date(b.joinedAt).getTime() - new Date(a.joinedAt).getTime())
            .slice(0, 5)
            .map(member => {
              const user = state.users.find(u => u.id === member.userId);
              if (!user) return null;

              return (
                <div key={member.userId} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                    <div>
                      <div className="font-medium text-gray-900">{user.name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {member.role === 'admin' && (
                      <Crown className="w-4 h-4 text-yellow-500" />
                    )}
                    <span className="text-sm text-gray-500">
                      {new Date(member.joinedAt).toLocaleDateString('es-ES')}
                    </span>
                  </div>
                </div>
              );
            })}
        </div>
      </div>

      {/* Team Settings Preview */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Configuración</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${team.settings.allowMembersToCreateBoards ? 'bg-green-500' : 'bg-gray-300'}`} />
            <span className="text-sm text-gray-700">Los miembros pueden crear tableros</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${team.settings.allowMembersToInvite ? 'bg-green-500' : 'bg-gray-300'}`} />
            <span className="text-sm text-gray-700">Los miembros pueden invitar personas</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${team.settings.requireApprovalForJoining ? 'bg-yellow-500' : 'bg-gray-300'}`} />
            <span className="text-sm text-gray-700">Requiere aprobación para unirse</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 rounded-full bg-blue-500" />
            <span className="text-sm text-gray-700">Visibilidad: {visibilityInfo.label}</span>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-6 border-b bg-gradient-to-r from-purple-600 to-blue-600 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Users className="w-6 h-6" />
              <div>
                <h2 className="text-xl font-bold">{team.name}</h2>
                <p className="text-sm opacity-90">{visibilityInfo.description}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Tabs */}
          <div className="mt-6 flex space-x-1 bg-white bg-opacity-20 rounded-lg p-1">
            {tabs.map(tab => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive 
                      ? 'bg-white text-purple-600' 
                      : 'text-white hover:bg-white hover:bg-opacity-20'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                  {'count' in tab && tab.count !== undefined && (
                    <span className={`px-1.5 py-0.5 rounded-full text-xs ${
                      isActive ? 'bg-purple-100 text-purple-600' : 'bg-white bg-opacity-20'
                    }`}>
                      {tab.count}
                    </span>
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {activeTab === 'overview' && renderOverviewTab()}
          {activeTab === 'members' && (
            <TeamMembersTab
              team={team}
              currentUserId={user?.id || ''}
              onUpdateTeam={onUpdateTeam}
            />
          )}
          {activeTab === 'boards' && (
            <TeamBoardsTab
              team={team}
              onUpdateTeam={onUpdateTeam}
            />
          )}
          {activeTab === 'settings' && canManage && (
            <TeamSettingsTab
              team={team}
              onUpdateTeam={onUpdateTeam}
              onDeleteTeam={onDeleteTeam}
            />
          )}
          {activeTab === 'activity' && (
            <TeamActivityTab team={team} />
          )}
        </div>
      </div>
    </div>
  );
}
