// Test server to diagnose the issue
console.log('🎯 Starting test server...');

import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 3002;

console.log('📦 Express imported successfully');

// CORS configuration
app.use(cors({
  origin: true,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-user-id']
}));

app.use(express.json());

console.log('🔧 Middleware configured');

// Health check endpoint
app.get('/health', (req, res) => {
  console.log('🏥 Health check requested');
  res.json({ 
    status: 'OK', 
    message: 'Test server is running',
    timestamp: new Date().toISOString()
  });
});

// Test endpoint
app.get('/api/test', (req, res) => {
  console.log('🧪 Test endpoint requested');
  res.json({ 
    success: true, 
    message: 'Test API is working' 
  });
});

console.log('🛣️ Routes configured');

// Start server
console.log('🚀 Starting server on port', PORT);

const server = app.listen(PORT, () => {
  console.log(`✅ Test server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log('🔄 Server is ready and waiting...');
});

server.on('error', (error) => {
  console.error('❌ Server error:', error);
});

// Keep alive
setInterval(() => {
  console.log('💓 Server heartbeat -', new Date().toISOString());
}, 30000);

console.log('🎯 Test server setup complete');
