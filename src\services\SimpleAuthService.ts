import { User } from '../types';
import { getApiUrl } from '../config/api';

const API_BASE_URL = getApiUrl();

export interface LoginCredentials {
  email: string;
  password: string;
}

export class SimpleAuthService {
  private static instance: SimpleAuthService;
  private currentUser: User | null = null;
  private listeners: ((user: User | null) => void)[] = [];

  private constructor() {
    // No longer loading from localStorage - user state is managed in memory only
  }

  static getInstance(): SimpleAuthService {
    if (!SimpleAuthService.instance) {
      SimpleAuthService.instance = new SimpleAuthService();
    }
    return SimpleAuthService.instance;
  }

  private saveUserToStorage(user: User | null): void {
    // No longer using localStorage - user state is managed in memory only
    // User persistence is handled by the backend session management
  }

  private clearUserFromStorage(): void {
    this.currentUser = null;
    this.notifyListeners();
    console.log('🗑️ User cleared from memory');
  }

  subscribe(listener: (user: User | null) => void): () => void {
    this.listeners.push(listener);
    listener(this.currentUser);
    
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.currentUser));
  }

  getCurrentUser(): User | null {
    return this.currentUser;
  }

  isAuthenticated(): boolean {
    return !!this.currentUser;
  }

  async login(credentials: LoginCredentials): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔐 SimpleAuthService.login called with:', { email: credentials.email });

      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      console.log('📊 Response status:', response.status);
      console.log('📊 Response ok:', response.ok);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Error de conexión' }));
        console.log('❌ Login failed:', errorData);
        return { success: false, error: errorData.error || 'Error de autenticación' };
      }

      const data = await response.json();
      console.log('✅ Login response:', { success: data.success, hasUser: !!data.user });

      if (data.success && data.user) {
        this.currentUser = data.user;
        this.notifyListeners();
        console.log('✅ Login successful for:', data.user.name);
        return { success: true };
      } else {
        console.log('❌ Login failed: Invalid response format');
        return { success: false, error: 'Respuesta inválida del servidor' };
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      return { success: false, error: 'Error de conexión con el servidor' };
    }
  }

  logout(): void {
    console.log('🚪 Logging out user:', this.currentUser?.name);
    this.clearUserFromStorage();
  }

  async updateProfile(updates: Partial<User>): Promise<{ success: boolean; error?: string }> {
    if (!this.currentUser) {
      return { success: false, error: 'No hay usuario autenticado' };
    }

    try {
      // Update via API
      const response = await fetch(`${API_BASE_URL}/users/${this.currentUser.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error('Failed to update profile');
      }

      const updatedUser = await response.json();
      this.currentUser = updatedUser;
      this.notifyListeners();
      return { success: true };
    } catch (error) {
      console.error('Update profile error:', error);
      return { success: false, error: 'Error al actualizar el perfil' };
    }
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<{ success: boolean; error?: string }> {
    if (!this.currentUser) {
      return { success: false, error: 'No hay usuario autenticado' };
    }

    try {
      // Verify current password and update via API
      const response = await fetch(`${API_BASE_URL}/users/${this.currentUser.id}/change-password`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ currentPassword, newPassword }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { success: false, error: errorData.error || 'Error al cambiar la contraseña' };
      }

      return { success: true };
    } catch (error) {
      console.error('Change password error:', error);
      return { success: false, error: 'Error al cambiar la contraseña' };
    }
  }

  async demoLogin(userType: 'admin' | 'member' = 'admin'): Promise<void> {
    const demoCredentials = {
      admin: { email: '<EMAIL>', password: 'admin123' },
      member: { email: '<EMAIL>', password: 'ana123' }
    };

    const credentials = demoCredentials[userType];
    console.log('🎭 Demo login for:', userType);
    await this.login(credentials);
  }

  async checkUserPersistence(): Promise<void> {
    // No longer using localStorage - user state is managed in memory only
    // In a production app, this would verify the user session with the backend
    if (this.currentUser) {
      console.log('✅ User session active:', this.currentUser.name);
    }
  }
}
