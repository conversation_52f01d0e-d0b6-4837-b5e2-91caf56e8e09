async function testLogin() {
  try {
    console.log('🧪 Testing login API...');
    
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    console.log('📊 Response status:', response.status);
    console.log('📊 Response headers:', Object.fromEntries(response.headers.entries()));

    const data = await response.json();
    console.log('📊 Response data:', data);

    if (response.ok) {
      console.log('✅ Login test successful!');
    } else {
      console.log('❌ Login test failed:', data.error);
    }

  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

testLogin();
