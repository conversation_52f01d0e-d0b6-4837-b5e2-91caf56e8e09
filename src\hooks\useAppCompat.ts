import { useDatabaseApp } from '../context/DatabaseAppContext';
import { useAuth } from '../context/AuthContext';

/**
 * Compatibility hook to bridge old AppContext usage with new DatabaseAppContext
 * This helps components that still reference the old context structure
 */
export function useAppCompat() {
  const databaseApp = useDatabaseApp();
  const { user } = useAuth();

  // Create a compatibility layer that mimics the old AppContext structure
  const compatState = {
    ...databaseApp.state,
    currentUser: user,
    currentBoard: databaseApp.getCurrentBoard(),
    sidebarOpen: databaseApp.state.userPreferences.sidebarOpen,
  };

  const compatDispatch = (action: any) => {
    // Handle common actions that components might still use
    switch (action.type) {
      case 'TOGGLE_SIDEBAR':
        databaseApp.updateUserPreferences({
          sidebarOpen: !databaseApp.state.userPreferences.sidebarOpen
        });
        break;
      case 'SET_CURRENT_BOARD':
        if (action.payload?.id) {
          databaseApp.setCurrentBoard(action.payload.id);
        }
        break;
      case 'ADD_LIST':
      case 'UPDATE_LIST':
      case 'DELETE_LIST':
      case 'ADD_CARD':
      case 'UPDATE_CARD':
      case 'DELETE_CARD':
        // Forward these actions directly to the database app dispatch
        databaseApp.dispatch(action);
        break;
      default:
        console.warn('Unhandled action in compatibility layer:', action.type);
    }
  };

  return {
    state: compatState,
    dispatch: compatDispatch,
    // Expose all DatabaseApp methods for direct access
    ...databaseApp
  };
}

// Also export as useApp for backward compatibility
export const useApp = useAppCompat;
