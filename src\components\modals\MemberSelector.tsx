import React, { useState } from 'react';
import { User } from '../../types';
import { X, Search, Check, UserPlus, Shield, Users } from 'lucide-react';

interface MemberSelectorProps {
  availableMembers: User[];
  assignedMembers: User[];
  onToggleMember: (member: User) => void;
  onClose: () => void;
  currentUser?: User;
  isTeamRestricted?: boolean;
}

export function MemberSelector({
  availableMembers,
  assignedMembers,
  onToggleMember,
  onClose,
  currentUser,
  isTeamRestricted = false
}: MemberSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');

  const filteredMembers = availableMembers.filter(member =>
    member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const isMemberAssigned = (member: User) => {
    return assignedMembers.some(assigned => assigned.id === member.id);
  };

  return (
    <div
      className="absolute top-full mt-2 left-0 w-full max-w-sm bg-white rounded-lg shadow-lg border z-50 p-4 max-h-96 overflow-y-auto"
      onClick={(e) => e.stopPropagation()}
      style={{
        maxHeight: 'calc(100vh - 200px)',
        minWidth: '280px'
      }}
    >
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="font-semibold text-gray-900">Miembros</h3>
          {isTeamRestricted && currentUser?.role !== 'admin' && (
            <p className="text-xs text-gray-500 mt-1 flex items-center">
              <Users className="w-3 h-3 mr-1" />
              Limitado a miembros del equipo
            </p>
          )}
          {currentUser?.role === 'admin' && (
            <p className="text-xs text-blue-600 mt-1 flex items-center">
              <Shield className="w-3 h-3 mr-1" />
              Acceso completo (Admin)
            </p>
          )}
        </div>
        <button onClick={onClose} className="p-1 hover:bg-gray-100 rounded">
          <X className="w-4 h-4" />
        </button>
      </div>

      {/* Search */}
      <div className="relative mb-4">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Buscar miembros..."
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Members List */}
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {filteredMembers.length > 0 ? (
          filteredMembers.map((member) => (
            <div
              key={member.id}
              onClick={() => onToggleMember(member)}
              className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer"
            >
              <img
                src={member.avatar}
                alt={member.name}
                className="w-8 h-8 rounded-full object-cover"
              />
              <div className="flex-1">
                <div className="font-medium text-sm text-gray-900">{member.name}</div>
                <div className="text-xs text-gray-500">{member.email}</div>
              </div>
              <div className="flex items-center space-x-2">
                {member.role === 'admin' && (
                  <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                    Admin
                  </span>
                )}
                {isMemberAssigned(member) && (
                  <Check className="w-4 h-4 text-green-600" />
                )}
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            {searchQuery ? (
              <>
                <Search className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm">No se encontraron miembros que coincidan con "{searchQuery}"</p>
              </>
            ) : availableMembers.length === 0 ? (
              <>
                <Shield className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm font-medium">No hay miembros disponibles</p>
                <p className="text-xs text-gray-400 mt-1">
                  Los miembros disponibles están limitados por las restricciones del equipo
                </p>
              </>
            ) : (
              <>
                <UserPlus className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm">No se encontraron miembros</p>
              </>
            )}
          </div>
        )}
      </div>

      {/* Assigned Members Summary */}
      {assignedMembers.length > 0 && (
        <div className="border-t pt-4 mt-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">
            Miembros asignados ({assignedMembers.length})
          </h4>
          <div className="flex flex-wrap gap-2">
            {assignedMembers.map((member) => (
              <div
                key={member.id}
                className="flex items-center space-x-2 bg-blue-50 text-blue-700 px-2 py-1 rounded-full text-xs"
              >
                <img
                  src={member.avatar}
                  alt={member.name}
                  className="w-4 h-4 rounded-full object-cover"
                />
                <span>{member.name}</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onToggleMember(member);
                  }}
                  className="hover:bg-blue-200 rounded-full p-0.5"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
