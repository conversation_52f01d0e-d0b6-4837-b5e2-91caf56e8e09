import React, { useState } from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { useAuth } from '../context/AuthContext';
import { useNotifications } from '../hooks/useNotifications';
import { useResponsive } from '../hooks/useResponsive';
import { NotificationPanel } from './NotificationPanel';
import { BoardManager } from './BoardManager';
import { UserProfile } from './UserProfile';
import { QuickAddCardModal } from './QuickAddCardModal';
import { MobileNavigation } from './MobileNavigation';
import {
  Menu,
  Search,
  Bell,
  Plus,
  Filter,
  Star,
  Users,
  MoreHorizontal,
  Grid3X3,
  ChevronDown,
  User,
  HardDrive,
  Calendar as CalendarIcon,
  UserPlus,
  PanelLeftOpen,
  PanelLeftClose
} from 'lucide-react';
import { Calendar } from './Calendar';
import { TeamManager } from './TeamManager';
import { BoardMemberManager } from './BoardMemberManager';
import { FilterMenu } from './FilterMenu';
import { UserManagement } from './UserManagement';
import { useUserPermissions } from '../hooks/useUserPermissions';

export function Header() {
  const { state, getCurrentBoard, updateUserPreferences, updateBoard, updateSearchQuery } = useDatabaseApp();
  const { user } = useAuth();
  const { unreadCount } = useNotifications();
  const { canAccessAdmin } = useUserPermissions();
  const { isMobile, isTablet } = useResponsive();
  const [showNotifications, setShowNotifications] = useState(false);
  const [showBoardManager, setShowBoardManager] = useState(false);
  const [showUserProfile, setShowUserProfile] = useState(false);
  const [showQuickAddCard, setShowQuickAddCard] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const [showTeamManager, setShowTeamManager] = useState(false);
  const [showBoardMembers, setShowBoardMembers] = useState(false);
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [showUserManagement, setShowUserManagement] = useState(false);
  const [showSettingsMenu, setShowSettingsMenu] = useState(false);

  const currentBoard = getCurrentBoard();

  return (
    <header className="bg-white border-b shadow-sm">
      <div className="flex items-center justify-between h-16 px-4">
        {/* Left Section */}
        <div className="flex items-center space-x-2 md:space-x-4">
          {/* Mobile Navigation */}
          {isMobile && (
            <MobileNavigation
              onBoardManagerOpen={() => setShowBoardManager(true)}
              onTeamManagerOpen={() => setShowTeamManager(true)}
              onUserManagerOpen={() => setShowUserManagement(true)}
              onCalendarOpen={() => setShowCalendar(true)}
              onNotificationsOpen={() => setShowNotifications(true)}
            />
          )}

          {/* Desktop Sidebar Toggle */}
          {!isMobile && (
            <button
              onClick={() => updateUserPreferences({ sidebarOpen: !state.userPreferences.sidebarOpen })}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              title={state.userPreferences.sidebarOpen ? "Ocultar sidebar" : "Mostrar sidebar"}
            >
              {state.userPreferences.sidebarOpen ? (
                <PanelLeftClose className="w-5 h-5 text-gray-600" />
              ) : (
                <PanelLeftOpen className="w-5 h-5 text-gray-600" />
              )}
            </button>
          )}
          
          {currentBoard ? (
            <div className="flex items-center space-x-2 md:space-x-3">
              <div className={`w-4 h-3 md:w-6 md:h-4 rounded-sm ${currentBoard.background}`} />
              <h1 className="text-lg md:text-xl font-bold text-gray-900 truncate max-w-[150px] md:max-w-none">
                {currentBoard.title}
              </h1>
              {!isMobile && (
                <button
                  onClick={async () => {
                    try {
                      await updateBoard(currentBoard.id, { isFavorite: !currentBoard.isFavorite });
                    } catch (error) {
                      console.error('Error updating board favorite status:', error);
                    }
                  }}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  <Star className={`w-4 h-4 ${currentBoard.isFavorite ? 'text-yellow-500 fill-current' : 'text-gray-400 hover:text-yellow-500'}`} />
                </button>
              )}
              <button
                onClick={() => setShowBoardManager(true)}
                className="flex items-center space-x-1 p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
              >
                <ChevronDown className="w-3 h-3 md:w-4 md:h-4" />
              </button>
            </div>
          ) : (
            <button
              onClick={() => setShowBoardManager(true)}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
            >
              <Grid3X3 className="w-4 h-4 md:w-5 md:h-5" />
              <span className="font-medium text-sm md:text-base hidden sm:inline">Seleccionar Tablero</span>
              <span className="font-medium text-sm md:text-base sm:hidden">Tablero</span>
            </button>
          )}
        </div>

        {/* Center Section - Search */}
        <div className="flex-1 max-w-md mx-2 md:mx-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder={isMobile ? "Buscar..." : "Buscar tarjetas, miembros, etiquetas..."}
              value={state.searchQuery || ''}
              onChange={(e) => updateSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm md:text-base"
            />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-1 md:space-x-3">
          {/* Filter Button - Hidden on mobile */}
          {!isMobile && (
            <div className="relative">
              <button
                onClick={() => setShowFilterMenu(!showFilterMenu)}
                disabled={!currentBoard}
                className={`p-2 hover:bg-gray-100 rounded-lg transition-colors relative ${
                  !currentBoard
                    ? 'text-gray-400 cursor-not-allowed'
                    : state.cardFilter !== 'all'
                      ? 'text-blue-600 bg-blue-50'
                      : 'text-gray-600'
                }`}
                title={currentBoard ? "Filtrar tarjetas" : "Selecciona un tablero"}
              >
                <Filter className="w-5 h-5" />
                {state.cardFilter !== 'all' && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full"></span>
                )}
              </button>

              <FilterMenu
                isOpen={showFilterMenu}
                onClose={() => setShowFilterMenu(false)}
              />
            </div>
          )}

          {/* Board Members - Simplified on mobile */}
          {currentBoard && currentBoard.members && currentBoard.members.length > 0 && !isMobile ? (
            <div className="flex items-center space-x-1">
              {/* Member Avatars */}
              <div className="flex -space-x-2">
                {currentBoard.members.slice(0, isTablet ? 2 : 3).map((member) => (
                  <img
                    key={member.id}
                    src={member.avatar}
                    alt={member.name}
                    className="w-7 h-7 md:w-8 md:h-8 rounded-full border-2 border-white object-cover hover:z-10 hover:scale-110 transition-transform cursor-pointer"
                    title={`${member.name} (${member.role})`}
                    onClick={() => setShowBoardMembers(true)}
                  />
                ))}
                {currentBoard.members.length > (isTablet ? 2 : 3) && (
                  <div
                    className="w-7 h-7 md:w-8 md:h-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-xs font-medium text-gray-600 cursor-pointer hover:bg-gray-300 transition-colors"
                    onClick={() => setShowBoardMembers(true)}
                    title={`+${currentBoard.members.length - (isTablet ? 2 : 3)} miembros más`}
                  >
                    +{currentBoard.members.length - (isTablet ? 2 : 3)}
                  </div>
                )}
              </div>

              {/* Add Member Button */}
              <button
                onClick={() => setShowBoardMembers(true)}
                className="p-1 hover:bg-gray-100 rounded-full text-gray-500 hover:text-gray-700 transition-colors"
                title="Gestionar miembros del tablero"
              >
                <Users className="w-4 h-4" />
              </button>
            </div>
          ) : (
            !isMobile && (
              <button
                onClick={() => setShowBoardMembers(true)}
                disabled={!currentBoard}
                className="p-2 hover:bg-gray-100 rounded-lg text-gray-600 disabled:text-gray-400 disabled:cursor-not-allowed"
                title={currentBoard ? "Agregar miembros al tablero" : "Selecciona un tablero"}
              >
                <Users className="w-5 h-5" />
              </button>
            )
          )}

          {/* Teams - Hidden on mobile */}
          {!isMobile && (
            <button
              onClick={() => setShowTeamManager(true)}
              className="p-2 hover:bg-gray-100 rounded-lg text-gray-600"
              title="Gestionar equipos"
            >
              <Users className="w-5 h-5" />
            </button>
          )}

          {/* Calendar - Hidden on mobile */}
          {!isMobile && (
            <button
              onClick={() => setShowCalendar(true)}
              className="p-2 hover:bg-gray-100 rounded-lg text-gray-600"
              title="Ver calendario de tareas"
            >
              <CalendarIcon className="w-5 h-5" />
            </button>
          )}

          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="p-2 hover:bg-gray-100 rounded-lg text-gray-600 relative"
            >
              <Bell className="w-4 h-4 md:w-5 md:h-5" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 md:w-5 md:h-5 flex items-center justify-center">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </button>

            <NotificationPanel
              isOpen={showNotifications}
              onClose={() => setShowNotifications(false)}
            />
          </div>

          {/* Quick Add Card Button */}
          <button
            onClick={() => setShowQuickAddCard(true)}
            disabled={!currentBoard}
            className="flex items-center space-x-1 md:space-x-2 bg-blue-600 text-white px-2 md:px-3 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors min-w-[44px]"
            title={!currentBoard ? "Selecciona un tablero para agregar tarjetas" : "Agregar nueva tarjeta"}
          >
            <Plus className="w-4 h-4" />
            <span className="hidden sm:inline text-sm md:text-base">Agregar Tarjeta</span>
          </button>

          {/* User Profile */}
          <button
            onClick={() => setShowUserProfile(true)}
            className="flex items-center space-x-1 md:space-x-2 p-1 md:p-2 hover:bg-gray-100 rounded-lg text-gray-600 min-w-[44px]"
          >
            <img
              src={user?.avatar}
              alt={user?.name}
              className="w-6 h-6 md:w-7 md:h-7 rounded-full object-cover"
            />
            <span className="hidden lg:block text-sm font-medium">{user?.name}</span>
          </button>

          {/* Storage Manager - Removed (no longer needed) */}

          {/* Settings Menu */}
          <div className="relative">
            <button
              onClick={() => setShowSettingsMenu(!showSettingsMenu)}
              className="p-2 hover:bg-gray-100 rounded-lg text-gray-600"
              title="Configuración y Administración"
            >
              <MoreHorizontal className="w-5 h-5" />
            </button>

            {showSettingsMenu && (
              <>
                {/* Backdrop */}
                <div
                  className="fixed inset-0 z-40"
                  onClick={() => setShowSettingsMenu(false)}
                />

                {/* Menu */}
                <div className="absolute top-full right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                  <div className="py-2">
                    {/* Only show administration section for admin users */}
                    {canAccessAdmin() && (
                      <>
                        <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100">
                          Administración
                        </div>

                        <button
                          onClick={() => {
                            setShowUserManagement(true);
                            setShowSettingsMenu(false);
                          }}
                          className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          <Users className="w-4 h-4 mr-3 text-blue-600" />
                          <div className="text-left">
                            <div className="font-medium">Gestión de Usuarios</div>
                            <div className="text-xs text-gray-500">Gestionar usuarios, roles y permisos</div>
                          </div>
                        </button>
                      </>
                    )}

                    {/* Storage Manager - Removed (no longer needed) */}

                    <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100 mt-2">
                      Equipos y Colaboración
                    </div>

                    <button
                      onClick={() => {
                        setShowTeamManager(true);
                        setShowSettingsMenu(false);
                      }}
                      className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      <Users className="w-4 h-4 mr-3 text-purple-600" />
                      <div className="text-left">
                        <div className="font-medium">Gestión de Equipos</div>
                        <div className="text-xs text-gray-500">Gestionar equipos e invitaciones</div>
                      </div>
                    </button>

                    <button
                      onClick={() => {
                        setShowBoardMembers(true);
                        setShowSettingsMenu(false);
                      }}
                      className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      <UserPlus className="w-4 h-4 mr-3 text-orange-600" />
                      <div className="text-left">
                        <div className="font-medium">Miembros del Tablero</div>
                        <div className="text-xs text-gray-500">Gestionar acceso al tablero</div>
                      </div>
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Board Manager Modal */}
      <BoardManager
        isOpen={showBoardManager}
        onClose={() => setShowBoardManager(false)}
      />

      {/* User Profile Modal */}
      <UserProfile
        isOpen={showUserProfile}
        onClose={() => setShowUserProfile(false)}
      />

      {/* Storage Manager Modal - Removed (no longer needed) */}

      {/* Quick Add Card Modal */}
      <QuickAddCardModal
        isOpen={showQuickAddCard}
        onClose={() => setShowQuickAddCard(false)}
      />

      {/* Team Manager Modal */}
      <TeamManager
        isOpen={showTeamManager}
        onClose={() => setShowTeamManager(false)}
      />

      {/* Calendar Modal */}
      {showCalendar && (
        <Calendar
          onClose={() => setShowCalendar(false)}
        />
      )}

      {/* Board Member Manager Modal */}
      <BoardMemberManager
        isOpen={showBoardMembers}
        onClose={() => setShowBoardMembers(false)}
      />

      {/* User Management Modal - Only for admin users */}
      {canAccessAdmin() && (
        <UserManagement
          isOpen={showUserManagement}
          onClose={() => setShowUserManagement(false)}
        />
      )}
    </header>
  );
}