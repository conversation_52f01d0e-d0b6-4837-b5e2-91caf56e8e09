import React, { useState } from 'react';
import { User, UserPermissions as UserPermissionsType } from '../types';
import { 
  Shield, 
  Crown, 
  Eye, 
  Check, 
  X, 
  Settings,
  Users,
  FileText,
  Trash2,
  Archive,
  UserPlus,
  BarChart3,
  Plus
} from 'lucide-react';

interface UserPermissionsProps {
  users: User[];
}

export function UserPermissions({ users }: UserPermissionsProps) {
  const [selectedRole, setSelectedRole] = useState<User['role'] | 'all'>('all');
  // UserService no longer needed - using API directly

  const getRoleIcon = (role: User['role']) => {
    switch (role) {
      case 'admin':
        return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'member':
        return <Shield className="w-4 h-4 text-blue-500" />;
      case 'observer':
        return <Eye className="w-4 h-4 text-gray-500" />;
    }
  };

  const getRoleBadgeColor = (role: User['role']) => {
    switch (role) {
      case 'admin':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'member':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'observer':
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPermissionIcon = (permission: keyof UserPermissionsType) => {
    switch (permission) {
      case 'canCreateBoards':
        return <Plus className="w-4 h-4" />;
      case 'canEditCards':
        return <FileText className="w-4 h-4" />;
      case 'canDeleteCards':
        return <Trash2 className="w-4 h-4" />;
      case 'canManageUsers':
        return <Users className="w-4 h-4" />;
      case 'canViewReports':
        return <BarChart3 className="w-4 h-4" />;
      case 'canManageSettings':
        return <Settings className="w-4 h-4" />;
      case 'canInviteUsers':
        return <UserPlus className="w-4 h-4" />;
      case 'canArchiveCards':
        return <Archive className="w-4 h-4" />;
      default:
        return <Shield className="w-4 h-4" />;
    }
  };

  const getPermissionLabel = (permission: keyof UserPermissionsType) => {
    switch (permission) {
      case 'canCreateBoards':
        return 'Crear Tableros';
      case 'canEditCards':
        return 'Editar Tarjetas';
      case 'canDeleteCards':
        return 'Eliminar Tarjetas';
      case 'canManageUsers':
        return 'Gestionar Usuarios';
      case 'canViewReports':
        return 'Ver Reportes';
      case 'canManageSettings':
        return 'Gestionar Configuración';
      case 'canInviteUsers':
        return 'Invitar Usuarios';
      case 'canArchiveCards':
        return 'Archivar Tarjetas';
      default:
        return permission;
    }
  };

  const getPermissionDescription = (permission: keyof UserPermissionsType) => {
    switch (permission) {
      case 'canCreateBoards':
        return 'Crear nuevos tableros y gestionar configuración de tableros';
      case 'canEditCards':
        return 'Editar contenido, descripciones y propiedades de tarjetas';
      case 'canDeleteCards':
        return 'Eliminar tarjetas y removerlas permanentemente';
      case 'canManageUsers':
        return 'Agregar, editar y remover usuarios del sistema';
      case 'canViewReports':
        return 'Acceder a analíticas y reportes de rendimiento';
      case 'canManageSettings':
        return 'Configurar ajustes del sistema y preferencias';
      case 'canInviteUsers':
        return 'Enviar invitaciones a nuevos usuarios';
      case 'canArchiveCards':
        return 'Archivar y restaurar tarjetas';
      default:
        return 'Descripción del permiso';
    }
  };

  const filteredUsers = selectedRole === 'all' 
    ? users 
    : users.filter(user => user.role === selectedRole);

  const roleStats = {
    admin: users.filter(u => u.role === 'admin').length,
    member: users.filter(u => u.role === 'member').length,
    observer: users.filter(u => u.role === 'observer').length
  };

  // Get permissions for each role
  const adminPermissions = userService.getUserPermissions({ role: 'admin' } as User);
  const memberPermissions = userService.getUserPermissions({ role: 'member' } as User);
  const observerPermissions = userService.getUserPermissions({ role: 'observer' } as User);

  const allPermissions: (keyof UserPermissionsType)[] = [
    'canCreateBoards',
    'canEditCards',
    'canDeleteCards',
    'canManageUsers',
    'canViewReports',
    'canManageSettings',
    'canInviteUsers',
    'canArchiveCards'
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Roles y Permisos de Usuario</h3>
          <p className="text-sm text-gray-600">Gestiona roles de usuario y sus permisos asociados</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <label className="text-sm font-medium text-gray-700">Filtrar por rol:</label>
          <select
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value as any)}
            className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Todos los Roles</option>
            <option value="admin">Administrador</option>
            <option value="member">Miembro</option>
            <option value="observer">Observador</option>
          </select>
        </div>
      </div>

      {/* Role Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
          <div className="flex items-center">
            <Crown className="w-5 h-5 text-yellow-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-yellow-600">Administradores</p>
              <p className="text-2xl font-bold text-yellow-900">{roleStats.admin}</p>
              <p className="text-sm text-yellow-700">Acceso completo al sistema</p>
            </div>
          </div>
        </div>
        
        <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
          <div className="flex items-center">
            <Shield className="w-5 h-5 text-blue-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-blue-600">Miembros</p>
              <p className="text-2xl font-bold text-blue-900">{roleStats.member}</p>
              <p className="text-sm text-blue-700">Acceso estándar</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-50 border border-gray-200 p-4 rounded-lg">
          <div className="flex items-center">
            <Eye className="w-5 h-5 text-gray-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-gray-600">Observadores</p>
              <p className="text-2xl font-bold text-gray-900">{roleStats.observer}</p>
              <p className="text-sm text-gray-700">Acceso de solo lectura</p>
            </div>
          </div>
        </div>
      </div>

      {/* Permissions Matrix */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h4 className="text-lg font-semibold text-gray-900">Matriz de Permisos</h4>
          <p className="text-sm text-gray-600">Resumen de permisos para cada rol</p>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Permiso
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <div className="flex items-center justify-center">
                    <Crown className="w-4 h-4 text-yellow-500 mr-1" />
                    Administrador
                  </div>
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <div className="flex items-center justify-center">
                    <Shield className="w-4 h-4 text-blue-500 mr-1" />
                    Miembro
                  </div>
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <div className="flex items-center justify-center">
                    <Eye className="w-4 h-4 text-gray-500 mr-1" />
                    Observador
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {allPermissions.map((permission) => (
                <tr key={permission} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 mt-1">
                        {getPermissionIcon(permission)}
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">
                          {getPermissionLabel(permission)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {getPermissionDescription(permission)}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-center">
                    {adminPermissions[permission] ? (
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    ) : (
                      <X className="w-5 h-5 text-red-500 mx-auto" />
                    )}
                  </td>
                  <td className="px-6 py-4 text-center">
                    {memberPermissions[permission] ? (
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    ) : (
                      <X className="w-5 h-5 text-red-500 mx-auto" />
                    )}
                  </td>
                  <td className="px-6 py-4 text-center">
                    {observerPermissions[permission] ? (
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    ) : (
                      <X className="w-5 h-5 text-red-500 mx-auto" />
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Users by Role */}
      <div className="bg-white border border-gray-200 rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h4 className="text-lg font-semibold text-gray-900">
            Usuarios {selectedRole !== 'all' && `con rol de ${selectedRole === 'admin' ? 'administrador' : selectedRole === 'member' ? 'miembro' : 'observador'}`}
          </h4>
          <p className="text-sm text-gray-600">
            {filteredUsers.length} usuario{filteredUsers.length !== 1 ? 's' : ''} encontrado{filteredUsers.length !== 1 ? 's' : ''}
          </p>
        </div>
        
        <div className="p-6">
          {filteredUsers.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-2">
                <Users className="w-12 h-12 mx-auto" />
              </div>
              <p className="text-gray-500">No se encontraron usuarios con el rol seleccionado</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredUsers.map((user) => {
                const permissions = userService.getUserPermissions(user);
                const permissionCount = Object.values(permissions).filter(Boolean).length;
                
                return (
                  <div key={user.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                          <span className="text-white font-medium text-sm">
                            {user.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      </div>
                      <div className="ml-3 flex-1">
                        <div className="text-sm font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                    
                    <div className="mb-3">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getRoleBadgeColor(user.role)}`}>
                        {getRoleIcon(user.role)}
                        <span className="ml-1 capitalize">{user.role}</span>
                      </span>
                    </div>
                    
                    <div className="text-sm text-gray-600">
                      <div className="flex items-center justify-between">
                        <span>Permisos:</span>
                        <span className="font-medium">{permissionCount}/{allPermissions.length}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                        <div 
                          className="bg-blue-500 h-1.5 rounded-full"
                          style={{ width: `${(permissionCount / allPermissions.length) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    <div className="mt-3">
                      <div className="text-xs text-gray-500 mb-1">Permisos clave:</div>
                      <div className="flex flex-wrap gap-1">
                        {allPermissions.slice(0, 3).map((permission) => (
                          <span
                            key={permission}
                            className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs ${
                              permissions[permission] 
                                ? 'bg-green-100 text-green-700' 
                                : 'bg-red-100 text-red-700'
                            }`}
                          >
                            {permissions[permission] ? (
                              <Check className="w-2 h-2 mr-1" />
                            ) : (
                              <X className="w-2 h-2 mr-1" />
                            )}
                            {getPermissionLabel(permission)}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
