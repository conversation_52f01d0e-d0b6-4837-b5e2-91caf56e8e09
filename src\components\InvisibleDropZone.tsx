import React from 'react';

interface InvisibleDropZoneProps {
  position: number;
  onDragOver: (e: React.DragEvent, targetId: string, targetType?: 'card' | 'list') => void;
  onDragLeave: () => void;
  onDrop: (e: React.DragEvent, targetId: string) => void;
  isActive: boolean;
}

export function InvisibleDropZone({ 
  position, 
  onDragOver, 
  onDragLeave, 
  onDrop, 
  isActive 
}: InvisibleDropZoneProps) {
  if (!isActive) {
    return null;
  }

  return (
    <div
      className="absolute inset-0 z-10"
      onDragOver={(e) => onDragOver(e, position.toString(), 'list')}
      onDragLeave={onDragLeave}
      onDrop={(e) => onDrop(e, position.toString())}
      style={{ pointerEvents: 'auto' }}
    />
  );
}
