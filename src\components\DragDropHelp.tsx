import React, { useState } from 'react';
import { HelpCircle, X, Move, GripVertical } from 'lucide-react';

export function DragDropHelp() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      {/* Help Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-40"
        title="Ayuda sobre arrastrar y soltar"
      >
        <HelpCircle className="w-6 h-6" />
      </button>

      {/* Help Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md mx-4 max-h-96 overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Arrastrar y Soltar
              </h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              {/* Card Drag and Drop */}
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-medium text-gray-900 flex items-center space-x-2">
                  <Move className="w-4 h-4 text-blue-500" />
                  <span>Mover Tarjetas</span>
                </h4>
                <p className="text-sm text-gray-600 mt-1">
                  Arrastra cualquier tarjeta y suéltala en otra lista o reordénala dentro de la misma lista.
                  Funciona con detección inteligente de colisiones y animaciones suaves.
                </p>
              </div>

              {/* List Drag and Drop */}
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-medium text-gray-900 flex items-center space-x-2">
                  <GripVertical className="w-4 h-4 text-green-500" />
                  <span>Mover Listas</span>
                </h4>
                <p className="text-sm text-gray-600 mt-1">
                  Arrastra una lista completa por su cabecera (icono de agarre).
                  Verás una vista previa en tiempo real con rotación 3D durante el arrastre.
                  Suéltala en cualquier posición para reordenarla.
                </p>
              </div>

              {/* New Features */}
              <div className="border-l-4 border-purple-500 pl-4">
                <h4 className="font-medium text-gray-900 flex items-center space-x-2">
                  <span className="w-4 h-4 bg-purple-500 rounded-full flex items-center justify-center text-white text-xs">✨</span>
                  <span>Nuevas Funcionalidades</span>
                </h4>
                <p className="text-sm text-gray-600 mt-1">
                  Powered by @dnd-kit: detección de colisiones avanzada, soporte para teclado,
                  animaciones fluidas, y mejor accesibilidad.
                </p>
              </div>

              {/* Tips */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <h4 className="font-medium text-yellow-800 mb-2">💡 Consejos</h4>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• Vista previa en tiempo real durante el arrastre</li>
                  <li>• Detección inteligente de colisiones</li>
                  <li>• Soporte completo para teclado (Tab + Espacio)</li>
                  <li>• Animaciones suaves y profesionales</li>
                  <li>• Los cambios se guardan automáticamente</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                onClick={() => setIsOpen(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Entendido
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
