# Script to kill processes using port 3001
Write-Host "🔍 Finding processes using port 3001..."

try {
    $connections = Get-NetTCPConnection -LocalPort 3001 -ErrorAction SilentlyContinue
    
    if ($connections) {
        foreach ($conn in $connections) {
            $processId = $conn.OwningProcess
            $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
            
            if ($process) {
                Write-Host "🎯 Found process: $($process.ProcessName) (PID: $processId)"
                Write-Host "🔪 Killing process $processId..."
                Stop-Process -Id $processId -Force
                Write-Host "✅ Process killed successfully"
            }
        }
    } else {
        Write-Host "✅ No processes found using port 3001"
    }
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)"
}

Write-Host "🏁 Done"
