async function testCompleteUserFlow() {
  console.log('🧪 Testing complete user creation and login flow...\n');

  const testUser = {
    username: 'testuser',
    name: '<PERSON><PERSON><PERSON>rue<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567899',
    password: 'test123',
    role: 'member'
  };

  try {
    // Step 1: Create user via API
    console.log('📝 Step 1: Creating user via API...');
    const createResponse = await fetch('http://localhost:3001/api/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser)
    });

    if (!createResponse.ok) {
      const errorData = await createResponse.json();
      console.log('❌ Create user failed:', errorData);
      return;
    }

    const createdUser = await createResponse.json();
    console.log('✅ User created successfully:', {
      id: createdUser.id,
      name: createdUser.name,
      email: createdUser.email,
      role: createdUser.role,
      isActive: createdUser.isActive
    });

    // Step 2: Verify user exists in database
    console.log('\n🔍 Step 2: Verifying user exists in database...');
    const getUserResponse = await fetch('http://localhost:3001/api/users');
    const allUsers = await getUserResponse.json();
    const foundUser = allUsers.find(u => u.email === testUser.email);
    
    if (foundUser) {
      console.log('✅ User found in database:', {
        id: foundUser.id,
        name: foundUser.name,
        email: foundUser.email,
        password: foundUser.password,
        isActive: foundUser.isActive
      });
    } else {
      console.log('❌ User not found in database');
      return;
    }

    // Step 3: Test login with new user credentials
    console.log('\n🔐 Step 3: Testing login with new user credentials...');
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });

    console.log('📊 Login response status:', loginResponse.status);

    if (!loginResponse.ok) {
      const errorData = await loginResponse.json();
      console.log('❌ Login failed:', errorData);
      return;
    }

    const loginData = await loginResponse.json();
    if (loginData.success) {
      console.log('✅ Login successful!', {
        userId: loginData.user.id,
        name: loginData.user.name,
        email: loginData.user.email,
        role: loginData.user.role
      });
    } else {
      console.log('❌ Login failed:', loginData.error);
    }

    // Step 4: Clean up - delete test user
    console.log('\n🧹 Step 4: Cleaning up test user...');
    const deleteResponse = await fetch(`http://localhost:3001/api/users/${createdUser.id}`, {
      method: 'DELETE'
    });

    if (deleteResponse.ok) {
      console.log('✅ Test user deleted successfully');
    } else {
      console.log('⚠️ Failed to delete test user');
    }

    console.log('\n🎉 Complete user flow test finished!');

  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

testCompleteUserFlow();
