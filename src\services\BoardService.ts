import { prisma, handlePrismaError } from '../lib/prisma';
import { Board, BoardMember, List, Label } from '../types';
import { BoardRole, BoardVisibility } from '@prisma/client';

export class BoardService {
  // Create a new board
  static async createBoard(boardData: Omit<Board, 'id' | 'createdAt' | 'updatedAt'>, creatorId: string): Promise<Board> {
    try {
      const board = await prisma.board.create({
        data: {
          title: boardData.title,
          description: boardData.description,
          background: boardData.background,
          isPublic: boardData.isPublic,
          isFavorite: boardData.isFavorite,
          visibility: boardData.visibility?.toUpperCase() as BoardVisibility || 'PRIVATE',
          teamId: boardData.teamId,
          createdById: creatorId,
          members: {
            create: {
              userId: creatorId,
              role: 'ADMIN',
            }
          }
        },
        include: {
          members: {
            include: {
              user: true
            }
          },
          lists: {
            include: {
              cards: {
                include: {
                  labels: {
                    include: {
                      label: true
                    }
                  },
                  assignedMembers: {
                    include: {
                      user: true
                    }
                  },
                  checklist: true,
                  comments: {
                    include: {
                      author: true
                    }
                  },
                  attachments: true,
                }
              }
            }
          },
          labels: true,
          team: true,
        }
      });

      return this.convertPrismaBoardToBoard(board);
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Get all boards
  static async getAllBoards(): Promise<Board[]> {
    try {
      const boards = await prisma.board.findMany({
        include: {
          members: {
            include: {
              user: true
            }
          },
          lists: {
            include: {
              cards: {
                include: {
                  labels: {
                    include: {
                      label: true
                    }
                  },
                  assignedMembers: {
                    include: {
                      user: true
                    }
                  },
                  checklist: true,
                  comments: {
                    include: {
                      author: true
                    }
                  },
                  attachments: true,
                }
              }
            }
          },
          labels: true,
          team: true,
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return boards.map(this.convertPrismaBoardToBoard);
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Get board by ID
  static async getBoardById(id: string): Promise<Board | null> {
    try {
      const board = await prisma.board.findUnique({
        where: { id },
        include: {
          members: {
            include: {
              user: true
            }
          },
          lists: {
            include: {
              cards: {
                include: {
                  labels: {
                    include: {
                      label: true
                    }
                  },
                  assignedMembers: {
                    include: {
                      user: true
                    }
                  },
                  checklist: true,
                  comments: {
                    include: {
                      author: true
                    }
                  },
                  attachments: true,
                }
              }
            }
          },
          labels: true,
          team: true,
        }
      });

      return board ? this.convertPrismaBoardToBoard(board) : null;
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Update board
  static async updateBoard(id: string, boardData: Partial<Board>): Promise<Board> {
    try {
      const board = await prisma.board.update({
        where: { id },
        data: {
          ...(boardData.title && { title: boardData.title }),
          ...(boardData.description && { description: boardData.description }),
          ...(boardData.background && { background: boardData.background }),
          ...(boardData.isPublic !== undefined && { isPublic: boardData.isPublic }),
          ...(boardData.isFavorite !== undefined && { isFavorite: boardData.isFavorite }),
          ...(boardData.visibility && { visibility: boardData.visibility.toUpperCase() as BoardVisibility }),
          ...(boardData.teamId && { teamId: boardData.teamId }),
        },
        include: {
          members: {
            include: {
              user: true
            }
          },
          lists: {
            include: {
              cards: {
                include: {
                  labels: {
                    include: {
                      label: true
                    }
                  },
                  assignedMembers: {
                    include: {
                      user: true
                    }
                  },
                  checklist: true,
                  comments: {
                    include: {
                      author: true
                    }
                  },
                  attachments: true,
                }
              }
            }
          },
          labels: true,
          team: true,
        }
      });

      return this.convertPrismaBoardToBoard(board);
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Delete board
  static async deleteBoard(id: string): Promise<void> {
    try {
      await prisma.board.delete({
        where: { id }
      });
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Add member to board
  static async addMemberToBoard(boardId: string, userId: string, role: string = 'member'): Promise<Board> {
    try {
      await prisma.boardMember.create({
        data: {
          boardId,
          userId,
          role: role.toUpperCase() as BoardRole,
        }
      });

      return this.getBoardById(boardId) as Promise<Board>;
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Remove member from board
  static async removeMemberFromBoard(boardId: string, userId: string): Promise<Board> {
    try {
      await prisma.boardMember.delete({
        where: {
          boardId_userId: {
            boardId,
            userId
          }
        }
      });

      return this.getBoardById(boardId) as Promise<Board>;
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Update member role
  static async updateMemberRole(boardId: string, userId: string, role: string): Promise<Board> {
    try {
      await prisma.boardMember.update({
        where: {
          boardId_userId: {
            boardId,
            userId
          }
        },
        data: {
          role: role.toUpperCase() as BoardRole
        }
      });

      return this.getBoardById(boardId) as Promise<Board>;
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Get boards for user
  static async getBoardsForUser(userId: string): Promise<Board[]> {
    try {
      const boards = await prisma.board.findMany({
        where: {
          OR: [
            {
              members: {
                some: {
                  userId
                }
              }
            },
            {
              createdById: userId
            }
          ]
        },
        include: {
          members: {
            include: {
              user: true
            }
          },
          lists: {
            include: {
              cards: {
                include: {
                  labels: {
                    include: {
                      label: true
                    }
                  },
                  assignedMembers: {
                    include: {
                      user: true
                    }
                  },
                  checklist: true,
                  comments: {
                    include: {
                      author: true
                    }
                  },
                  attachments: true,
                }
              }
            }
          },
          labels: true,
          team: true,
        }
      });

      return boards.map(this.convertPrismaBoardToBoard);
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Convert Prisma board to our Board type
  private static convertPrismaBoardToBoard(prismaBoard: any): Board {
    return {
      id: prismaBoard.id,
      title: prismaBoard.title,
      description: prismaBoard.description || '',
      background: prismaBoard.background,
      isPublic: prismaBoard.isPublic,
      isFavorite: prismaBoard.isFavorite,
      visibility: prismaBoard.visibility.toLowerCase(),
      teamId: prismaBoard.teamId,
      members: prismaBoard.members.map((member: any): BoardMember => ({
        id: member.user.id,
        name: member.user.name,
        email: member.user.email,
        username: member.user.username,
        avatar: member.user.avatar,
        role: member.role.toLowerCase(),
        joinedAt: new Date(member.joinedAt),
        invitedBy: member.invitedBy,
      })),
      lists: prismaBoard.lists.map((list: any): List => ({
        id: list.id,
        title: list.title,
        position: list.position,
        archived: list.archived,
        cards: list.cards.map((card: any) => ({
          id: card.id,
          title: card.title,
          description: card.description || '',
          position: card.position,
          archived: card.archived,
          cover: card.cover,
          dueDate: card.dueDate ? new Date(card.dueDate) : undefined,
          labels: card.labels.map((cl: any) => ({
            id: cl.label.id,
            name: cl.label.name,
            color: cl.label.color,
          })),
          assignedMembers: card.assignedMembers.map((am: any) => am.user.id),
          checklist: card.checklist.map((item: any) => ({
            id: item.id,
            text: item.text,
            completed: item.completed,
          })),
          comments: card.comments.map((comment: any) => ({
            id: comment.id,
            text: comment.text,
            author: {
              id: comment.author.id,
              name: comment.author.name,
              avatar: comment.author.avatar,
            },
            createdAt: new Date(comment.createdAt),
          })),
          attachments: card.attachments.map((attachment: any) => ({
            id: attachment.id,
            name: attachment.name,
            url: attachment.url,
            type: attachment.type.toLowerCase(),
            size: attachment.size,
          })),
          createdAt: new Date(card.createdAt),
          updatedAt: new Date(card.updatedAt),
        }))
      })),
      labels: prismaBoard.labels.map((label: any): Label => ({
        id: label.id,
        name: label.name,
        color: label.color,
      })),
      createdAt: new Date(prismaBoard.createdAt),
      updatedAt: new Date(prismaBoard.updatedAt),
    };
  }
}
