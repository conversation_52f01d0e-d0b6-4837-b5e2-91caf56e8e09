import fs from 'fs';
import path from 'path';

console.log('🔍 Diagnóstico completo de errores en la aplicación Trello...\n');

// 1. Verificar archivos críticos
const criticalFiles = [
  'src/App.tsx',
  'src/context/DatabaseAppContext.tsx',
  'src/context/AuthContext.tsx',
  'src/components/BoardManager.tsx',
  'src/components/Header.tsx',
  'src/components/Sidebar.tsx',
  'src/components/DndKitBoard.tsx',
  'src/components/ErrorBoundary.tsx'
];

console.log('📁 Verificando archivos críticos...');
criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - Existe`);
  } else {
    console.log(`❌ ${file} - No encontrado`);
  }
});

// 2. Buscar patrones problemáticos
console.log('\n🔍 Buscando patrones problemáticos...');

function searchInFile(filePath, patterns) {
  if (!fs.existsSync(filePath)) return [];
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const issues = [];
  
  lines.forEach((line, index) => {
    patterns.forEach(pattern => {
      if (line.includes(pattern.search)) {
        issues.push({
          file: filePath,
          line: index + 1,
          content: line.trim(),
          issue: pattern.issue
        });
      }
    });
  });
  
  return issues;
}

const problematicPatterns = [
  { search: 'dispatch({ type:', issue: 'Uso directo de dispatch en lugar de funciones del contexto' },
  { search: '.toLocaleDateString()', issue: 'Posible error de fecha sin conversión' },
  { search: 'localStorage', issue: 'Uso de localStorage (debería estar eliminado)' },
  { search: 'useApp()', issue: 'Uso del contexto antiguo en lugar de useDatabaseApp()' },
  { search: 'undefined.', issue: 'Posible acceso a propiedad de undefined' },
  { search: 'null.', issue: 'Posible acceso a propiedad de null' },
  { search: 'console.error', issue: 'Error manejado (revisar si es crítico)' }
];

let allIssues = [];
criticalFiles.forEach(file => {
  const issues = searchInFile(file, problematicPatterns);
  allIssues = allIssues.concat(issues);
});

if (allIssues.length === 0) {
  console.log('✅ No se encontraron patrones problemáticos');
} else {
  console.log(`❌ Se encontraron ${allIssues.length} posibles problemas:`);
  
  const groupedIssues = allIssues.reduce((acc, issue) => {
    if (!acc[issue.file]) acc[issue.file] = [];
    acc[issue.file].push(issue);
    return acc;
  }, {});
  
  Object.entries(groupedIssues).forEach(([file, issues]) => {
    console.log(`\n📄 ${file}:`);
    issues.forEach(issue => {
      console.log(`   Línea ${issue.line}: ${issue.issue}`);
      console.log(`   Código: ${issue.content}`);
    });
  });
}

// 3. Verificar dependencias críticas
console.log('\n📦 Verificando dependencias críticas...');

const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const criticalDeps = [
  'react',
  'react-dom',
  '@dnd-kit/core',
  '@dnd-kit/sortable',
  'lucide-react'
];

criticalDeps.forEach(dep => {
  if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
    console.log(`✅ ${dep} - Instalado`);
  } else {
    console.log(`❌ ${dep} - No encontrado`);
  }
});

// 4. Verificar estructura de carpetas
console.log('\n📁 Verificando estructura de carpetas...');

const requiredDirs = [
  'src/components',
  'src/context',
  'src/hooks',
  'src/services',
  'src/types',
  'src/utils',
  'server'
];

requiredDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`✅ ${dir} - Existe`);
  } else {
    console.log(`❌ ${dir} - No encontrado`);
  }
});

// 5. Verificar archivos de configuración
console.log('\n⚙️ Verificando archivos de configuración...');

const configFiles = [
  'package.json',
  'tsconfig.json',
  'vite.config.ts',
  'tailwind.config.js'
];

configFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - Existe`);
  } else {
    console.log(`❌ ${file} - No encontrado`);
  }
});

// 6. Recomendaciones
console.log('\n💡 Recomendaciones para solucionar pantallas blancas:');
console.log('1. Verificar que todos los componentes tengan Error Boundaries');
console.log('2. Asegurar que no se use dispatch directamente');
console.log('3. Validar que todas las fechas se conviertan correctamente');
console.log('4. Comprobar que no haya referencias a localStorage');
console.log('5. Verificar que todos los hooks usen useDatabaseApp()');
console.log('6. Asegurar que las props requeridas no sean undefined');

console.log('\n🎯 Diagnóstico completado.');
