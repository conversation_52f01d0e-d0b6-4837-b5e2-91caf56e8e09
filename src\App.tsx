import React, { useEffect, useState } from 'react';
import { AuthProvider, useAuth } from './context/AuthContext';
import { DatabaseAppProvider, useDatabaseApp } from './context/DatabaseAppContext';
import { useResponsive } from './hooks/useResponsive';
import { AuthScreen } from './components/auth/AuthScreen';
import { Sidebar } from './components/Sidebar';
import { Header } from './components/Header';
import { DndKitBoard } from './components/DndKitBoard';
import { ToastNotifications } from './components/ToastNotifications';
import { ErrorBoundary } from './components/ErrorBoundary';
import { NoAccessMessage } from './components/NoAccessMessage';
import { BoardManager } from './components/BoardManager';
import ApiService from './services/ApiService';
import { debugApiConfig } from './config/api';

function MainLayout() {
  const { state, getVisibleBoards, updateUserPreferences } = useDatabaseApp();
  const [showBoardManager, setShowBoardManager] = useState(false);
  const { isMobile } = useResponsive();

  const visibleBoards = getVisibleBoards();
  const hasAccessToBoards = visibleBoards.length > 0;

  // Auto-hide sidebar on mobile
  React.useEffect(() => {
    if (isMobile && state.userPreferences.sidebarOpen) {
      updateUserPreferences({ sidebarOpen: false });
    }
  }, [isMobile, state.userPreferences.sidebarOpen, updateUserPreferences]);

  return (
    <div className="flex h-screen bg-gray-100 relative">
      {/* Sidebar */}
      {state.userPreferences.sidebarOpen && (
        <ErrorBoundary fallback={<div className="w-64 bg-red-50 p-4">Error en sidebar</div>}>
          {isMobile ? (
            // Mobile sidebar overlay
            <div className="fixed inset-0 z-40 md:hidden">
              <div
                className="absolute inset-0 bg-black bg-opacity-50"
                onClick={() => updateUserPreferences({ sidebarOpen: false })}
              />
              <div className="absolute left-0 top-0 h-full w-80 max-w-[85vw]">
                <Sidebar />
              </div>
            </div>
          ) : (
            // Desktop sidebar
            <Sidebar />
          )}
        </ErrorBoundary>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        <ErrorBoundary fallback={<div className="h-16 bg-red-50 p-4">Error en header</div>}>
          <Header />
        </ErrorBoundary>
        <main className="flex-1 overflow-auto min-h-0">
          {state.loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Cargando datos...</p>
              </div>
            </div>
          ) : state.error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <p className="text-red-600 mb-4">Error: {state.error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Reintentar
                </button>
              </div>
            </div>
          ) : !hasAccessToBoards ? (
            <NoAccessMessage
              onCreateBoard={() => setShowBoardManager(true)}
              onRequestAccess={() => {
                // Could implement a request access feature here
                console.log('Request access functionality could be implemented here');
              }}
            />
          ) : (
            <ErrorBoundary fallback={<div className="flex-1 bg-red-50 p-4">Error en tablero</div>}>
              <DndKitBoard />
            </ErrorBoundary>
          )}
        </main>
      </div>

      {/* Toast Notifications */}
      <ErrorBoundary fallback={null}>
        <ToastNotifications />
      </ErrorBoundary>

      {/* Board Manager Modal */}
      <BoardManager
        isOpen={showBoardManager}
        onClose={() => setShowBoardManager(false)}
      />
    </div>
  );
}

function AppContent() {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <AuthScreen />;
  }

  return (
    <DatabaseAppProvider>
      <MainLayout />
    </DatabaseAppProvider>
  );
}

function App() {
  useEffect(() => {
    // Debug API configuration
    debugApiConfig();

    // Check API server on startup
    const checkApiServer = async () => {
      try {
        await ApiService.healthCheck();
        console.log('✅ API server is running');
      } catch (error) {
        console.error('❌ API server is not running. Application requires backend API.');
      }
    };

    checkApiServer();
  }, []);

  return (
    <ErrorBoundary>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;