import React, { useState, useRef } from 'react';
import { X, Upload, Link, File, Image, Trash2 } from 'lucide-react';
import { Attachment } from '../../types';

interface AttachmentUploaderProps {
  onAddAttachment: (attachment: Omit<Attachment, 'id'>) => void;
  onClose: () => void;
}

export function AttachmentUploader({ onAddAttachment, onClose }: AttachmentUploaderProps) {
  const [activeTab, setActiveTab] = useState<'file' | 'link'>('file');
  const [linkUrl, setLinkUrl] = useState('');
  const [linkName, setLinkName] = useState('');
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    Array.from(files).forEach(file => {
      // Simulate file upload (in real app, you'd upload to a server)
      const attachment: Omit<Attachment, 'id'> = {
        name: file.name,
        url: URL.createObjectURL(file), // In real app, this would be the server URL
        type: 'file',
        size: file.size
      };
      
      onAddAttachment(attachment);
    });
    
    onClose();
  };

  const handleLinkAdd = () => {
    if (!linkUrl.trim()) return;

    const attachment: Omit<Attachment, 'id'> = {
      name: linkName.trim() || linkUrl,
      url: linkUrl.trim(),
      type: 'link'
    };

    onAddAttachment(attachment);
    onClose();
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) {
      return <Image className="w-5 h-5 text-blue-500" />;
    }
    return <File className="w-5 h-5 text-gray-500" />;
  };

  return (
    <div
      className="absolute top-full mt-2 left-0 w-full max-w-md bg-white rounded-lg shadow-lg border z-50 p-4 max-h-96 overflow-y-auto"
      onClick={(e) => e.stopPropagation()}
      style={{
        maxHeight: 'calc(100vh - 200px)',
        minWidth: '320px'
      }}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-gray-900">Adjuntar archivo</h3>
        <button onClick={onClose} className="p-1 hover:bg-gray-100 rounded">
          <X className="w-4 h-4" />
        </button>
      </div>

      {/* Tabs */}
      <div className="flex mb-4 border-b">
        <button
          onClick={() => setActiveTab('file')}
          className={`px-4 py-2 text-sm font-medium border-b-2 ${
            activeTab === 'file'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
          }`}
        >
          Subir archivo
        </button>
        <button
          onClick={() => setActiveTab('link')}
          className={`px-4 py-2 text-sm font-medium border-b-2 ${
            activeTab === 'link'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
          }`}
        >
          Enlace
        </button>
      </div>

      {/* File Upload Tab */}
      {activeTab === 'file' && (
        <div>
          <div
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              isDragging
                ? 'border-blue-400 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
          >
            <Upload className="w-8 h-8 text-gray-400 mx-auto mb-4" />
            <p className="text-sm text-gray-600 mb-2">
              Arrastra archivos aquí o{' '}
              <button
                onClick={() => fileInputRef.current?.click()}
                className="text-blue-600 hover:text-blue-700 underline"
              >
                selecciona archivos
              </button>
            </p>
            <p className="text-xs text-gray-500">
              Máximo 10MB por archivo
            </p>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            multiple
            onChange={(e) => handleFileSelect(e.target.files)}
            className="hidden"
            accept="*/*"
          />

          <div className="mt-4 text-xs text-gray-500">
            <p>Tipos de archivo soportados:</p>
            <p>Imágenes, documentos, videos, audio y más</p>
          </div>
        </div>
      )}

      {/* Link Tab */}
      {activeTab === 'link' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              URL del enlace
            </label>
            <input
              type="url"
              value={linkUrl}
              onChange={(e) => setLinkUrl(e.target.value)}
              placeholder="https://ejemplo.com"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nombre del enlace (opcional)
            </label>
            <input
              type="text"
              value={linkName}
              onChange={(e) => setLinkName(e.target.value)}
              placeholder="Nombre descriptivo del enlace"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <button
            onClick={handleLinkAdd}
            disabled={!linkUrl.trim()}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            Agregar enlace
          </button>
        </div>
      )}
    </div>
  );
}
