import React from 'react';
import { AlertTriangle, Trash2, RefreshCw } from 'lucide-react';

interface EmergencyCleanupProps {
  onCleanup: () => void;
  onReload: () => void;
}

export function EmergencyCleanup({ onCleanup, onReload }: EmergencyCleanupProps) {
  return (
    <div className="fixed inset-0 bg-red-900 bg-opacity-90 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="w-8 h-8 text-red-600" />
          </div>
          <h2 className="text-xl font-bold text-gray-900">Almacenamiento Crítico</h2>
          <p className="text-gray-600 mt-2">
            El almacenamiento local está lleno y la aplicación no puede funcionar correctamente.
          </p>
        </div>

        {/* Problem Description */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-red-800 mb-2">¿Qué está pasando?</h3>
          <ul className="text-red-700 text-sm space-y-1">
            <li>• El navegador ha alcanzado el límite de almacenamiento</li>
            <li>• Los datos no se pueden guardar correctamente</li>
            <li>• La aplicación puede comportarse de forma inesperada</li>
          </ul>
        </div>

        {/* Solutions */}
        <div className="space-y-3">
          <h3 className="font-medium text-gray-900">Soluciones disponibles:</h3>
          
          {/* Emergency Cleanup */}
          <button
            onClick={onCleanup}
            className="w-full flex items-center justify-center space-x-2 p-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <Trash2 className="w-4 h-4" />
            <span>Limpieza de Emergencia</span>
          </button>
          <p className="text-xs text-gray-500 text-center">
            Eliminará todos los datos antiguos y mantendrá solo lo esencial
          </p>

          {/* Reload */}
          <button
            onClick={onReload}
            className="w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Recargar Aplicación</span>
          </button>
          <p className="text-xs text-gray-500 text-center">
            Recarga la página después de la limpieza
          </p>
        </div>

        {/* Warning */}
        <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-xs">
            <strong>Nota:</strong> La limpieza de emergencia eliminará actividades, notificaciones 
            y datos antiguos, pero preservará tus tableros principales.
          </p>
        </div>
      </div>
    </div>
  );
}
