import React, { useState } from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { useAuth } from '../context/AuthContext';
import { useUserPermissions } from '../hooks/useUserPermissions';
import { Team, Board } from '../types';
import {
  Users,
  Plus,
  Settings,
  Crown,
  Eye,
  EyeOff,
  Globe,
  Lock,
  Building,
  Search,
  Filter,
  MoreHorizontal,
  Edit3,
  Trash2,
  UserPlus,
  X
} from 'lucide-react';
import { CreateTeamModal } from './teams/CreateTeamModal';
import { TeamCard } from './teams/TeamCard';
import { TeamDetailsModal } from './teams/TeamDetailsModal';

interface TeamManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function TeamManager({ isOpen, onClose }: TeamManagerProps) {
  const { state, dispatch, loadTeams, createTeam, updateTeam, deleteTeam, updateBoard, createBoard } = useDatabaseApp();
  const { user: currentUser } = useAuth();
  const { canCreateTeams, isAdmin, getRoleName } = useUserPermissions();
  const [showCreateTeam, setShowCreateTeam] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterBy, setFilterBy] = useState<'all' | 'admin' | 'member' | 'observer'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  if (!isOpen) return null;

  // Filter teams based on search and role
  const filteredTeams = state.teams.filter(team => {
    const matchesSearch = team.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         team.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (!matchesSearch) return false;

    if (filterBy === 'all') return true;

    const userMembership = team.members.find(member => member.userId === currentUser?.id);
    return userMembership?.role === filterBy;
  });

  const handleCreateTeam = async (teamData: Omit<Team, 'id' | 'createdAt' | 'updatedAt'>, newBoard?: Omit<Board, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!currentUser) {
      console.error('❌ No current user found');
      return;
    }

    // Verificar permisos antes de crear el equipo
    if (!canCreateTeams()) {
      console.error('❌ User does not have permission to create teams');
      alert('Solo los administradores pueden crear equipos');
      return;
    }

    try {
      let boardIds = [...teamData.boards];

      // Primero crear el equipo sin tableros nuevos
      const newTeam: Team = {
        ...teamData,
        id: Date.now().toString(),
        boards: boardIds, // Solo tableros existentes por ahora
        createdAt: new Date(),
        updatedAt: new Date(),
        members: [
          {
            userId: currentUser.id,
            role: 'admin',
            joinedAt: new Date()
          }
        ]
      };

      // Crear el equipo primero
      const createdTeam = await createTeam(newTeam);

      // Si se va a crear un nuevo tablero, crearlo después del equipo
      if (newBoard && createdTeam?.id) {
        // Convert team members to board members
        const teamMembersAsBoardMembers = createdTeam.members.map(teamMember => {
          // Find the user data
          const userData = state.users.find(user => user.id === teamMember.userId);
          if (!userData) {
            console.warn('⚠️ User data not found for team member:', teamMember.userId);
            return null;
          }

          return {
            id: userData.id,
            name: userData.name,
            email: userData.email,
            avatar: userData.avatar,
            role: teamMember.role.toLowerCase() as 'admin' | 'member' | 'observer',
            joinedAt: new Date()
          };
        }).filter(Boolean); // Remove null entries

        console.log('👥 Creating board with team members:', {
          teamId: createdTeam.id,
          teamName: createdTeam.name,
          boardMembers: teamMembersAsBoardMembers.length,
          memberNames: teamMembersAsBoardMembers.map(m => m?.name).filter(Boolean)
        });

        const boardToCreate = {
          ...newBoard,
          teamId: createdTeam.id, // Usar el ID real del equipo creado
          members: teamMembersAsBoardMembers
        };

        const createdBoard = await createBoard(boardToCreate);
        boardIds.push(createdBoard.id);

        // Actualizar el equipo con el nuevo tablero
        if (createdBoard?.id) {
          await updateTeam(createdTeam.id, {
            ...createdTeam,
            boards: [...createdTeam.boards, createdBoard.id]
          });
        }
      }

      // Después de crear el equipo, actualizar los tableros existentes
      if (teamData.boards.length > 0 && createdTeam?.id) {
        const actualTeamId = createdTeam.id;
        for (const boardId of teamData.boards) {
          const existingBoard = state.boards.find((b: Board) => b.id === boardId);
          if (existingBoard) {
            const updatedBoard: Board = {
              ...existingBoard,
              teamId: actualTeamId,
              updatedAt: new Date()
            };
            await updateBoard(boardId, updatedBoard);
          }
        }
      }

      setShowCreateTeam(false);
    } catch (error) {
      console.error('Error creating team:', error);
    }
  };

  const getVisibilityIcon = (visibility: Team['visibility']) => {
    switch (visibility) {
      case 'private':
        return <Lock className="w-4 h-4" />;
      case 'team':
        return <Users className="w-4 h-4" />;
      case 'organization':
        return <Building className="w-4 h-4" />;
      case 'public':
        return <Globe className="w-4 h-4" />;
    }
  };

  const getVisibilityLabel = (visibility: Team['visibility']) => {
    switch (visibility) {
      case 'private':
        return 'Privado';
      case 'team':
        return 'Equipo';
      case 'organization':
        return 'Organización';
      case 'public':
        return 'Público';
    }
  };

  const getUserRole = (team: Team) => {
    const membership = team.members.find(member => member.userId === currentUser?.id);
    return membership?.role || 'observer';
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Administrador';
      case 'normal':
        return 'Miembro';
      case 'observer':
        return 'Observador';
      default:
        return 'Sin rol';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-6 border-b bg-gradient-to-r from-purple-600 to-blue-600 text-white">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Users className="w-8 h-8" />
              <h2 className="text-2xl font-bold">Gestión de Equipos</h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white text-opacity-70" />
                <input
                  type="text"
                  placeholder="Buscar equipos..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                />
              </div>

              {/* Filter */}
              <div className="flex items-center space-x-2">
                <Filter className="w-4 h-4 text-white text-opacity-70" />
                <select
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value as any)}
                  className="bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                >
                  <option value="all" className="text-gray-900">Todos los equipos</option>
                  <option value="admin" className="text-gray-900">Soy administrador</option>
                  <option value="normal" className="text-gray-900">Soy miembro</option>
                  <option value="observer" className="text-gray-900">Soy observador</option>
                </select>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm opacity-90">
                {filteredTeams.length} equipo{filteredTeams.length !== 1 ? 's' : ''}
              </span>
              
              {canCreateTeams() ? (
                <button
                  onClick={() => setShowCreateTeam(true)}
                  className="flex items-center space-x-2 bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  <span>Crear Equipo</span>
                </button>
              ) : (
                <div className="flex items-center space-x-2 bg-white bg-opacity-10 px-4 py-2 rounded-lg text-white text-opacity-60 cursor-not-allowed">
                  <Lock className="w-4 h-4" />
                  <span>Solo Administradores</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {filteredTeams.length === 0 ? (
            <div className="text-center py-12">
              {searchQuery || filterBy !== 'all' ? (
                <>
                  <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-600 mb-2">
                    No se encontraron equipos
                  </h3>
                  <p className="text-gray-500 mb-4">
                    Intenta ajustar tus filtros de búsqueda
                  </p>
                  <button
                    onClick={() => {
                      setSearchQuery('');
                      setFilterBy('all');
                    }}
                    className="text-blue-600 hover:text-blue-700 transition-colors"
                  >
                    Limpiar filtros
                  </button>
                </>
              ) : (
                <>
                  <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-600 mb-2">
                    No tienes equipos aún
                  </h3>
                  <p className="text-gray-500 mb-4">
                    {canCreateTeams()
                      ? 'Crea tu primer equipo para colaborar con otros usuarios'
                      : 'Solo los administradores pueden crear equipos'
                    }
                  </p>
                  {canCreateTeams() ? (
                    <button
                      onClick={() => setShowCreateTeam(true)}
                      className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Crear mi primer equipo
                    </button>
                  ) : (
                    <div className="flex items-center justify-center space-x-2 bg-gray-100 text-gray-500 px-6 py-2 rounded-lg cursor-not-allowed">
                      <Lock className="w-4 h-4" />
                      <span>Acceso restringido</span>
                    </div>
                  )}
                </>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTeams.map(team => (
                <TeamCard
                  key={team.id}
                  team={team}
                  currentUserId={state.currentUser?.id || ''}
                  onClick={() => setSelectedTeam(team)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Create Team Modal */}
        {showCreateTeam && (
          <CreateTeamModal
            onClose={() => setShowCreateTeam(false)}
            onCreateTeam={handleCreateTeam}
          />
        )}

        {/* Team Details Modal */}
        {selectedTeam && (
          <TeamDetailsModal
            team={selectedTeam}
            onClose={() => setSelectedTeam(null)}
            onUpdateTeam={async (updatedTeam) => {
              try {
                await updateTeam(updatedTeam.id, updatedTeam);
                setSelectedTeam(updatedTeam);
              } catch (error) {
                console.error('❌ Error updating team:', error);
              }
            }}
            onDeleteTeam={async (teamId) => {
              try {
                await deleteTeam(teamId);
                setSelectedTeam(null); // Close the modal
              } catch (error) {
                console.error('❌ Error deleting team:', error);
                throw error; // Re-throw to let the component handle the error message
              }
            }}
          />
        )}
      </div>
    </div>
  );
}
