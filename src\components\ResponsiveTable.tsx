import React from 'react';
import { useResponsive } from '../hooks/useResponsive';

interface Column {
  key: string;
  label: string;
  render?: (value: any, row: any) => React.ReactNode;
  width?: string;
  mobileHidden?: boolean; // Hide this column on mobile
  mobileLabel?: string; // Alternative label for mobile
}

interface ResponsiveTableProps {
  columns: Column[];
  data: any[];
  keyField: string;
  emptyMessage?: string;
  className?: string;
  onRowClick?: (row: any) => void;
}

export function ResponsiveTable({
  columns,
  data,
  keyField,
  emptyMessage = 'No hay datos disponibles',
  className = '',
  onRowClick,
}: ResponsiveTableProps) {
  const { isMobile } = useResponsive();

  if (data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>{emptyMessage}</p>
      </div>
    );
  }

  if (isMobile) {
    // Mobile: Card-based layout
    return (
      <div className={`space-y-3 ${className}`}>
        {data.map((row) => (
          <div
            key={row[keyField]}
            className={`bg-white border border-gray-200 rounded-lg p-4 shadow-sm ${
              onRowClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''
            }`}
            onClick={() => onRowClick?.(row)}
          >
            {columns
              .filter(col => !col.mobileHidden)
              .map((column) => {
                const value = row[column.key];
                const displayValue = column.render ? column.render(value, row) : value;
                const label = column.mobileLabel || column.label;

                return (
                  <div key={column.key} className="flex justify-between items-start py-1">
                    <span className="text-sm font-medium text-gray-600 mr-3">
                      {label}:
                    </span>
                    <span className="text-sm text-gray-900 text-right flex-1">
                      {displayValue}
                    </span>
                  </div>
                );
              })}
          </div>
        ))}
      </div>
    );
  }

  // Desktop: Traditional table layout
  return (
    <div className={`overflow-x-auto ${className}`}>
      <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
        <thead className="bg-gray-50">
          <tr>
            {columns.map((column) => (
              <th
                key={column.key}
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                style={{ width: column.width }}
              >
                {column.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {data.map((row) => (
            <tr
              key={row[keyField]}
              className={`${
                onRowClick 
                  ? 'cursor-pointer hover:bg-gray-50 transition-colors' 
                  : ''
              }`}
              onClick={() => onRowClick?.(row)}
            >
              {columns.map((column) => {
                const value = row[column.key];
                const displayValue = column.render ? column.render(value, row) : value;

                return (
                  <td
                    key={column.key}
                    className="px-4 py-3 text-sm text-gray-900"
                    style={{ width: column.width }}
                  >
                    <div className="truncate">
                      {displayValue}
                    </div>
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

// Hook for creating responsive table columns
export function useResponsiveColumns() {
  const { isMobile } = useResponsive();

  const createColumn = (
    key: string,
    label: string,
    options: {
      render?: (value: any, row: any) => React.ReactNode;
      width?: string;
      mobileHidden?: boolean;
      mobileLabel?: string;
    } = {}
  ): Column => ({
    key,
    label,
    ...options,
  });

  const createMobileOptimizedColumn = (
    key: string,
    desktopLabel: string,
    mobileLabel: string,
    options: {
      render?: (value: any, row: any) => React.ReactNode;
      width?: string;
    } = {}
  ): Column => ({
    key,
    label: desktopLabel,
    mobileLabel,
    ...options,
  });

  return {
    createColumn,
    createMobileOptimizedColumn,
    isMobile,
  };
}
