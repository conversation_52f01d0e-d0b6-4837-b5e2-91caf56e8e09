import { useEffect, useRef, useState } from 'react';

interface SwipeGestureOptions {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  threshold?: number; // Minimum distance for a swipe
  velocity?: number; // Minimum velocity for a swipe
  preventScroll?: boolean; // Prevent default scroll behavior
  enabled?: boolean; // Enable/disable gestures
}

interface TouchPoint {
  x: number;
  y: number;
  time: number;
}

export function useSwipeGestures(options: SwipeGestureOptions = {}) {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    threshold = 50,
    velocity = 0.3,
    preventScroll = false,
    enabled = true,
  } = options;

  const elementRef = useRef<HTMLElement>(null);
  const [touchStart, setTouchStart] = useState<TouchPoint | null>(null);
  const [touchEnd, setTouchEnd] = useState<TouchPoint | null>(null);
  const [isSwiping, setIsSwiping] = useState(false);

  const getTouchPoint = (touch: Touch): TouchPoint => ({
    x: touch.clientX,
    y: touch.clientY,
    time: Date.now(),
  });

  const calculateDistance = (start: TouchPoint, end: TouchPoint) => {
    const deltaX = end.x - start.x;
    const deltaY = end.y - start.y;
    return {
      deltaX,
      deltaY,
      distance: Math.sqrt(deltaX * deltaX + deltaY * deltaY),
    };
  };

  const calculateVelocity = (start: TouchPoint, end: TouchPoint) => {
    const { distance } = calculateDistance(start, end);
    const deltaTime = end.time - start.time;
    return deltaTime > 0 ? distance / deltaTime : 0;
  };

  const handleTouchStart = (e: TouchEvent) => {
    if (!enabled) return;
    
    const touch = e.touches[0];
    if (touch) {
      setTouchStart(getTouchPoint(touch));
      setTouchEnd(null);
      setIsSwiping(false);
    }
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (!enabled || !touchStart) return;

    const touch = e.touches[0];
    if (touch) {
      const currentPoint = getTouchPoint(touch);
      const { deltaX, deltaY, distance } = calculateDistance(touchStart, currentPoint);
      
      // Determine if this is a swipe gesture
      if (distance > threshold / 2) {
        setIsSwiping(true);
        
        // Prevent scroll if this looks like a horizontal swipe
        if (preventScroll && Math.abs(deltaX) > Math.abs(deltaY)) {
          e.preventDefault();
        }
      }
      
      setTouchEnd(currentPoint);
    }
  };

  const handleTouchEnd = (e: TouchEvent) => {
    if (!enabled || !touchStart || !touchEnd) return;

    const { deltaX, deltaY, distance } = calculateDistance(touchStart, touchEnd);
    const swipeVelocity = calculateVelocity(touchStart, touchEnd);

    // Check if the swipe meets the minimum requirements
    if (distance < threshold || swipeVelocity < velocity) {
      setTouchStart(null);
      setTouchEnd(null);
      setIsSwiping(false);
      return;
    }

    // Determine swipe direction based on the largest delta
    const absX = Math.abs(deltaX);
    const absY = Math.abs(deltaY);

    if (absX > absY) {
      // Horizontal swipe
      if (deltaX > 0) {
        onSwipeRight?.();
      } else {
        onSwipeLeft?.();
      }
    } else {
      // Vertical swipe
      if (deltaY > 0) {
        onSwipeDown?.();
      } else {
        onSwipeUp?.();
      }
    }

    // Reset state
    setTouchStart(null);
    setTouchEnd(null);
    setIsSwiping(false);
  };

  useEffect(() => {
    const element = elementRef.current;
    if (!element || !enabled) return;

    // Add touch event listeners
    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [enabled, touchStart, touchEnd, threshold, velocity, preventScroll]);

  return {
    ref: elementRef,
    isSwiping,
    touchStart,
    touchEnd,
  };
}

// Hook for detecting swipe gestures on the entire screen
export function useGlobalSwipeGestures(options: SwipeGestureOptions = {}) {
  const [touchStart, setTouchStart] = useState<TouchPoint | null>(null);
  const [touchEnd, setTouchEnd] = useState<TouchPoint | null>(null);

  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    threshold = 50,
    velocity = 0.3,
    enabled = true,
  } = options;

  const getTouchPoint = (touch: Touch): TouchPoint => ({
    x: touch.clientX,
    y: touch.clientY,
    time: Date.now(),
  });

  const calculateDistance = (start: TouchPoint, end: TouchPoint) => {
    const deltaX = end.x - start.x;
    const deltaY = end.y - start.y;
    return {
      deltaX,
      deltaY,
      distance: Math.sqrt(deltaX * deltaX + deltaY * deltaY),
    };
  };

  const calculateVelocity = (start: TouchPoint, end: TouchPoint) => {
    const { distance } = calculateDistance(start, end);
    const deltaTime = end.time - start.time;
    return deltaTime > 0 ? distance / deltaTime : 0;
  };

  useEffect(() => {
    if (!enabled) return;

    const handleTouchStart = (e: TouchEvent) => {
      const touch = e.touches[0];
      if (touch) {
        setTouchStart(getTouchPoint(touch));
        setTouchEnd(null);
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!touchStart) return;
      
      const touch = e.touches[0];
      if (touch) {
        setTouchEnd(getTouchPoint(touch));
      }
    };

    const handleTouchEnd = () => {
      if (!touchStart || !touchEnd) return;

      const { deltaX, deltaY, distance } = calculateDistance(touchStart, touchEnd);
      const swipeVelocity = calculateVelocity(touchStart, touchEnd);

      if (distance < threshold || swipeVelocity < velocity) {
        setTouchStart(null);
        setTouchEnd(null);
        return;
      }

      const absX = Math.abs(deltaX);
      const absY = Math.abs(deltaY);

      if (absX > absY) {
        if (deltaX > 0) {
          onSwipeRight?.();
        } else {
          onSwipeLeft?.();
        }
      } else {
        if (deltaY > 0) {
          onSwipeDown?.();
        } else {
          onSwipeUp?.();
        }
      }

      setTouchStart(null);
      setTouchEnd(null);
    };

    document.addEventListener('touchstart', handleTouchStart, { passive: true });
    document.addEventListener('touchmove', handleTouchMove, { passive: true });
    document.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [enabled, touchStart, touchEnd, threshold, velocity]);

  return {
    touchStart,
    touchEnd,
  };
}
