import React, { useState } from 'react';
import { X, Calendar, Clock, Trash2 } from 'lucide-react';
import { formatDateTime } from '../../utils/dateUtils';

interface DatePickerProps {
  currentDate?: Date;
  onDateChange: (date: Date | undefined) => void;
  onClose: () => void;
}

export function DatePicker({ currentDate, onDateChange, onClose }: DatePickerProps) {
  const [selectedDate, setSelectedDate] = useState(
    currentDate ? currentDate.toISOString().split('T')[0] : ''
  );
  const [selectedTime, setSelectedTime] = useState(
    currentDate ? currentDate.toTimeString().slice(0, 5) : '12:00'
  );

  const handleSave = () => {
    if (selectedDate) {
      const date = new Date(`${selectedDate}T${selectedTime}`);
      onDateChange(date);
    }
    onClose();
  };

  const handleRemove = () => {
    onDateChange(undefined);
    onClose();
  };

  const getQuickDateOptions = () => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    return [
      { label: 'Hoy', date: today },
      { label: 'Mañana', date: tomorrow },
      { label: 'Próxima semana', date: nextWeek }
    ];
  };

  const quickOptions = getQuickDateOptions();

  const isOverdue = currentDate && currentDate < new Date();

  return (
    <div
      className="absolute top-full mt-2 left-0 w-full max-w-sm bg-white rounded-lg shadow-lg border z-50 p-4 max-h-96 overflow-y-auto"
      onClick={(e) => e.stopPropagation()}
      style={{
        maxHeight: 'calc(100vh - 200px)',
        minWidth: '300px'
      }}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-gray-900">Fecha de vencimiento</h3>
        <button onClick={onClose} className="p-1 hover:bg-gray-100 rounded">
          <X className="w-4 h-4" />
        </button>
      </div>

      {/* Current Date Status */}
      {currentDate && (
        <div className={`mb-4 p-3 rounded-lg ${
          isOverdue ? 'bg-red-50 border border-red-200' : 'bg-blue-50 border border-blue-200'
        }`}>
          <div className="flex items-center space-x-2">
            <Calendar className={`w-4 h-4 ${isOverdue ? 'text-red-600' : 'text-blue-600'}`} />
            <span className={`text-sm font-medium ${
              isOverdue ? 'text-red-700' : 'text-blue-700'
            }`}>
              {isOverdue ? 'Vencida' : 'Programada'}
            </span>
          </div>
          <p className={`text-sm mt-1 ${isOverdue ? 'text-red-600' : 'text-blue-600'}`}>
            {formatDateTime(currentDate)}
          </p>
        </div>
      )}

      {/* Quick Options */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Opciones rápidas</h4>
        <div className="space-y-1">
          {quickOptions.map((option) => (
            <button
              key={option.label}
              onClick={() => {
                setSelectedDate(option.date.toISOString().split('T')[0]);
                setSelectedTime('12:00');
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded-lg"
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Date Input */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <Calendar className="w-4 h-4 inline mr-1" />
          Fecha
        </label>
        <input
          type="date"
          value={selectedDate}
          onChange={(e) => setSelectedDate(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Time Input */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <Clock className="w-4 h-4 inline mr-1" />
          Hora
        </label>
        <input
          type="time"
          value={selectedTime}
          onChange={(e) => setSelectedTime(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Actions */}
      <div className="flex space-x-2">
        <button
          onClick={handleSave}
          disabled={!selectedDate}
          className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          Guardar
        </button>
        {currentDate && (
          <button
            onClick={handleRemove}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center space-x-1"
          >
            <Trash2 className="w-4 h-4" />
            <span>Quitar</span>
          </button>
        )}
      </div>
    </div>
  );
}
