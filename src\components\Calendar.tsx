import React, { useState, useMemo } from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { Card as CardType } from '../types';
import {
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon,
  Clock,
  AlertTriangle,
  CheckCircle,
  Plus,
  Filter,
  Users,
  Tag,
  BarChart3,
  Grid3X3,
  Columns
} from 'lucide-react';
import { CardModal } from './CardModal';
import { CalendarWeekView } from './CalendarWeekView';
import { CalendarStats } from './CalendarStats';

interface CalendarProps {
  onClose?: () => void;
}

export function Calendar({ onClose }: CalendarProps) {
  const { state, getCurrentBoard } = useDatabaseApp();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedCard, setSelectedCard] = useState<CardType | null>(null);
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'stats'>('month');
  const [filterBy, setFilterBy] = useState<'all' | 'assigned' | 'overdue' | 'today' | 'week'>('all');

  // Get all cards with due dates from all boards
  const allCardsWithDates = useMemo(() => {
    const cards: (CardType & { boardTitle: string; listTitle: string })[] = [];
    
    state.boards.forEach(board => {
      board.lists.forEach(list => {
        list.cards.forEach(card => {
          if (card.dueDate && !card.archived) {
            cards.push({
              ...card,
              boardTitle: board.title,
              listTitle: list.title
            });
          }
        });
      });
    });

    return cards;
  }, [state.boards]);

  // Filter cards based on selected filter
  const filteredCards = useMemo(() => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

    return allCardsWithDates.filter(card => {
      const dueDate = new Date(card.dueDate!);
      
      switch (filterBy) {
        case 'assigned':
          return card.assignedMembers.some(member => member.id === state.currentUser?.id);
        case 'overdue':
          return dueDate < today;
        case 'today':
          return dueDate.toDateString() === today.toDateString();
        case 'week':
          return dueDate >= today && dueDate <= weekFromNow;
        default:
          return true;
      }
    });
  }, [allCardsWithDates, filterBy, state.currentUser]);

  // Generate calendar days
  const calendarDays = useMemo(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const days = [];
    const currentDay = new Date(startDate);
    
    for (let i = 0; i < 42; i++) {
      const dayCards = filteredCards.filter(card => {
        const cardDate = new Date(card.dueDate!);
        return cardDate.toDateString() === currentDay.toDateString();
      });
      
      days.push({
        date: new Date(currentDay),
        isCurrentMonth: currentDay.getMonth() === month,
        isToday: currentDay.toDateString() === new Date().toDateString(),
        cards: dayCards
      });
      
      currentDay.setDate(currentDay.getDate() + 1);
    }
    
    return days;
  }, [currentDate, filteredCards]);

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(prev.getMonth() + (direction === 'next' ? 1 : -1));
      return newDate;
    });
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const getCardPriorityColor = (card: CardType) => {
    const now = new Date();
    const dueDate = new Date(card.dueDate!);
    const diffHours = (dueDate.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    if (diffHours < 0) return 'bg-red-500'; // Overdue
    if (diffHours < 24) return 'bg-orange-500'; // Due today
    if (diffHours < 72) return 'bg-yellow-500'; // Due in 3 days
    return 'bg-blue-500'; // Normal
  };

  const handleCardClick = (card: CardType) => {
    setSelectedCard(card);
  };

  const handleUpdateCard = (updatedCard: CardType) => {
    // Find the board and list containing this card
    const board = state.boards.find(b =>
      b.lists.some(l => l.cards.some(c => c.id === updatedCard.id))
    );

    if (board) {
      const list = board.lists.find(l => l.cards.some(c => c.id === updatedCard.id));
      if (list) {
        dispatch({
          type: 'UPDATE_CARD',
          payload: {
            boardId: board.id,
            listId: list.id,
            card: updatedCard
          }
        });

        // Update the selected card to reflect changes in the modal
        setSelectedCard(updatedCard);
      }
    }
  };

  const monthNames = [
    'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
    'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
  ];

  const dayNames = ['Dom', 'Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb'];

  const filterOptions = [
    { value: 'all', label: 'Todas las tarjetas', icon: CalendarIcon },
    { value: 'assigned', label: 'Asignadas a mí', icon: Users },
    { value: 'overdue', label: 'Vencidas', icon: AlertTriangle },
    { value: 'today', label: 'Vencen hoy', icon: Clock },
    { value: 'week', label: 'Esta semana', icon: CheckCircle }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-6 border-b bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <CalendarIcon className="w-8 h-8" />
              <h2 className="text-2xl font-bold">Calendario de Tareas</h2>
            </div>
            {onClose && (
              <button
                onClick={onClose}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
              >
                <Plus className="w-6 h-6 rotate-45" />
              </button>
            )}
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {viewMode !== 'stats' && (
                <>
                  <button
                    onClick={() => navigateMonth('prev')}
                    className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                  >
                    <ChevronLeft className="w-5 h-5" />
                  </button>

                  <h3 className="text-xl font-semibold min-w-[200px] text-center">
                    {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
                  </h3>

                  <button
                    onClick={() => navigateMonth('next')}
                    className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                  >
                    <ChevronRight className="w-5 h-5" />
                  </button>

                  <button
                    onClick={goToToday}
                    className="px-4 py-2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg transition-colors"
                  >
                    Hoy
                  </button>
                </>
              )}

              {/* View Mode Selector */}
              <div className="flex items-center space-x-1 bg-white bg-opacity-20 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('month')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'month'
                      ? 'bg-white bg-opacity-30 text-white'
                      : 'text-white text-opacity-70 hover:text-opacity-100'
                  }`}
                  title="Vista Mensual"
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('week')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'week'
                      ? 'bg-white bg-opacity-30 text-white'
                      : 'text-white text-opacity-70 hover:text-opacity-100'
                  }`}
                  title="Vista Semanal"
                >
                  <Columns className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('stats')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'stats'
                      ? 'bg-white bg-opacity-30 text-white'
                      : 'text-white text-opacity-70 hover:text-opacity-100'
                  }`}
                  title="Estadísticas"
                >
                  <BarChart3 className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <select
                value={filterBy}
                onChange={(e) => setFilterBy(e.target.value as any)}
                className="bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg px-3 py-1 text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
              >
                {filterOptions.map(option => (
                  <option key={option.value} value={option.value} className="text-gray-900">
                    {option.label}
                  </option>
                ))}
              </select>
              
              <div className="text-sm opacity-90">
                {filteredCards.length} tarjeta{filteredCards.length !== 1 ? 's' : ''}
              </div>
            </div>
          </div>
        </div>

        {/* Calendar Content */}
        <div className="flex-1 overflow-auto">
          {viewMode === 'month' && (
            <div className="p-6">
              {/* Day Headers */}
              <div className="grid grid-cols-7 gap-1 mb-2">
                {dayNames.map(day => (
                  <div key={day} className="p-3 text-center font-semibold text-gray-600 bg-gray-50 rounded-lg">
                    {day}
                  </div>
                ))}
              </div>

              {/* Calendar Days */}
              <div className="grid grid-cols-7 gap-1">
                {calendarDays.map((day, index) => (
                  <div
                    key={index}
                    className={`min-h-[120px] p-2 border rounded-lg ${
                      day.isCurrentMonth
                        ? 'bg-white border-gray-200'
                        : 'bg-gray-50 border-gray-100'
                    } ${
                      day.isToday
                        ? 'ring-2 ring-blue-500 bg-blue-50'
                        : ''
                    }`}
                  >
                    <div className={`text-sm font-medium mb-2 ${
                      day.isCurrentMonth ? 'text-gray-900' : 'text-gray-400'
                    } ${
                      day.isToday ? 'text-blue-600 font-bold' : ''
                    }`}>
                      {day.date.getDate()}
                    </div>

                    <div className="space-y-1">
                      {day.cards.slice(0, 3).map(card => (
                        <div
                          key={card.id}
                          onClick={() => handleCardClick(card)}
                          className={`text-xs p-1 rounded cursor-pointer hover:opacity-80 transition-opacity text-white ${getCardPriorityColor(card)}`}
                          title={`${card.title} - ${card.boardTitle} > ${card.listTitle}`}
                        >
                          <div className="truncate font-medium">{card.title}</div>
                          <div className="flex items-center space-x-1 mt-1">
                            <Clock className="w-3 h-3" />
                            <span>{new Date(card.dueDate!).toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' })}</span>
                          </div>
                        </div>
                      ))}

                      {day.cards.length > 3 && (
                        <div className="text-xs text-gray-500 text-center py-1">
                          +{day.cards.length - 3} más
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {viewMode === 'week' && (
            <CalendarWeekView
              currentDate={currentDate}
              filteredCards={filteredCards}
              onCardClick={handleCardClick}
            />
          )}

          {viewMode === 'stats' && (
            <div className="p-6">
              <CalendarStats
                allCards={allCardsWithDates}
                currentUserId={state.currentUser?.id}
              />
            </div>
          )}
        </div>

        {/* Card Modal */}
        {selectedCard && (
          <CardModal
            card={selectedCard}
            onClose={() => setSelectedCard(null)}
            onUpdate={handleUpdateCard}
          />
        )}
      </div>
    </div>
  );
}
