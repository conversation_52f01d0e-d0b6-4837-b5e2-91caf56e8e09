async function debugUsers() {
  console.log('🔍 Debugging user management system...\n');

  try {
    // Check API server health
    console.log('1. 🏥 Checking API server health...');
    const healthResponse = await fetch('http://localhost:3001/health');
    const healthData = await healthResponse.json();
    console.log('✅ API Server:', healthData.message);

    // Get all users from API
    console.log('\n2. 👥 Getting all users from API...');
    const usersResponse = await fetch('http://localhost:3001/api/users');
    
    if (!usersResponse.ok) {
      console.log('❌ Failed to get users from API');
      return;
    }

    const users = await usersResponse.json();
    console.log(`✅ Found ${users.length} users in database:`);
    
    users.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.name} (${user.email})`);
      console.log(`      - Username: ${user.username}`);
      console.log(`      - Role: ${user.role}`);
      console.log(`      - Active: ${user.isActive}`);
      console.log(`      - Password: ${user.password}`);
      console.log(`      - Created: ${new Date(user.createdAt).toLocaleString()}`);
      console.log('');
    });

    // Test login for each user
    console.log('3. 🔐 Testing login for each user...');
    for (const user of users) {
      console.log(`\n   Testing login for ${user.name} (${user.email})...`);
      
      const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: user.email,
          password: user.password
        })
      });

      if (loginResponse.ok) {
        const loginData = await loginResponse.json();
        if (loginData.success) {
          console.log(`   ✅ Login successful for ${user.name}`);
        } else {
          console.log(`   ❌ Login failed for ${user.name}: ${loginData.error}`);
        }
      } else {
        const errorData = await loginResponse.json();
        console.log(`   ❌ Login failed for ${user.name}: ${errorData.error}`);
      }
    }

    console.log('\n4. 📊 Summary:');
    console.log(`   - Total users: ${users.length}`);
    console.log(`   - Active users: ${users.filter(u => u.isActive).length}`);
    console.log(`   - Admin users: ${users.filter(u => u.role === 'admin').length}`);
    console.log(`   - Member users: ${users.filter(u => u.role === 'member').length}`);
    console.log(`   - Observer users: ${users.filter(u => u.role === 'observer').length}`);

  } catch (error) {
    console.error('❌ Debug error:', error);
  }
}

debugUsers();
