import React, { useState } from 'react';
import { useDatabaseApp } from '../../context/DatabaseAppContext';
import { useUserPermissions } from '../../hooks/useUserPermissions';
import { Team, Board } from '../../types';
import {
  Plus,
  Star,
  Users,
  Calendar,
  MoreHorizontal,
  Grid3X3,
  Trello as BoardIcon,
  FolderPlus,
  X,
  Check,
  Trash2
} from 'lucide-react';

interface TeamBoardsTabProps {
  team: Team;
  onUpdateTeam: (team: Team) => void;
}

export function TeamBoardsTab({ team, onUpdateTeam }: TeamBoardsTabProps) {
  const { state, dispatch } = useDatabaseApp();
  const { isAdmin } = useUserPermissions();
  const [showAddExistingBoard, setShowAddExistingBoard] = useState(false);
  const [selectedBoardId, setSelectedBoardId] = useState('');

  // Get boards that belong to this team
  const teamBoards = state.boards.filter(board => board.teamId === team.id);

  // Get available boards (not assigned to any team)
  const availableBoards = state.boards.filter(board => !board.teamId);

  const handleAddExistingBoard = () => {
    if (!selectedBoardId) return;

    const boardToAdd = state.boards.find(board => board.id === selectedBoardId);
    if (!boardToAdd) return;

    console.log('🔗 Assigning board to team:', {
      boardId: selectedBoardId,
      boardTitle: boardToAdd.title,
      teamId: team.id,
      teamName: team.name,
      teamMembersCount: team.members.length,
      currentBoardMembersCount: boardToAdd.members.length
    });

    // Convert team members to board members
    const teamMembersAsBoardMembers = team.members.map(teamMember => {
      // Find the user data from state.users
      const userData = state.users.find(user => user.id === teamMember.userId);
      if (!userData) {
        console.warn('⚠️ User data not found for team member:', teamMember.userId);
        return null;
      }

      return {
        id: userData.id,
        name: userData.name,
        email: userData.email,
        avatar: userData.avatar,
        role: teamMember.role.toLowerCase() as 'admin' | 'member' | 'observer',
        joinedAt: new Date()
      };
    }).filter(Boolean); // Remove null entries

    // Merge existing board members with team members (avoid duplicates)
    const existingMemberIds = boardToAdd.members.map(m => m.id);
    const newMembers = teamMembersAsBoardMembers.filter(member =>
      member && !existingMemberIds.includes(member.id)
    );

    const allBoardMembers = [...boardToAdd.members, ...newMembers];

    console.log('👥 Board members after team assignment:', {
      existingMembers: boardToAdd.members.length,
      newMembersFromTeam: newMembers.length,
      totalMembers: allBoardMembers.length,
      memberNames: allBoardMembers.map(m => m?.name).filter(Boolean)
    });

    // Update the board to assign it to this team and add team members
    const updatedBoard: Board = {
      ...boardToAdd,
      teamId: team.id,
      members: allBoardMembers,
      updatedAt: new Date()
    };

    // Update the team to include this board
    const updatedTeam: Team = {
      ...team,
      boards: [...team.boards, selectedBoardId],
      updatedAt: new Date()
    };

    dispatch({ type: 'UPDATE_BOARD', payload: updatedBoard });
    onUpdateTeam(updatedTeam);
    setSelectedBoardId('');
    setShowAddExistingBoard(false);
  };

  const handleRemoveBoard = (boardId: string) => {
    if (!confirm('¿Estás seguro de que quieres desasignar este tablero del equipo?')) return;

    const boardToRemove = state.boards.find(board => board.id === boardId);
    if (!boardToRemove) return;

    // Update the board to remove team assignment
    const updatedBoard: Board = {
      ...boardToRemove,
      teamId: undefined,
      updatedAt: new Date()
    };

    // Update the team to remove this board
    const updatedTeam: Team = {
      ...team,
      boards: team.boards.filter(id => id !== boardId),
      updatedAt: new Date()
    };

    dispatch({ type: 'UPDATE_BOARD', payload: updatedBoard });
    onUpdateTeam(updatedTeam);
  };

  const handleSyncTeamMembersToBoard = (boardId: string) => {
    const board = state.boards.find(b => b.id === boardId);
    if (!board) return;

    console.log('🔄 Syncing team members to board:', {
      boardId,
      boardTitle: board.title,
      teamId: team.id,
      teamName: team.name
    });

    // Convert team members to board members
    const teamMembersAsBoardMembers = team.members.map(teamMember => {
      const userData = state.users.find(user => user.id === teamMember.userId);
      if (!userData) return null;

      return {
        id: userData.id,
        name: userData.name,
        email: userData.email,
        avatar: userData.avatar,
        role: teamMember.role.toLowerCase() as 'admin' | 'member' | 'observer',
        joinedAt: new Date()
      };
    }).filter(Boolean);

    // Merge with existing members (avoid duplicates)
    const existingMemberIds = board.members.map(m => m.id);
    const newMembers = teamMembersAsBoardMembers.filter(member =>
      member && !existingMemberIds.includes(member.id)
    );

    if (newMembers.length === 0) {
      alert('Todos los miembros del equipo ya están en el tablero.');
      return;
    }

    const updatedBoard: Board = {
      ...board,
      members: [...board.members, ...newMembers],
      updatedAt: new Date()
    };

    dispatch({ type: 'UPDATE_BOARD', payload: updatedBoard });

    alert(`Se agregaron ${newMembers.length} miembros del equipo al tablero.`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Tableros del Equipo ({teamBoards.length})
          </h3>
          <p className="text-sm text-gray-500">
            Tableros asignados a este equipo
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowAddExistingBoard(true)}
            disabled={availableBoards.length === 0}
            className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            <FolderPlus className="w-4 h-4" />
            <span>Agregar Tablero Existente</span>
          </button>

          {isAdmin() ? (
            <button className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              <Plus className="w-4 h-4" />
              <span>Crear Tablero</span>
            </button>
          ) : (
            <div className="flex items-center space-x-2 bg-gray-100 text-gray-500 px-4 py-2 rounded-lg cursor-not-allowed">
              <Plus className="w-4 h-4" />
              <span>Solo Admins</span>
            </div>
          )}
        </div>
      </div>

      {/* Add Existing Board Modal */}
      {showAddExistingBoard && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Agregar Tablero Existente</h3>
                <button
                  onClick={() => setShowAddExistingBoard(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <X className="w-5 h-5 text-gray-500" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Seleccionar Tablero
                  </label>
                  <select
                    value={selectedBoardId}
                    onChange={(e) => setSelectedBoardId(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Seleccionar tablero...</option>
                    {availableBoards.map(board => (
                      <option key={board.id} value={board.id}>
                        {board.title}
                      </option>
                    ))}
                  </select>
                </div>

                {availableBoards.length === 0 && (
                  <div className="text-center py-4">
                    <BoardIcon className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">
                      No hay tableros disponibles para asignar
                    </p>
                  </div>
                )}

                <div className="flex items-center justify-end space-x-3 pt-4">
                  <button
                    onClick={() => setShowAddExistingBoard(false)}
                    className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleAddExistingBoard}
                    disabled={!selectedBoardId}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                  >
                    Agregar Tablero
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Boards Grid */}
      {teamBoards.length === 0 ? (
        <div className="text-center py-12">
          <Grid3X3 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-600 mb-2">
            No hay tableros en este equipo
          </h4>
          <p className="text-gray-500 mb-4">
            Crea el primer tablero para empezar a colaborar
          </p>
          <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Crear primer tablero
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {teamBoards.map(board => (
            <div
              key={board.id}
              className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer group"
            >
              {/* Board Header */}
              <div 
                className="h-24 bg-gradient-to-br from-blue-500 to-purple-600 relative"
                style={{ background: board.background }}
              >
                <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all" />
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSyncTeamMembersToBoard(board.id);
                    }}
                    className="p-1 bg-green-500 bg-opacity-80 hover:bg-opacity-100 rounded transition-all"
                    title="Sincronizar miembros del equipo al tablero"
                  >
                    <Users className="w-4 h-4 text-white" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveBoard(board.id);
                    }}
                    className="p-1 bg-red-500 bg-opacity-80 hover:bg-opacity-100 rounded transition-all"
                    title="Desasignar tablero del equipo"
                  >
                    <Trash2 className="w-4 h-4 text-white" />
                  </button>
                  <button className="p-1 bg-white bg-opacity-20 hover:bg-opacity-30 rounded">
                    <MoreHorizontal className="w-4 h-4 text-white" />
                  </button>
                </div>
                {board.isFavorite && (
                  <div className="absolute top-2 left-2">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  </div>
                )}
              </div>

              {/* Board Content */}
              <div className="p-4">
                <h4 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                  {board.title}
                </h4>
                
                {board.description && (
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                    {board.description}
                  </p>
                )}

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="flex -space-x-1">
                      {board.members.slice(0, 3).map((member, index) => (
                        <img
                          key={member.id}
                          src={member.avatar}
                          alt={member.name}
                          className="w-6 h-6 rounded-full border-2 border-white object-cover"
                          title={member.name}
                        />
                      ))}
                      {board.members.length > 3 && (
                        <div className="w-6 h-6 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-xs font-medium text-gray-600">
                          +{board.members.length - 3}
                        </div>
                      )}
                    </div>
                    {board.members.length > 0 && (
                      <span className="text-xs text-gray-500">
                        {board.members.length}
                      </span>
                    )}
                  </div>

                  <span className="text-xs text-gray-500 flex items-center space-x-1">
                    <Calendar className="w-3 h-3" />
                    <span>
                      {new Date(board.updatedAt).toLocaleDateString('es-ES')}
                    </span>
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
