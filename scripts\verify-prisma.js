import { PrismaClient } from '@prisma/client';

async function verifyPrisma() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Verificando conexión a Prisma...');
    
    // Test connection
    await prisma.$connect();
    console.log('✅ Conexión a Prisma exitosa');
    
    // Check if users table exists and has data
    const userCount = await prisma.user.count();
    console.log(`📊 Usuarios en base de datos: ${userCount}`);
    
    if (userCount === 0) {
      console.log('⚠️  No hay usuarios en la base de datos');
      console.log('💡 Los usuarios se crearán automáticamente al iniciar la aplicación');
    } else {
      const users = await prisma.user.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true
        }
      });
      
      console.log('👥 Usuarios encontrados:');
      users.forEach(user => {
        console.log(`   - ${user.name} (${user.email}) - ${user.role} - ${user.isActive ? 'Activo' : 'Inactivo'}`);
      });
    }
    
    console.log('🎉 Verificación completada exitosamente');
    
  } catch (error) {
    console.error('❌ Error verificando Prisma:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

verifyPrisma();
