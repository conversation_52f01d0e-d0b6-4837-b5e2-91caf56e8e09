import React, { useState, useEffect } from 'react';
import { User, UserFormData } from '../types';
import ApiService from '../services/ApiService';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { useResponsive } from '../hooks/useResponsive';
import { ResponsiveModal } from './ResponsiveModal';
import { UserList } from './UserList';
import { UserForm } from './UserForm';
import { UserMetrics } from './UserMetrics';
import { UserPermissions } from './UserPermissions';
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  BarChart3, 
  Settings,
  X,
  UserPlus,
  Shield,
  Activity
} from 'lucide-react';

interface UserManagementProps {
  isOpen: boolean;
  onClose: () => void;
}

type ViewMode = 'list' | 'create' | 'metrics' | 'permissions';

export function UserManagement({ isOpen, onClose }: UserManagementProps) {
  const { state, dispatch, loadUsers: refreshUsers } = useDatabaseApp();
  const { users } = state;
  const { isMobile } = useResponsive();
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState<'all' | 'admin' | 'member' | 'observer'>('all');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadUsers();
    }
  }, [isOpen]);

  const loadUsers = async () => {
    setLoading(true);
    try {
      console.log('🔄 Loading users from API...');
      await refreshUsers();
      console.log('✅ Users loaded successfully');
    } catch (err) {
      setError('Error loading users');
      console.error('❌ Error loading users:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = async (userData: UserFormData) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('👤 Creating user via API:', {
        username: userData.username,
        email: userData.email,
        role: userData.role
      });

      const newUser = await ApiService.createUser(userData);
      console.log('✅ User created successfully:', newUser.name);

      // Update local state immediately
      dispatch({ type: 'ADD_USER', payload: newUser });

      // Show success message
      setSuccess(`Usuario "${newUser.name}" creado exitosamente`);

      // Switch back to list view after a short delay
      setTimeout(() => {
        setViewMode('list');
        setSuccess(null);
      }, 2000);

      // Reload users to ensure consistency
      await loadUsers();

      console.log('✅ User creation process completed successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error creating user';
      setError(errorMessage);
      console.error('❌ Error creating user:', err);

      // Don't switch views if there's an error, stay on create form
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateUser = async (id: string, updates: Partial<User>) => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔄 Updating user via API:', { id, updates });
      const updatedUser = await ApiService.updateUser(id, updates);
      console.log('✅ User updated successfully:', updatedUser.name);

      dispatch({ type: 'UPDATE_USER', payload: updatedUser });
      await loadUsers(); // Reload to ensure consistency
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error updating user';
      setError(errorMessage);
      console.error('❌ Error updating user:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async (id: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este usuario?')) return;

    setLoading(true);
    setError(null);

    try {
      console.log('🗑️ Deleting user via API:', id);
      await ApiService.deleteUser(id);
      console.log('✅ User deleted successfully');

      dispatch({ type: 'DELETE_USER', payload: id });
      await loadUsers(); // Reload to ensure consistency
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error deleting user';
      setError(errorMessage);
      console.error('❌ Error deleting user:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleUserStatus = async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔄 Toggling user status via API:', id);

      // Get current user to determine new status
      const currentUser = users.find(u => u.id === id);
      if (!currentUser) {
        throw new Error('Usuario no encontrado');
      }

      const newStatus = currentUser.isActive !== false ? false : true;
      const updatedUser = await ApiService.updateUser(id, { isActive: newStatus });

      console.log('✅ User status updated successfully:', updatedUser.name, 'Active:', updatedUser.isActive);

      dispatch({ type: 'UPDATE_USER', payload: updatedUser });
      await loadUsers(); // Reload to ensure consistency
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error updating user status';
      setError(errorMessage);
      console.error('❌ Error updating user status:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async (id: string) => {
    if (!confirm('¿Estás seguro de que quieres restablecer la contraseña de este usuario?')) return;

    setLoading(true);
    setError(null);

    try {
      console.log('🔑 Resetting password via API:', id);

      const newPassword = 'temp123';
      const updatedUser = await ApiService.updateUser(id, { password: newPassword });

      console.log('✅ Password reset successfully for user:', updatedUser.name);

      alert(`Contraseña restablecida exitosamente para ${updatedUser.name}.\nNueva contraseña: ${newPassword}`);

      // No need to update local state as password is not displayed
      await loadUsers(); // Reload to ensure consistency
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error resetting password';
      setError(errorMessage);
      console.error('❌ Error resetting password:', err);
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (user.username && user.username.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    
    return matchesSearch && matchesRole;
  });

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      title="Sistema de Gestión de Usuarios"
      size="xl"
      className="flex flex-col"
    >

          {/* Navigation Tabs */}
          <div className={`flex ${isMobile ? 'overflow-x-auto' : ''} border-b border-gray-200 flex-shrink-0`}>
            <button
              onClick={() => setViewMode('list')}
              className={`flex items-center space-x-2 ${isMobile ? 'px-4 py-3' : 'px-6 py-3'} font-medium transition-colors whitespace-nowrap min-h-[44px] ${
                viewMode === 'list'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Users className="w-4 h-4 flex-shrink-0" />
              <span className={isMobile ? 'text-sm' : ''}>{isMobile ? 'Lista' : 'Lista de Usuarios'}</span>
            </button>
            <button
              onClick={() => setViewMode('create')}
              className={`flex items-center space-x-2 ${isMobile ? 'px-4 py-3' : 'px-6 py-3'} font-medium transition-colors whitespace-nowrap min-h-[44px] ${
                viewMode === 'create'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <UserPlus className="w-4 h-4 flex-shrink-0" />
              <span className={isMobile ? 'text-sm' : ''}>{isMobile ? 'Crear' : 'Crear Usuario'}</span>
            </button>
            <button
              onClick={() => setViewMode('metrics')}
              className={`flex items-center space-x-2 ${isMobile ? 'px-4 py-3' : 'px-6 py-3'} font-medium transition-colors whitespace-nowrap min-h-[44px] ${
                viewMode === 'metrics'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <BarChart3 className="w-4 h-4 flex-shrink-0" />
              <span className={isMobile ? 'text-sm' : ''}>Métricas</span>
            </button>
            <button
              onClick={() => setViewMode('permissions')}
              className={`flex items-center space-x-2 ${isMobile ? 'px-4 py-3' : 'px-6 py-3'} font-medium transition-colors whitespace-nowrap min-h-[44px] ${
                viewMode === 'permissions'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Shield className="w-4 h-4 flex-shrink-0" />
              <span className={isMobile ? 'text-sm' : ''}>Permisos</span>
            </button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          {/* Success Display */}
          {success && (
            <div className="mx-6 mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-green-700 text-sm">{success}</p>
            </div>
          )}

          {/* Content */}
          <div className={`${isMobile ? 'p-4' : 'p-6'} overflow-y-auto flex-1 min-h-0`}>
            {viewMode === 'list' && (
              <div className="space-y-6">
                {/* Search and Filter Controls */}
                <div className="flex flex-col sm:flex-row gap-3 md:gap-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder={isMobile ? "Buscar usuarios..." : "Buscar usuarios por nombre, email o usuario..."}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className={`w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isMobile ? 'text-sm min-h-[44px]' : ''}`}
                    />
                  </div>
                  <div className="relative">
                    <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <select
                      value={roleFilter}
                      onChange={(e) => setRoleFilter(e.target.value as any)}
                      className={`pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isMobile ? 'text-sm min-h-[44px] w-full' : ''}`}
                    >
                      <option value="all">Todos los Roles</option>
                      <option value="admin">Administrador</option>
                      <option value="member">Miembro</option>
                      <option value="observer">Observador</option>
                    </select>
                  </div>
                </div>

                {/* User List */}
                <UserList
                  users={filteredUsers}
                  loading={loading}
                  onUpdateUser={handleUpdateUser}
                  onDeleteUser={handleDeleteUser}
                  onToggleStatus={handleToggleUserStatus}
                  onResetPassword={handleResetPassword}
                />
              </div>
            )}

            {viewMode === 'create' && (
              <UserForm
                onSubmit={handleCreateUser}
                loading={loading}
                onCancel={() => setViewMode('list')}
              />
            )}

            {viewMode === 'metrics' && (
              <UserMetrics users={users} />
            )}

            {viewMode === 'permissions' && (
              <UserPermissions users={users} />
            )}
          </div>
    </ResponsiveModal>
  );
}
