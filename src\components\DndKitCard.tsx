import React, { useState, useEffect } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useResponsive } from '../hooks/useResponsive';
import { Card as CardType, Label } from '../types';
import { 
  Calendar, 
  MessageCircle, 
  Paperclip, 
  CheckSquare, 
  User,
  Edit3
} from 'lucide-react';

interface DndKitCardProps {
  card: CardType;
  onClick?: () => void;
  onUpdate?: (card: CardType) => void;
  isDragOverlay?: boolean;
}

export function DndKitCard({ card, onClick, onUpdate, isDragOverlay = false }: DndKitCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editingTitle, setEditingTitle] = useState(false);
  const [title, setTitle] = useState(card.title);
  const { isMobile } = useResponsive();
  
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: sortableIsDragging,
  } = useSortable({
    id: card.id,
    data: {
      type: 'card',
      card,
    },
    disabled: isDragOverlay, // Disable sorting when used as overlay
  });

  const style = {
    transform: CSS.Translate.toString(transform),
    transition,
  };

  const checklist = card.checklist || [];
  const completedTasks = checklist.filter(item => item && item.completed).length;
  const totalTasks = checklist.length;
  const isOverdue = card.dueDate && new Date(card.dueDate) < new Date();

  // Sync title when card prop changes
  useEffect(() => {
    setTitle(card.title);
  }, [card.title]);
  
  const handleClick = (e: React.MouseEvent) => {
    // Simple click handler - no drag interference since drag is handled by separate area
    if (onClick) {
      e.stopPropagation();
      onClick();
    }
  };

  const handleDoubleClick = () => {
    setEditingTitle(true);
  };

  const handleTitleSave = () => {
    const trimmedTitle = title.trim();
    if (onUpdate && trimmedTitle !== card.title && trimmedTitle.length > 0) {
      onUpdate({ ...card, title: trimmedTitle, updatedAt: new Date() });
    } else if (trimmedTitle.length === 0) {
      // Don't allow empty titles
      setTitle(card.title);
    }
    setEditingTitle(false);
  };

  const handleTitleCancel = () => {
    setTitle(card.title);
    setEditingTitle(false);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`card-container bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 group overflow-hidden relative mb-2 min-h-[44px] w-full ${
        sortableIsDragging && !isDragOverlay ? 'opacity-50' : ''
      } ${isDragOverlay ? 'rotate-3 shadow-2xl' : ''} ${isMobile ? 'card-mobile' : ''} hover:border-blue-200`}
    >
      {/* Drag Handle - Solo esta área es draggable */}
      <div
        {...attributes}
        {...listeners}
        className={`absolute top-0 left-0 w-8 h-full cursor-grab active:cursor-grabbing z-10 flex items-center justify-center ${
          isMobile ? 'w-12' : 'w-8'
        } hover:bg-gray-50 transition-colors`}
        title="Arrastra para mover la tarjeta"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="w-3 h-3 grid grid-cols-2 gap-0.5 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
        </div>
      </div>

      {/* Contenido clickeable */}
      <div
        onClick={handleClick}
        onDoubleClick={handleDoubleClick}
        className={`cursor-pointer ${isMobile ? 'ml-12 pr-2' : 'ml-8 pr-3'} h-full flex-1 min-w-0`}
      >
      {/* Cover Image/Color */}
      {card.cover && (
        <div
          className="w-full h-16 mb-2 overflow-hidden"
          style={{
            background: card.cover.startsWith('http') || card.cover.startsWith('data:')
              ? `url(${card.cover}) center/cover`
              : card.cover,
            borderTopLeftRadius: '0.5rem',
            borderTopRightRadius: '0.5rem',
            margin: '-1px -1px 0 -1px',
            width: 'calc(100% + 2px)'
          }}
        />
      )}

      <div className={`${isMobile ? 'p-2' : 'p-3'}`}>
        {/* Labels */}
        {card.labels && card.labels.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-2">
            {card.labels.map((label) => (
              <span
                key={label.id}
                className="inline-block px-2 py-1 text-xs font-medium text-white rounded"
                style={{ backgroundColor: label.color }}
              >
                {label.name}
              </span>
            ))}
          </div>
        )}

        {/* Card Title */}
        {editingTitle ? (
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            onBlur={handleTitleSave}
            onKeyDown={(e) => {
              e.stopPropagation();
              if (e.key === 'Enter') {
                handleTitleSave();
              } else if (e.key === 'Escape') {
                handleTitleCancel();
              }
            }}
            onClick={(e) => e.stopPropagation()}
            className={`card-title ${isMobile ? 'text-xs' : 'text-sm'} font-medium text-gray-900 mb-2 leading-tight break-words bg-white border-2 border-blue-400 rounded px-2 py-1 w-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm`}
            autoFocus
            placeholder="Título de la tarjeta"
          />
        ) : (
          <h3
            className={`card-title ${isMobile ? 'text-xs' : 'text-sm'} font-medium text-gray-900 mb-2 leading-tight break-words cursor-text hover:bg-gray-50 hover:shadow-sm rounded px-2 py-1 transition-all duration-200 border border-transparent hover:border-gray-200`}
            onClick={(e) => {
              e.stopPropagation();
              setEditingTitle(true);
            }}
            title="Haz clic para editar el título"
          >
            {card.title}
          </h3>
        )}

        {/* Card Description Preview */}
        {card.description && (
          <p className={`card-description ${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 mb-2 break-words`}>
            {card.description}
          </p>
        )}

        {/* Card Stats */}
        <div className={`card-stats flex items-center justify-between ${isMobile ? 'text-xs' : 'text-xs'} text-gray-500 min-w-0`}>
          <div className={`flex items-center ${isMobile ? 'space-x-2' : 'space-x-3'} flex-wrap min-w-0`}>
            {/* Due Date */}
            {card.dueDate && (
              <div className={`flex items-center space-x-1 ${isOverdue ? 'text-red-500' : 'text-gray-500'}`}>
                <Calendar className="w-3 h-3" />
                <span className={isOverdue ? 'bg-red-100 text-red-700 px-1 rounded' : ''}>
                  {new Date(card.dueDate).toLocaleDateString()}
                </span>
              </div>
            )}

            {/* Checklist Progress */}
            {totalTasks > 0 && (
              <div className="flex items-center space-x-1">
                <CheckSquare className="w-3 h-3" />
                <span className={completedTasks === totalTasks ? 'text-green-600' : ''}>
                  {completedTasks}/{totalTasks}
                </span>
              </div>
            )}

            {/* Comments */}
            {card.comments && card.comments.length > 0 && (
              <div className="flex items-center space-x-1">
                <MessageCircle className="w-3 h-3" />
                <span>{card.comments.length}</span>
              </div>
            )}

            {/* Attachments */}
            {card.attachments && card.attachments.length > 0 && (
              <div className="flex items-center space-x-1">
                <Paperclip className="w-3 h-3" />
                <span>{card.attachments.length}</span>
              </div>
            )}
          </div>

          {/* Assigned Members */}
          {card.assignedMembers && card.assignedMembers.length > 0 && (
            <div className="flex -space-x-1 flex-shrink-0">
              {card.assignedMembers.slice(0, isMobile ? 2 : 3).map((member) => (
                <img
                  key={member?.id || Math.random()}
                  src={member?.avatar || ''}
                  alt={member?.name || 'Member'}
                  className={`${isMobile ? 'w-5 h-5' : 'w-6 h-6'} rounded-full border-2 border-white object-cover`}
                  title={member?.name || 'Member'}
                />
              ))}
              {card.assignedMembers.length > (isMobile ? 2 : 3) && (
                <div className={`${isMobile ? 'w-5 h-5' : 'w-6 h-6'} rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-xs font-medium text-gray-600`}>
                  +{card.assignedMembers.length - (isMobile ? 2 : 3)}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      </div>

      {/* Quick Edit Button */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          setEditingTitle(true);
        }}
        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-100 rounded transition-opacity"
        title="Editar título"
      >
        <Edit3 className="w-3 h-3 text-gray-500" />
      </button>
    </div>
  );
}
