import { useState, useCallback } from 'react';
import { Card, List } from '../types';

type DraggedItem = Card | List | null;
type DragType = 'card' | 'list';

export function useDragAndDrop() {
  const [draggedItem, setDraggedItem] = useState<DraggedItem>(null);
  const [dragType, setDragType] = useState<DragType | null>(null);
  const [dragOverItem, setDragOverItem] = useState<string | null>(null);
  const [placeholderPosition, setPlaceholderPosition] = useState<number | null>(null);

  const handleDragStart = useCallback((e: React.DragEvent, item: Card | List, type: DragType) => {
    setDraggedItem(item);
    setDragType(type);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', item.id);
    e.dataTransfer.setData('application/json', JSON.stringify({ id: item.id, type }));

    // Add visual feedback
    if (e.target instanceof HTMLElement) {
      e.target.style.opacity = '0.5';
    }

    // Set initial placeholder position for lists
    if (type === 'list' && 'position' in item) {
      setPlaceholderPosition(item.position);
    }
  }, []);

  const handleDragEnd = useCallback((e: React.DragEvent) => {
    setDraggedItem(null);
    setDragType(null);
    setDragOverItem(null);
    setPlaceholderPosition(null);

    // Reset visual feedback
    if (e.target instanceof HTMLElement) {
      e.target.style.opacity = '1';
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent, targetId: string, targetType: DragType = 'card') => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';

    // Allow dropping cards on lists and lists on drop zones
    if (
      (dragType === 'card' && targetType === 'card') ||
      (dragType === 'list' && targetType === 'list')
    ) {
      setDragOverItem(targetId);

      // Update placeholder position for lists
      if (dragType === 'list' && targetType === 'list') {
        const newPosition = parseInt(targetId);
        if (!isNaN(newPosition)) {
          setPlaceholderPosition(newPosition);
        }
      }
    }
  }, [dragType]);

  const handleDragLeave = useCallback(() => {
    setDragOverItem(null);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent, targetId: string, onCardDrop?: (card: Card, listId: string) => void, onListDrop?: (list: List, newPosition: number) => void) => {
    e.preventDefault();
    setDragOverItem(null);

    if (draggedItem && dragType) {
      if (dragType === 'card' && onCardDrop) {
        onCardDrop(draggedItem as Card, targetId);
      } else if (dragType === 'list' && onListDrop) {
        // Parse the target position from the targetId
        const newPosition = parseInt(targetId);
        onListDrop(draggedItem as List, newPosition);
      }
      setDraggedItem(null);
      setDragType(null);
    }
  }, [draggedItem, dragType]);

  return {
    draggedItem,
    dragType,
    dragOverItem,
    placeholderPosition,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDragLeave,
    handleDrop
  };
}