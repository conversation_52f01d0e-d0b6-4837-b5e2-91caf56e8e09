import { AppNotification, Card, User, Activity } from '../types';

export type NotificationType = 'mention' | 'due_date' | 'assignment' | 'comment' | 'card_moved' | 'card_created';

interface NotificationData {
  type: NotificationType;
  title: string;
  message: string;
  boardId?: string;
  cardId?: string;
  userId?: string;
}

export class NotificationService {
  private static instance: NotificationService;
  private notifications: AppNotification[] = [];
  private listeners: ((notifications: AppNotification[]) => void)[] = [];

  private constructor() {}

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Add a new notification
   */
  addNotification(data: NotificationData): AppNotification {
    const notification: AppNotification = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      type: data.type,
      title: data.title,
      message: data.message,
      read: false,
      createdAt: new Date(),
      boardId: data.boardId,
      cardId: data.cardId
    };

    this.notifications.unshift(notification);
    
    // Keep only last 50 notifications
    if (this.notifications.length > 50) {
      this.notifications = this.notifications.slice(0, 50);
    }

    this.notifyListeners();
    this.showBrowserNotification(notification);
    
    return notification;
  }

  /**
   * Mark notification as read
   */
  markAsRead(notificationId: string): void {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      this.notifyListeners();
    }
  }

  /**
   * Mark all notifications as read
   */
  markAllAsRead(): void {
    this.notifications.forEach(n => n.read = true);
    this.notifyListeners();
  }

  /**
   * Get all notifications
   */
  getNotifications(): AppNotification[] {
    return [...this.notifications];
  }

  /**
   * Get unread notifications count
   */
  getUnreadCount(): number {
    return this.notifications.filter(n => !n.read).length;
  }

  /**
   * Subscribe to notification changes
   */
  subscribe(listener: (notifications: AppNotification[]) => void): () => void {
    this.listeners.push(listener);

    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Clear all notifications
   */
  clearAll(): void {
    this.notifications = [];
    this.notifyListeners();
  }

  /**
   * Show browser notification if permission granted
   */
  private showBrowserNotification(notification: AppNotification): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id
      });
    }
  }

  /**
   * Request browser notification permission
   */
  static async requestPermission(): Promise<NotificationPermission> {
    if ('Notification' in window) {
      return await Notification.requestPermission();
    }
    return 'denied';
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener([...this.notifications]));
  }

  // Specific notification creators
  
  /**
   * Create mention notification
   */
  createMentionNotification(mentionedUser: User, card: Card, author: User, boardId: string): AppNotification {
    return this.addNotification({
      type: 'mention',
      title: 'Te mencionaron',
      message: `${author.name} te mencionó en "${card.title}"`,
      boardId,
      cardId: card.id,
      userId: mentionedUser.id
    });
  }

  /**
   * Create due date notification
   */
  createDueDateNotification(card: Card, boardId: string, daysUntilDue: number): AppNotification {
    const timeText = daysUntilDue === 0 ? 'hoy' :
                    daysUntilDue === 1 ? 'mañana' :
                    daysUntilDue < 0 ? `hace ${Math.abs(daysUntilDue)} día${Math.abs(daysUntilDue) !== 1 ? 's' : ''}` :
                    `en ${daysUntilDue} día${daysUntilDue !== 1 ? 's' : ''}`;

    return this.addNotification({
      type: 'due_date',
      title: daysUntilDue < 0 ? 'Tarea vencida' : 'Tarea próxima a vencer',
      message: `"${card.title}" vence ${timeText}`,
      boardId,
      cardId: card.id
    });
  }

  /**
   * Create assignment notification
   */
  createAssignmentNotification(assignedUser: User, card: Card, assigner: User, boardId: string): AppNotification {
    return this.addNotification({
      type: 'assignment',
      title: 'Nueva asignación',
      message: `${assigner.name} te asignó la tarjeta "${card.title}"`,
      boardId,
      cardId: card.id,
      userId: assignedUser.id
    });
  }

  /**
   * Create comment notification
   */
  createCommentNotification(card: Card, author: User, boardId: string, assignedUsers: User[]): AppNotification[] {
    const notifications: AppNotification[] = [];

    assignedUsers.forEach(user => {
      if (user.id !== author.id) { // Don't notify the author
        notifications.push(this.addNotification({
          type: 'comment',
          title: 'Nuevo comentario',
          message: `${author.name} comentó en "${card.title}"`,
          boardId,
          cardId: card.id,
          userId: user.id
        }));
      }
    });

    return notifications;
  }

  /**
   * Create card moved notification
   */
  createCardMovedNotification(card: Card, fromList: string, toList: string, mover: User, boardId: string): AppNotification {
    return this.addNotification({
      type: 'card_moved',
      title: 'Tarjeta movida',
      message: `${mover.name} movió "${card.title}" de ${fromList} a ${toList}`,
      boardId,
      cardId: card.id
    });
  }

  /**
   * Create card created notification
   */
  createCardCreatedNotification(card: Card, creator: User, listTitle: string, boardId: string): AppNotification {
    return this.addNotification({
      type: 'card_created',
      title: 'Nueva tarjeta creada',
      message: `${creator.name} creó "${card.title}" en ${listTitle}`,
      boardId,
      cardId: card.id
    });
  }

  /**
   * Check for due date notifications
   */
  checkDueDateNotifications(cards: Card[], boardId: string): void {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    cards.forEach(card => {
      if (!card.dueDate) return;
      
      const dueDate = new Date(card.dueDate.getFullYear(), card.dueDate.getMonth(), card.dueDate.getDate());
      const diffTime = dueDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      // Notify for overdue, due today, or due tomorrow
      if (diffDays <= 1 && diffDays >= -1) {
        // Check if we already notified for this card today
        const existingNotification = this.notifications.find(n => 
          n.cardId === card.id && 
          n.type === 'due_date' &&
          n.createdAt.toDateString() === now.toDateString()
        );
        
        if (!existingNotification) {
          this.createDueDateNotification(card, boardId, diffDays);
        }
      }
    });
  }
}
