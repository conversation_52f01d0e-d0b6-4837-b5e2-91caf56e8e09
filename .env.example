# Environment Configuration for Trello Application

# API Configuration
# Override the automatic API URL detection if needed
# VITE_API_URL=http://localhost:3001

# Development Tunnel Configuration
# When using development tunnels (devtunnels, ngrok, etc.)
# the application will automatically detect and configure the API URL
# No manual configuration needed for most tunnel services

# Production Configuration
# For production builds, set the API URL to your production server
# VITE_API_URL=https://your-production-api.com

# Database Configuration (Server-side)
# DATABASE_URL="file:./dev.db"

# Server Configuration (Server-side)
# PORT=3001
# NODE_ENV=development

# Notes:
# - The application automatically detects tunnel environments
# - Supported tunnel services: devtunnels.ms, ngrok.io, tunnels.dev, localhost.run, serveo.net
# - For local development, no configuration is needed (defaults to localhost:3001)
# - For production, set VITE_API_URL to your production API server
