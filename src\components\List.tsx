import React, { useState } from 'react';
import { List as ListType, Card as CardType } from '../types';
import { Card } from './Card';
import { Plus, MoreHorizontal, GripVertical } from 'lucide-react';

interface ListProps {
  list: ListType;
  onAddCard: (listId: string, title: string) => void;
  onDragStart: (e: React.DragEvent, item: CardType | ListType, type: 'card' | 'list') => void;
  onDragEnd: (e: React.DragEvent) => void;
  onDragOver: (e: React.DragEvent, targetId: string, targetType?: 'card' | 'list') => void;
  onDragLeave: () => void;
  onDrop: (e: React.DragEvent, targetId: string) => void;
  isDragOver: boolean;
  onCardClick: (card: CardType) => void;
  position: number;
}

export function List({
  list,
  onAddCard,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDragLeave,
  onDrop,
  isDragOver,
  onCardClick,
  position
}: ListProps) {
  const [isAddingCard, setIsAddingCard] = useState(false);
  const [newCardTitle, setNewCardTitle] = useState('');

  const handleAddCard = () => {
    if (newCardTitle.trim()) {
      onAddCard(list.id, newCardTitle.trim());
      setNewCardTitle('');
      setIsAddingCard(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddCard();
    } else if (e.key === 'Escape') {
      setIsAddingCard(false);
      setNewCardTitle('');
    }
  };

  return (
    <div
      className={`flex flex-col w-72 bg-gray-50 rounded-lg shadow-sm transition-all duration-200 ${
        isDragOver ? 'ring-2 ring-blue-400 shadow-lg transform scale-105' : 'hover:shadow-md'
      }`}
    >
      {/* List Header */}
      <div
        className="flex items-center justify-between p-3 border-b bg-white rounded-t-lg cursor-grab active:cursor-grabbing group"
        draggable
        onDragStart={(e) => onDragStart(e, list, 'list')}
        onDragEnd={onDragEnd}
      >
        <div className="flex items-center space-x-2">
          <div className="relative">
            <GripVertical className="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
              Arrastra para mover la lista
            </div>
          </div>
          <h2 className="font-semibold text-gray-900">{list.title}</h2>
        </div>
        <div className="flex items-center space-x-1">
          <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
            {list.cards.length}
          </span>
          <button className="p-1 hover:bg-gray-100 rounded">
            <MoreHorizontal className="w-4 h-4 text-gray-500" />
          </button>
        </div>
      </div>

      {/* Cards Container */}
      <div
        className={`flex-1 p-3 min-h-32 transition-colors ${
          isDragOver ? 'bg-blue-50 border-2 border-blue-300 border-dashed' : ''
        }`}
        onDragOver={(e) => onDragOver(e, list.id, 'card')}
        onDragLeave={onDragLeave}
        onDrop={(e) => onDrop(e, list.id)}
      >
        <div className="space-y-4">
          {list.cards
            .sort((a, b) => a.position - b.position)
            .map((card) => (
              <Card
                key={card.id}
                card={card}
                onDragStart={(e) => onDragStart(e, card, 'card')}
                onDragEnd={onDragEnd}
                onClick={() => onCardClick(card)}
              />
            ))}
        </div>

        {/* Add Card Form */}
        {isAddingCard ? (
          <div className="mt-3">
            <textarea
              value={newCardTitle}
              onChange={(e) => setNewCardTitle(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Ingresa un título para esta tarjeta..."
              className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
              autoFocus
            />
            <div className="flex items-center space-x-2 mt-2">
              <button
                onClick={handleAddCard}
                className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Agregar Tarjeta
              </button>
              <button
                onClick={() => {
                  setIsAddingCard(false);
                  setNewCardTitle('');
                }}
                className="px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Cancelar
              </button>
            </div>
          </div>
        ) : (
          <button
            onClick={() => setIsAddingCard(true)}
            className="w-full mt-3 p-3 text-gray-600 hover:bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors flex items-center justify-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Agregar una tarjeta</span>
          </button>
        )}
      </div>
    </div>
  );
}