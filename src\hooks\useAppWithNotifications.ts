import { useDatabaseApp } from '../context/DatabaseAppContext';
import { NotificationService } from '../services/NotificationService';
import { Card, User, Comment } from '../types';

export function useAppWithNotifications() {
  const databaseApp = useDatabaseApp();
  const { state, dispatch } = databaseApp;
  const notificationService = NotificationService.getInstance();

  // Enhanced dispatch that triggers notifications
  const enhancedDispatch = (action: any) => {
    // Call original dispatch first
    dispatch(action);

    // Then handle notifications based on action type
    switch (action.type) {
      case 'ADD_CARD':
        if (state.currentBoard) {
          const list = state.currentBoard.lists.find(l => l.id === action.payload.listId);
          if (list) {
            notificationService.createCardCreatedNotification(
              action.payload.card,
              state.currentUser,
              list.title,
              state.currentBoard.id
            );
          }
        }
        break;

      case 'MOVE_CARD':
        if (state.currentBoard) {
          const fromList = state.currentBoard.lists.find(l => l.id === action.payload.fromListId);
          const toList = state.currentBoard.lists.find(l => l.id === action.payload.toListId);
          const card = fromList?.cards.find(c => c.id === action.payload.cardId);
          
          if (fromList && toList && card && fromList.id !== toList.id) {
            notificationService.createCardMovedNotification(
              card,
              fromList.title,
              toList.title,
              state.currentUser,
              state.currentBoard.id
            );
          }
        }
        break;

      case 'UPDATE_CARD':
        // Check if members were added
        if (state.currentBoard) {
          const list = state.currentBoard.lists.find(l => l.id === action.payload.listId);
          const originalCard = list?.cards.find(c => c.id === action.payload.card.id);
          
          if (originalCard && list) {
            const newMembers = action.payload.card.assignedMembers.filter(
              (member: User) => !originalCard.assignedMembers.some(om => om.id === member.id)
            );
            
            // Notify newly assigned members
            newMembers.forEach((member: User) => {
              if (member.id !== state.currentUser?.id) {
                notificationService.createAssignmentNotification(
                  member,
                  action.payload.card,
                  state.currentUser,
                  state.currentBoard!.id
                );
              }
            });

            // Check if new comments were added
            const newComments = action.payload.card.comments.filter(
              (comment: Comment) => !originalCard.comments.some(oc => oc.id === comment.id)
            );

            if (newComments.length > 0) {
              // Notify assigned members about new comments
              notificationService.createCommentNotification(
                action.payload.card,
                state.currentUser,
                state.currentBoard!.id,
                action.payload.card.assignedMembers
              );

              // Check for mentions in comments
              newComments.forEach((comment: Comment) => {
                const mentionRegex = /@(\w+)/g;
                const mentions = comment.text.match(mentionRegex);
                
                if (mentions && state.currentBoard) {
                  mentions.forEach(mention => {
                    const username = mention.substring(1);
                    const mentionedUser = state.currentBoard!.members.find(
                      m => m.name.toLowerCase().includes(username.toLowerCase())
                    );
                    
                    if (mentionedUser && mentionedUser.id !== state.currentUser?.id) {
                      notificationService.createMentionNotification(
                        mentionedUser,
                        action.payload.card,
                        state.currentUser,
                        state.currentBoard!.id
                      );
                    }
                  });
                }
              });
            }
          }
        }
        break;
    }
  };

  // Check for due date notifications periodically
  const checkDueDateNotifications = () => {
    if (state.currentBoard) {
      const allCards = state.currentBoard.lists.flatMap(list => list.cards);
      notificationService.checkDueDateNotifications(allCards, state.currentBoard.id);
    }
  };

  return {
    state,
    dispatch: enhancedDispatch,
    checkDueDateNotifications,
    notificationService
  };
}
