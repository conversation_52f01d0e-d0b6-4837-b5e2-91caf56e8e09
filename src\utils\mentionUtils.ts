import { BoardMember, Team, User } from '../types';

/**
 * Get available users for @mentions based on board's team assignment
 * If board is assigned to a team, only show team members
 * If no team assigned, show all board members (current behavior)
 */
export function getAvailableUsersForMentions(
  currentBoard: any,
  teams: Team[]
): BoardMember[] {
  if (!currentBoard) {
    console.log('❌ getAvailableUsersForMentions: No current board');
    return [];
  }

  console.log('🔍 getAvailableUsersForMentions: Starting analysis', {
    boardId: currentBoard.id,
    boardTitle: currentBoard.title,
    boardTeamId: currentBoard.teamId,
    boardMembersCount: currentBoard.members?.length || 0,
    boardMemberNames: currentBoard.members?.map((m: any) => m.name) || [],
    teamsCount: teams?.length || 0
  });

  // If board is assigned to a team, only show team members
  if (currentBoard.teamId) {
    console.log('🏢 Board has team assigned:', currentBoard.teamId);
    const boardTeam = teams.find(team => team.id === currentBoard.teamId);

    if (boardTeam) {
      console.log('✅ Found board team:', {
        teamId: boardTeam.id,
        teamName: boardTeam.name,
        teamMembersCount: boardTeam.members?.length || 0,
        teamMemberUserIds: boardTeam.members?.map(m => m.userId) || []
      });

      // Get team member user IDs
      const teamMemberIds = boardTeam.members.map(member => member.userId);

      // Filter board members to only include team members
      const filteredMembers = currentBoard.members.filter((boardMember: BoardMember) =>
        teamMemberIds.includes(boardMember.id)
      );

      console.log('🎯 @Mentions filtered by team:', {
        boardTitle: currentBoard.title,
        teamName: boardTeam.name,
        teamMemberIds,
        boardMemberIds: currentBoard.members?.map((m: any) => m.id) || [],
        filteredCount: filteredMembers.length,
        filteredNames: filteredMembers.map((m: any) => m.name)
      });

      if (filteredMembers.length === 0) {
        console.warn('⚠️ No team members found in board members! Team members may not be synced to board.');
      }

      return filteredMembers;
    } else {
      console.log('❌ Board team not found in teams array:', {
        boardTeamId: currentBoard.teamId,
        availableTeamIds: teams?.map(t => t.id) || []
      });
    }
  } else {
    console.log('📋 Board has no team assigned');
  }

  // If no team assigned, show all board members (current behavior)
  const allMembers = currentBoard.members || [];
  console.log('👥 @Mentions showing all board members:', {
    boardTitle: currentBoard.title,
    count: allMembers.length,
    memberNames: allMembers.map((m: any) => m.name)
  });

  return allMembers;
}

/**
 * Extract mentions from text and filter by available users
 */
export function extractMentionsFromText(
  text: string,
  availableUsers: BoardMember[]
): BoardMember[] {
  const mentionRegex = /@(\w+)/g;
  const mentions: BoardMember[] = [];
  let match;

  while ((match = mentionRegex.exec(text)) !== null) {
    const memberName = match[1];
    const member = availableUsers.find(m => 
      m.name.toLowerCase() === memberName.toLowerCase()
    );
    if (member && !mentions.find(m => m.id === member.id)) {
      mentions.push(member);
    }
  }

  return mentions;
}

/**
 * Validate mentions in text against available users
 */
export function validateMentions(
  text: string,
  availableUsers: BoardMember[]
): { valid: BoardMember[], invalid: string[] } {
  const mentionRegex = /@(\w+)/g;
  const valid: BoardMember[] = [];
  const invalid: string[] = [];
  let match;

  while ((match = mentionRegex.exec(text)) !== null) {
    const memberName = match[1];
    const member = availableUsers.find(m => 
      m.name.toLowerCase() === memberName.toLowerCase()
    );
    
    if (member) {
      if (!valid.find(m => m.id === member.id)) {
        valid.push(member);
      }
    } else {
      if (!invalid.includes(memberName)) {
        invalid.push(memberName);
      }
    }
  }

  return { valid, invalid };
}

/**
 * Get available users for card member assignment based on user role and board's team assignment
 * - ADMIN users: Can see all board members (no restrictions)
 * - MEMBER/OBSERVER users:
 *   - If board has team: Only team members
 *   - If board has no team: All board members
 */
export function getAvailableUsersForCardAssignment(
  currentBoard: any,
  teams: Team[],
  currentUser: User,
  allUsers: User[]
): User[] {
  if (!currentBoard || !currentUser) return [];

  // ADMIN users can see all board members without restrictions
  if (currentUser.role === 'admin') {
    // Return all users who are members of the board
    const boardMemberIds = currentBoard.members?.map((member: BoardMember) => member.id) || [];
    return allUsers.filter(user => boardMemberIds.includes(user.id));
  }

  // For MEMBER and OBSERVER users, apply team-based filtering
  // If board is assigned to a team, only show team members
  if (currentBoard.teamId) {
    const boardTeam = teams.find(team => team.id === currentBoard.teamId);
    if (boardTeam) {
      // Get team member user IDs
      const teamMemberIds = boardTeam.members.map(member => member.userId);

      // Filter to users who are both team members AND board members
      const boardMemberIds = currentBoard.members?.map((member: BoardMember) => member.id) || [];
      return allUsers.filter(user =>
        teamMemberIds.includes(user.id) && boardMemberIds.includes(user.id)
      );
    }
  }

  // If no team assigned, show all board members (current behavior)
  const boardMemberIds = currentBoard.members?.map((member: BoardMember) => member.id) || [];
  return allUsers.filter(user => boardMemberIds.includes(user.id));
}
