import React, { useState, useEffect } from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { Card, Label, User } from '../types';
import { 
  Search, 
  Filter, 
  X, 
  Calendar, 
  User as UserIcon, 
  Tag,
  CheckSquare,
  Clock,
  AlertCircle
} from 'lucide-react';

interface SearchFilters {
  query: string;
  labels: string[];
  members: string[];
  dueDate: 'overdue' | 'today' | 'week' | 'none' | '';
  hasChecklist: boolean | null;
  hasAttachments: boolean | null;
}

export function SearchAndFilter() {
  const { state, getCurrentBoard, updateSearchQuery, updateCardFilter } = useDatabaseApp();
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    query: state.searchQuery,
    labels: [],
    members: [],
    dueDate: '',
    hasChecklist: null,
    hasAttachments: null
  });

  const [filteredCards, setFilteredCards] = useState<Card[]>([]);

  // Update search query in global state
  useEffect(() => {
    updateSearchQuery(filters.query);
  }, [filters.query, updateSearchQuery]);

  // Filter cards based on current filters
  useEffect(() => {
    if (!state.currentBoard) {
      setFilteredCards([]);
      return;
    }

    const allCards = state.currentBoard.lists.flatMap(list => list.cards);
    
    const filtered = allCards.filter(card => {
      // Text search
      if (filters.query) {
        const searchLower = filters.query.toLowerCase();
        const matchesTitle = card.title.toLowerCase().includes(searchLower);
        const matchesDescription = card.description?.toLowerCase().includes(searchLower);
        const matchesComments = card.comments.some(comment => 
          comment.text.toLowerCase().includes(searchLower)
        );
        
        if (!matchesTitle && !matchesDescription && !matchesComments) {
          return false;
        }
      }

      // Label filter
      if (filters.labels.length > 0) {
        const hasMatchingLabel = card.labels.some(label => 
          filters.labels.includes(label.id)
        );
        if (!hasMatchingLabel) return false;
      }

      // Member filter
      if (filters.members.length > 0) {
        const hasMatchingMember = card.assignedMembers.some(member => 
          filters.members.includes(member.id)
        );
        if (!hasMatchingMember) return false;
      }

      // Due date filter
      if (filters.dueDate) {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

        switch (filters.dueDate) {
          case 'overdue':
            if (!card.dueDate || card.dueDate >= today) return false;
            break;
          case 'today':
            if (!card.dueDate || card.dueDate < today || card.dueDate >= new Date(today.getTime() + 24 * 60 * 60 * 1000)) return false;
            break;
          case 'week':
            if (!card.dueDate || card.dueDate < today || card.dueDate > weekFromNow) return false;
            break;
          case 'none':
            if (card.dueDate) return false;
            break;
        }
      }

      // Checklist filter
      if (filters.hasChecklist !== null) {
        const hasChecklist = card.checklist.length > 0;
        if (filters.hasChecklist !== hasChecklist) return false;
      }

      // Attachments filter
      if (filters.hasAttachments !== null) {
        const hasAttachments = card.attachments.length > 0;
        if (filters.hasAttachments !== hasAttachments) return false;
      }

      return true;
    });

    setFilteredCards(filtered);
  }, [filters, state.currentBoard]);

  const clearFilters = () => {
    setFilters({
      query: '',
      labels: [],
      members: [],
      dueDate: '',
      hasChecklist: null,
      hasAttachments: null
    });
  };

  const hasActiveFilters = filters.labels.length > 0 || 
                          filters.members.length > 0 || 
                          filters.dueDate !== '' || 
                          filters.hasChecklist !== null || 
                          filters.hasAttachments !== null;

  const toggleLabel = (labelId: string) => {
    setFilters(prev => ({
      ...prev,
      labels: prev.labels.includes(labelId)
        ? prev.labels.filter(id => id !== labelId)
        : [...prev.labels, labelId]
    }));
  };

  const toggleMember = (memberId: string) => {
    setFilters(prev => ({
      ...prev,
      members: prev.members.includes(memberId)
        ? prev.members.filter(id => id !== memberId)
        : [...prev.members, memberId]
    }));
  };

  return (
    <div className="bg-white border-b border-gray-200 p-4">
      {/* Search Bar */}
      <div className="flex items-center space-x-4 mb-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            value={filters.query}
            onChange={(e) => setFilters(prev => ({ ...prev, query: e.target.value }))}
            placeholder="Buscar tarjetas..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
            showFilters || hasActiveFilters
              ? 'bg-blue-50 border-blue-200 text-blue-700'
              : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
          }`}
        >
          <Filter className="w-4 h-4" />
          <span>Filtros</span>
          {hasActiveFilters && (
            <span className="bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {filters.labels.length + filters.members.length + (filters.dueDate ? 1 : 0) + 
               (filters.hasChecklist !== null ? 1 : 0) + (filters.hasAttachments !== null ? 1 : 0)}
            </span>
          )}
        </button>

        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <div className="border-t pt-4 space-y-4">
          {/* Labels Filter */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
              <Tag className="w-4 h-4 mr-1" />
              Etiquetas
            </h4>
            <div className="flex flex-wrap gap-2">
              {state.labels.map(label => (
                <button
                  key={label.id}
                  onClick={() => toggleLabel(label.id)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-all ${
                    filters.labels.includes(label.id)
                      ? 'text-white ring-2 ring-gray-400'
                      : 'text-white opacity-70 hover:opacity-100'
                  }`}
                  style={{ backgroundColor: label.color }}
                >
                  {label.name}
                </button>
              ))}
            </div>
          </div>

          {/* Members Filter */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
              <UserIcon className="w-4 h-4 mr-1" />
              Miembros
            </h4>
            <div className="flex flex-wrap gap-2">
              {state.currentBoard?.members.map(member => (
                <button
                  key={member.id}
                  onClick={() => toggleMember(member.id)}
                  className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm border transition-all ${
                    filters.members.includes(member.id)
                      ? 'bg-blue-50 border-blue-200 text-blue-700'
                      : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <img
                    src={member.avatar}
                    alt={member.name}
                    className="w-4 h-4 rounded-full"
                  />
                  <span>{member.name}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Due Date Filter */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
              <Calendar className="w-4 h-4 mr-1" />
              Fecha de vencimiento
            </h4>
            <div className="flex flex-wrap gap-2">
              {[
                { value: 'overdue', label: 'Vencidas', icon: AlertCircle, color: 'text-red-600' },
                { value: 'today', label: 'Hoy', icon: Clock, color: 'text-orange-600' },
                { value: 'week', label: 'Esta semana', icon: Calendar, color: 'text-blue-600' },
                { value: 'none', label: 'Sin fecha', icon: X, color: 'text-gray-600' }
              ].map(option => (
                <button
                  key={option.value}
                  onClick={() => setFilters(prev => ({ 
                    ...prev, 
                    dueDate: prev.dueDate === option.value ? '' : option.value as any
                  }))}
                  className={`flex items-center space-x-1 px-3 py-1 rounded-lg text-sm border transition-all ${
                    filters.dueDate === option.value
                      ? 'bg-blue-50 border-blue-200 text-blue-700'
                      : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <option.icon className={`w-3 h-3 ${option.color}`} />
                  <span>{option.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Other Filters */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Otros</h4>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setFilters(prev => ({ 
                  ...prev, 
                  hasChecklist: prev.hasChecklist === true ? null : true
                }))}
                className={`flex items-center space-x-1 px-3 py-1 rounded-lg text-sm border transition-all ${
                  filters.hasChecklist === true
                    ? 'bg-blue-50 border-blue-200 text-blue-700'
                    : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                }`}
              >
                <CheckSquare className="w-3 h-3" />
                <span>Con checklist</span>
              </button>
              
              <button
                onClick={() => setFilters(prev => ({ 
                  ...prev, 
                  hasAttachments: prev.hasAttachments === true ? null : true
                }))}
                className={`flex items-center space-x-1 px-3 py-1 rounded-lg text-sm border transition-all ${
                  filters.hasAttachments === true
                    ? 'bg-blue-50 border-blue-200 text-blue-700'
                    : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                }`}
              >
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clipRule="evenodd" />
                </svg>
                <span>Con archivos</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Results Summary */}
      {(filters.query || hasActiveFilters) && (
        <div className="mt-4 pt-4 border-t">
          <p className="text-sm text-gray-600">
            {filteredCards.length === 0 
              ? 'No se encontraron tarjetas que coincidan con los filtros'
              : `${filteredCards.length} tarjeta${filteredCards.length !== 1 ? 's' : ''} encontrada${filteredCards.length !== 1 ? 's' : ''}`
            }
          </p>
        </div>
      )}
    </div>
  );
}
