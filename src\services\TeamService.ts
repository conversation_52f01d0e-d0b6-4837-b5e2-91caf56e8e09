import { prisma, handlePrismaError } from '../lib/prisma';
import { Team, TeamMember } from '../types';
import { TeamRole, TeamVisibility } from '@prisma/client';

export class TeamService {
  // Create a new team
  static async createTeam(teamData: Omit<Team, 'id' | 'createdAt' | 'updatedAt'>, creatorId: string): Promise<Team> {
    try {
      const team = await prisma.team.create({
        data: {
          name: teamData.name,
          description: teamData.description,
          avatar: teamData.avatar,
          visibility: teamData.visibility.toUpperCase() as TeamVisibility,
          settings: {
            create: {
              allowMembersToCreateBoards: teamData.settings?.allowMembersToCreateBoards || false,
              allowMembersToInvite: teamData.settings?.allowMembersToInvite || false,
              defaultBoardVisibility: 'TEAM',
              requireApprovalForJoining: teamData.settings?.requireApprovalForJoining || true,
            }
          },
          members: {
            create: {
              userId: creatorId,
              role: 'ADMIN',
            }
          }
        },
        include: {
          settings: true,
          members: {
            include: {
              user: true
            }
          },
          boards: true,
        }
      });

      return this.convertPrismaTeamToTeam(team);
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Get all teams
  static async getAllTeams(): Promise<Team[]> {
    try {
      const teams = await prisma.team.findMany({
        include: {
          settings: true,
          members: {
            include: {
              user: true
            }
          },
          boards: true,
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return teams.map(this.convertPrismaTeamToTeam);
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Get team by ID
  static async getTeamById(id: string): Promise<Team | null> {
    try {
      const team = await prisma.team.findUnique({
        where: { id },
        include: {
          settings: true,
          members: {
            include: {
              user: true
            }
          },
          boards: true,
        }
      });

      return team ? this.convertPrismaTeamToTeam(team) : null;
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Update team
  static async updateTeam(id: string, teamData: Partial<Team>): Promise<Team> {
    try {
      const team = await prisma.team.update({
        where: { id },
        data: {
          ...(teamData.name && { name: teamData.name }),
          ...(teamData.description && { description: teamData.description }),
          ...(teamData.avatar && { avatar: teamData.avatar }),
          ...(teamData.visibility && { visibility: teamData.visibility.toUpperCase() as TeamVisibility }),
          ...(teamData.settings && {
            settings: {
              upsert: {
                create: teamData.settings,
                update: teamData.settings,
              }
            }
          })
        },
        include: {
          settings: true,
          members: {
            include: {
              user: true
            }
          },
          boards: true,
        }
      });

      return this.convertPrismaTeamToTeam(team);
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Delete team
  static async deleteTeam(id: string): Promise<void> {
    try {
      await prisma.team.delete({
        where: { id }
      });
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Add member to team
  static async addMemberToTeam(teamId: string, userId: string, role: string = 'normal'): Promise<Team> {
    try {
      await prisma.teamMember.create({
        data: {
          teamId,
          userId,
          role: role.toUpperCase() as TeamRole,
        }
      });

      return this.getTeamById(teamId) as Promise<Team>;
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Remove member from team
  static async removeMemberFromTeam(teamId: string, userId: string): Promise<Team> {
    try {
      await prisma.teamMember.delete({
        where: {
          teamId_userId: {
            teamId,
            userId
          }
        }
      });

      return this.getTeamById(teamId) as Promise<Team>;
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Update member role
  static async updateMemberRole(teamId: string, userId: string, role: string): Promise<Team> {
    try {
      await prisma.teamMember.update({
        where: {
          teamId_userId: {
            teamId,
            userId
          }
        },
        data: {
          role: role.toUpperCase() as TeamRole
        }
      });

      return this.getTeamById(teamId) as Promise<Team>;
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Get teams for user
  static async getTeamsForUser(userId: string): Promise<Team[]> {
    try {
      const teams = await prisma.team.findMany({
        where: {
          members: {
            some: {
              userId
            }
          }
        },
        include: {
          settings: true,
          members: {
            include: {
              user: true
            }
          },
          boards: true,
        }
      });

      return teams.map(this.convertPrismaTeamToTeam);
    } catch (error) {
      handlePrismaError(error);
      throw error;
    }
  }

  // Convert Prisma team to our Team type
  private static convertPrismaTeamToTeam(prismaTeam: any): Team {
    return {
      id: prismaTeam.id,
      name: prismaTeam.name,
      description: prismaTeam.description || '',
      avatar: prismaTeam.avatar || '',
      visibility: prismaTeam.visibility.toLowerCase(),
      members: prismaTeam.members.map((member: any): TeamMember => ({
        userId: member.userId,
        role: member.role.toLowerCase(),
        joinedAt: new Date(member.joinedAt),
        invitedBy: member.invitedBy,
      })),
      boards: prismaTeam.boards.map((board: any) => board.id),
      settings: prismaTeam.settings ? {
        allowMembersToCreateBoards: prismaTeam.settings.allowMembersToCreateBoards,
        allowMembersToInvite: prismaTeam.settings.allowMembersToInvite,
        defaultBoardVisibility: prismaTeam.settings.defaultBoardVisibility.toLowerCase(),
        requireApprovalForJoining: prismaTeam.settings.requireApprovalForJoining,
      } : {
        allowMembersToCreateBoards: false,
        allowMembersToInvite: false,
        defaultBoardVisibility: 'team',
        requireApprovalForJoining: true,
      },
      createdAt: new Date(prismaTeam.createdAt),
      updatedAt: new Date(prismaTeam.updatedAt),
    };
  }
}
