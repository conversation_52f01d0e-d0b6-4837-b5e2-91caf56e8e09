import React, { useState, useEffect } from 'react';
import { useNotifications } from '../hooks/useNotifications';
import { Notification } from '../types';
import { 
  X, 
  Bell, 
  Calendar,
  MessageCircle,
  User,
  Move,
  Plus,
  AlertCircle
} from 'lucide-react';

interface ToastNotification extends Notification {
  isVisible: boolean;
  timeoutId?: NodeJS.Timeout;
}

export function ToastNotifications() {
  const { notifications } = useNotifications();
  const [toasts, setToasts] = useState<ToastNotification[]>([]);

  useEffect(() => {
    // Show toast for new notifications
    const latestNotification = notifications[0];
    if (latestNotification && !toasts.find(t => t.id === latestNotification.id)) {
      const toast: ToastNotification = {
        ...latestNotification,
        isVisible: true
      };

      setToasts(prev => [toast, ...prev.slice(0, 4)]); // Keep max 5 toasts

      // Auto-hide after 5 seconds
      const timeoutId = setTimeout(() => {
        hideToast(toast.id);
      }, 5000);

      toast.timeoutId = timeoutId;
    }
  }, [notifications]);

  const hideToast = (toastId: string) => {
    setToasts(prev => prev.map(toast => 
      toast.id === toastId 
        ? { ...toast, isVisible: false }
        : toast
    ));

    // Remove from array after animation
    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== toastId));
    }, 300);
  };

  const getToastIcon = (type: Notification['type']) => {
    switch (type) {
      case 'mention':
        return <User className="w-4 h-4 text-blue-500" />;
      case 'due_date':
        return <Calendar className="w-4 h-4 text-orange-500" />;
      case 'assignment':
        return <User className="w-4 h-4 text-green-500" />;
      case 'comment':
        return <MessageCircle className="w-4 h-4 text-purple-500" />;
      case 'card_moved':
        return <Move className="w-4 h-4 text-indigo-500" />;
      case 'card_created':
        return <Plus className="w-4 h-4 text-emerald-500" />;
      default:
        return <Bell className="w-4 h-4 text-gray-500" />;
    }
  };

  const getToastColor = (type: Notification['type']) => {
    switch (type) {
      case 'mention':
        return 'border-blue-200 bg-blue-50';
      case 'due_date':
        return 'border-orange-200 bg-orange-50';
      case 'assignment':
        return 'border-green-200 bg-green-50';
      case 'comment':
        return 'border-purple-200 bg-purple-50';
      case 'card_moved':
        return 'border-indigo-200 bg-indigo-50';
      case 'card_created':
        return 'border-emerald-200 bg-emerald-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  if (toasts.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className={`transform transition-all duration-300 ease-in-out ${
            toast.isVisible 
              ? 'translate-x-0 opacity-100' 
              : 'translate-x-full opacity-0'
          }`}
        >
          <div className={`max-w-sm w-full bg-white border rounded-lg shadow-lg p-4 ${getToastColor(toast.type)}`}>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-0.5">
                {getToastIcon(toast.type)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900">
                  {toast.title}
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  {toast.message}
                </p>
              </div>
              <button
                onClick={() => {
                  if (toast.timeoutId) {
                    clearTimeout(toast.timeoutId);
                  }
                  hideToast(toast.id);
                }}
                className="flex-shrink-0 p-1 hover:bg-gray-100 rounded"
              >
                <X className="w-3 h-3 text-gray-400" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
