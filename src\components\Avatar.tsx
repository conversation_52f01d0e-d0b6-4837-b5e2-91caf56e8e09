import React from 'react';

interface AvatarProps {
  src?: string;
  alt: string;
  name: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const sizeClasses = {
  xs: 'w-6 h-6 text-xs',
  sm: 'w-8 h-8 text-sm',
  md: 'w-10 h-10 text-sm',
  lg: 'w-12 h-12 text-lg',
  xl: 'w-20 h-20 text-2xl'
};

const bgColors = [
  'bg-blue-500',
  'bg-green-500',
  'bg-purple-500',
  'bg-pink-500',
  'bg-indigo-500',
  'bg-yellow-500',
  'bg-red-500',
  'bg-gray-500'
];

export function Avatar({ src, alt, name, size = 'md', className = '' }: AvatarProps) {
  // Generate consistent color based on name
  const colorIndex = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % bgColors.length;
  const bgColor = bgColors[colorIndex];
  
  const sizeClass = sizeClasses[size];
  const baseClasses = `${sizeClass} rounded-full flex items-center justify-center font-bold text-white object-cover`;
  
  if (src) {
    return (
      <img
        src={src}
        alt={alt}
        className={`${baseClasses} ${className}`}
      />
    );
  }
  
  return (
    <div className={`${baseClasses} ${bgColor} ${className}`}>
      {name.charAt(0).toUpperCase()}
    </div>
  );
}
