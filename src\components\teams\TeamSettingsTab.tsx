import React, { useState } from 'react';
import { Team } from '../../types';
import {
  Settings,
  Lock,
  Users,
  Building,
  Globe,
  Save,
  Trash2,
  AlertTriangle,
  Camera,
  Check,
  X
} from 'lucide-react';

interface TeamSettingsTabProps {
  team: Team;
  onUpdateTeam: (team: Team) => void;
  onDeleteTeam: (teamId: string) => void;
}

export function TeamSettingsTab({ team, onUpdateTeam, onDeleteTeam }: TeamSettingsTabProps) {
  const [formData, setFormData] = useState({
    name: team.name,
    description: team.description,
    visibility: team.visibility,
    avatar: team.avatar || ''
  });

  const [settings, setSettings] = useState(team.settings);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasChanges, setHasChanges] = useState(false);

  const visibilityOptions = [
    {
      value: 'private' as const,
      label: 'Privado',
      description: 'Solo los miembros invitados pueden ver y unirse al equipo',
      icon: Lock,
      color: 'text-red-600 bg-red-50 border-red-200'
    },
    {
      value: 'team' as const,
      label: 'Equipo',
      description: 'Los miembros del equipo pueden ver y unirse',
      icon: Users,
      color: 'text-blue-600 bg-blue-50 border-blue-200'
    },
    {
      value: 'organization' as const,
      label: 'Organización',
      description: 'Todos en la organización pueden ver y unirse',
      icon: Building,
      color: 'text-purple-600 bg-purple-50 border-purple-200'
    },
    {
      value: 'public' as const,
      label: 'Público',
      description: 'Cualquiera en internet puede ver y unirse al equipo',
      icon: Globe,
      color: 'text-green-600 bg-green-50 border-green-200'
    }
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
  };

  const handleSettingsChange = (field: string, value: boolean) => {
    setSettings(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'El nombre del equipo es obligatorio';
    } else if (formData.name.length < 3) {
      newErrors.name = 'El nombre debe tener al menos 3 caracteres';
    } else if (formData.name.length > 50) {
      newErrors.name = 'El nombre no puede tener más de 50 caracteres';
    }

    if (formData.description.length > 200) {
      newErrors.description = 'La descripción no puede tener más de 200 caracteres';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validateForm()) return;

    const updatedTeam: Team = {
      ...team,
      name: formData.name.trim(),
      description: formData.description.trim(),
      visibility: formData.visibility,
      avatar: formData.avatar,
      settings,
      updatedAt: new Date()
    };

    onUpdateTeam(updatedTeam);
    setHasChanges(false);
  };

  const handleAvatarUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        handleInputChange('avatar', e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="space-y-8">
      {/* General Settings */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Información General</h3>
        
        <div className="space-y-6">
          {/* Avatar */}
          <div className="flex items-center space-x-4">
            <div className="relative">
              {formData.avatar ? (
                <img
                  src={formData.avatar}
                  alt="Avatar del equipo"
                  className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                />
              ) : (
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center">
                  <Users className="w-8 h-8 text-white" />
                </div>
              )}
              <label className="absolute -bottom-1 -right-1 bg-blue-600 text-white p-1.5 rounded-full cursor-pointer hover:bg-blue-700 transition-colors">
                <Camera className="w-3 h-3" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarUpload}
                  className="hidden"
                />
              </label>
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Avatar del Equipo</h4>
              <p className="text-sm text-gray-500">Sube una imagen para representar tu equipo</p>
            </div>
          </div>

          {/* Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nombre del Equipo *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Nombre del equipo"
              maxLength={50}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              {formData.name.length}/50 caracteres
            </p>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Descripción
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${
                errors.description ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Describe el propósito y objetivos de tu equipo..."
              rows={3}
              maxLength={200}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              {formData.description.length}/200 caracteres
            </p>
          </div>
        </div>
      </div>

      {/* Visibility Settings */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Visibilidad del Equipo</h3>
        
        <div className="space-y-3">
          {visibilityOptions.map(option => {
            const Icon = option.icon;
            const isSelected = formData.visibility === option.value;
            
            return (
              <label
                key={option.value}
                className={`flex items-start space-x-3 p-4 border rounded-lg cursor-pointer transition-colors ${
                  isSelected 
                    ? option.color
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <input
                  type="radio"
                  name="visibility"
                  value={option.value}
                  checked={isSelected}
                  onChange={(e) => handleInputChange('visibility', e.target.value)}
                  className="sr-only"
                />
                <div className={`p-2 rounded-lg ${isSelected ? 'bg-white' : option.color}`}>
                  <Icon className="w-4 h-4" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">{option.label}</span>
                    {isSelected && <Check className="w-4 h-4 text-blue-600" />}
                  </div>
                  <p className="text-sm text-gray-600">{option.description}</p>
                </div>
              </label>
            );
          })}
        </div>
      </div>

      {/* Team Permissions */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Permisos del Equipo</h3>
        
        <div className="space-y-4">
          <label className="flex items-start space-x-3">
            <input
              type="checkbox"
              checked={settings.allowMembersToCreateBoards}
              onChange={(e) => handleSettingsChange('allowMembersToCreateBoards', e.target.checked)}
              className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <div>
              <span className="font-medium text-gray-900">Permitir a los miembros crear tableros</span>
              <p className="text-sm text-gray-500">Los miembros pueden crear nuevos tableros en este equipo</p>
            </div>
          </label>

          <label className="flex items-start space-x-3">
            <input
              type="checkbox"
              checked={settings.allowMembersToInvite}
              onChange={(e) => handleSettingsChange('allowMembersToInvite', e.target.checked)}
              className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <div>
              <span className="font-medium text-gray-900">Permitir a los miembros invitar personas</span>
              <p className="text-sm text-gray-500">Los miembros pueden enviar invitaciones al equipo</p>
            </div>
          </label>

          <label className="flex items-start space-x-3">
            <input
              type="checkbox"
              checked={settings.requireApprovalForJoining}
              onChange={(e) => handleSettingsChange('requireApprovalForJoining', e.target.checked)}
              className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <div>
              <span className="font-medium text-gray-900">Requerir aprobación para unirse</span>
              <p className="text-sm text-gray-500">Las solicitudes de unión deben ser aprobadas por un administrador</p>
            </div>
          </label>
        </div>
      </div>

      {/* Save Changes */}
      {hasChanges && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-blue-700">Tienes cambios sin guardar</span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => {
                  setFormData({
                    name: team.name,
                    description: team.description,
                    visibility: team.visibility,
                    avatar: team.avatar || ''
                  });
                  setSettings(team.settings);
                  setHasChanges(false);
                }}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
              >
                Descartar
              </button>
              <button
                onClick={handleSave}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Save className="w-4 h-4" />
                <span>Guardar Cambios</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Danger Zone */}
      <div className="bg-white border border-red-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-red-600 mb-4 flex items-center space-x-2">
          <AlertTriangle className="w-5 h-5" />
          <span>Zona de Peligro</span>
        </h3>
        
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Eliminar Equipo</h4>
            <p className="text-sm text-gray-600 mb-4">
              Una vez eliminado, no podrás recuperar este equipo ni sus datos. Todos los tableros del equipo también serán eliminados.
            </p>
            
            {!showDeleteConfirm ? (
              <button
                onClick={() => setShowDeleteConfirm(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                <Trash2 className="w-4 h-4" />
                <span>Eliminar Equipo</span>
              </button>
            ) : (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-sm text-red-700 mb-4">
                  ¿Estás seguro de que quieres eliminar este equipo? Esta acción no se puede deshacer.
                </p>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={async () => {
                      try {
                        await onDeleteTeam(team.id);
                        // The modal will be closed by the parent component
                      } catch (error) {
                        console.error('Error deleting team:', error);
                        alert('Error al eliminar el equipo. Por favor, inténtalo de nuevo.');
                      }
                    }}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Sí, eliminar equipo
                  </button>
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancelar
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
