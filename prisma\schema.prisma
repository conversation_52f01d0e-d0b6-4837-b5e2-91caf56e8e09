// Prisma schema for Trello Clone
// Migrated from localStorage to database

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// User model
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  name      String
  email     String   @unique
  phone     String?
  password  String
  role      UserRole @default(MEMBER)
  avatar    String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // User permissions and preferences
  permissions UserPermissions?
  preferences UserPreferences?

  // Relationships
  boardMemberships BoardMember[]
  teamMemberships  TeamMember[]
  comments         Comment[]
  notifications    Notification[]
  activities       Activity[]
  cardAssignments  CardAssignment[]
  createdBoards    Board[]        @relation("BoardCreator")
  invitedBy        User?          @relation("UserInvitations", fields: [invitedById], references: [id])
  invitedById      String?
  invitedUsers     User[]         @relation("UserInvitations")

  @@map("users")
}

// User permissions model
model UserPermissions {
  id                String  @id @default(cuid())
  userId            String  @unique
  canCreateBoards   Boolean @default(true)
  canEditCards      Boolean @default(true)
  canDeleteCards    Boolean @default(false)
  canManageUsers    Boolean @default(false)
  canViewReports    Boolean @default(true)
  canManageSettings Boolean @default(false)
  canInviteUsers    Boolean @default(false)
  canArchiveCards   Boolean @default(true)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_permissions")
}

// User preferences model
model UserPreferences {
  id            String  @id @default(cuid())
  userId        String  @unique
  sidebarOpen   Boolean @default(false)
  theme         String  @default("light")
  language      String  @default("es")
  lastBoardId   String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  user      User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  lastBoard Board? @relation("UserLastBoard", fields: [lastBoardId], references: [id])

  @@map("user_preferences")
}

// Team model
model Team {
  id          String         @id @default(cuid())
  name        String
  description String?
  avatar      String?
  visibility  TeamVisibility @default(TEAM)
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Team settings
  settings TeamSettings?

  // Relationships
  members TeamMember[]
  boards  Board[]

  @@map("teams")
}

// Team settings model
model TeamSettings {
  id                        String            @id @default(cuid())
  teamId                    String            @unique
  allowMembersToCreateBoards Boolean          @default(false)
  allowMembersToInvite      Boolean           @default(false)
  defaultBoardVisibility    BoardVisibility   @default(TEAM)
  requireApprovalForJoining Boolean           @default(true)

  team Team @relation(fields: [teamId], references: [id], onDelete: Cascade)

  @@map("team_settings")
}

// Team member model
model TeamMember {
  id        String   @id @default(cuid())
  teamId    String
  userId    String
  role      TeamRole @default(NORMAL)
  joinedAt  DateTime @default(now())
  invitedBy String?

  team Team @relation(fields: [teamId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([teamId, userId])
  @@map("team_members")
}

// Board model
model Board {
  id          String          @id @default(cuid())
  title       String
  description String?
  background  String          @default("bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500")
  isPublic    Boolean         @default(false)
  isFavorite  Boolean         @default(false)
  visibility  BoardVisibility @default(PRIVATE)
  teamId      String?
  createdById String
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // Relationships
  team         Team?         @relation(fields: [teamId], references: [id])
  createdBy    User          @relation("BoardCreator", fields: [createdById], references: [id])
  members      BoardMember[]
  lists        List[]
  labels       Label[]
  activities   Activity[]
  userLastBoards UserPreferences[] @relation("UserLastBoard")

  @@map("boards")
}

// Board member model
model BoardMember {
  id        String    @id @default(cuid())
  boardId   String
  userId    String
  role      BoardRole @default(MEMBER)
  joinedAt  DateTime  @default(now())
  invitedBy String?

  board Board @relation(fields: [boardId], references: [id], onDelete: Cascade)
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([boardId, userId])
  @@map("board_members")
}

// List model
model List {
  id       String  @id @default(cuid())
  title    String
  position Int
  archived Boolean @default(false)
  boardId  String

  board Board @relation(fields: [boardId], references: [id], onDelete: Cascade)
  cards Card[]

  @@map("lists")
}

// Label model
model Label {
  id      String @id @default(cuid())
  name    String
  color   String
  boardId String

  board Board     @relation(fields: [boardId], references: [id], onDelete: Cascade)
  cards CardLabel[]

  @@map("labels")
}

// Card model
model Card {
  id          String   @id @default(cuid())
  title       String
  description String?
  position    Int
  archived    Boolean  @default(false)
  cover       String?
  dueDate     DateTime?
  listId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  list List @relation(fields: [listId], references: [id], onDelete: Cascade)

  // Relationships
  labels       CardLabel[]
  assignedMembers CardAssignment[]
  checklist    ChecklistItem[]
  comments     Comment[]
  attachments  Attachment[]

  @@map("cards")
}

// Card-Label relationship (many-to-many)
model CardLabel {
  id      String @id @default(cuid())
  cardId  String
  labelId String

  card  Card  @relation(fields: [cardId], references: [id], onDelete: Cascade)
  label Label @relation(fields: [labelId], references: [id], onDelete: Cascade)

  @@unique([cardId, labelId])
  @@map("card_labels")
}

// Card assignment model (many-to-many between cards and users)
model CardAssignment {
  id         String   @id @default(cuid())
  cardId     String
  userId     String
  assignedAt DateTime @default(now())
  assignedBy String?

  card Card @relation(fields: [cardId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([cardId, userId])
  @@map("card_assignments")
}

// Checklist item model
model ChecklistItem {
  id        String  @id @default(cuid())
  text      String
  completed Boolean @default(false)
  position  Int     @default(0)
  cardId    String

  card Card @relation(fields: [cardId], references: [id], onDelete: Cascade)

  @@map("checklist_items")
}

// Comment model
model Comment {
  id        String   @id @default(cuid())
  text      String
  cardId    String
  authorId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  card   Card @relation(fields: [cardId], references: [id], onDelete: Cascade)
  author User @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@map("comments")
}

// Attachment model
model Attachment {
  id     String          @id @default(cuid())
  name   String
  url    String
  type   AttachmentType
  size   Int?
  cardId String

  card Card @relation(fields: [cardId], references: [id], onDelete: Cascade)

  @@map("attachments")
}

// Activity model
model Activity {
  id          String       @id @default(cuid())
  type        ActivityType
  description String
  boardId     String
  cardId      String?
  userId      String
  createdAt   DateTime     @default(now())

  board Board @relation(fields: [boardId], references: [id], onDelete: Cascade)
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("activities")
}

// Notification model
model Notification {
  id        String           @id @default(cuid())
  type      NotificationType
  title     String
  message   String
  read      Boolean          @default(false)
  userId    String
  boardId   String?
  cardId    String?
  createdAt DateTime         @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// Enums
enum UserRole {
  ADMIN
  MEMBER
  OBSERVER
}

enum TeamRole {
  ADMIN
  NORMAL
  OBSERVER
}

enum BoardRole {
  ADMIN
  MEMBER
  OBSERVER
}

enum TeamVisibility {
  PRIVATE
  TEAM
  ORGANIZATION
}

enum BoardVisibility {
  PRIVATE
  TEAM
  ORGANIZATION
  PUBLIC
}

enum AttachmentType {
  FILE
  LINK
}

enum ActivityType {
  CARD_CREATED
  CARD_MOVED
  CARD_UPDATED
  MEMBER_ADDED
  COMMENT_ADDED
}

enum NotificationType {
  MENTION
  ASSIGNMENT
  COMMENT
  DUE_DATE
}
