import React, { useState } from 'react';
import { Label } from '../../types';
import { X, Plus, Check } from 'lucide-react';

interface LabelSelectorProps {
  availableLabels: Label[];
  selectedLabels: Label[];
  onToggleLabel: (label: Label) => void;
  onCreateLabel: (name: string, color: string) => void;
  onClose: () => void;
}

const LABEL_COLORS = [
  '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16',
  '#22c55e', '#10b981', '#14b8a6', '#06b6d4', '#0ea5e9',
  '#3b82f6', '#6366f1', '#8b5cf6', '#a855f7', '#d946ef',
  '#ec4899', '#f43f5e', '#6b7280', '#374151', '#1f2937'
];

export function LabelSelector({ 
  availableLabels, 
  selectedLabels, 
  onToggleLabel, 
  onCreateLabel, 
  onClose 
}: LabelSelectorProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [newLabelName, setNewLabelName] = useState('');
  const [selectedColor, setSelectedColor] = useState(LABEL_COLORS[0]);

  const handleCreateLabel = () => {
    if (newLabelName.trim()) {
      onCreateLabel(newLabelName.trim(), selectedColor);
      setNewLabelName('');
      setIsCreating(false);
    }
  };

  const isLabelSelected = (label: Label) => {
    return selectedLabels.some(selected => selected.id === label.id);
  };

  return (
    <div
      className="absolute top-full mt-2 left-0 w-full max-w-sm bg-white rounded-lg shadow-lg border z-50 p-4 max-h-96 overflow-y-auto"
      onClick={(e) => e.stopPropagation()}
      style={{
        maxHeight: 'calc(100vh - 200px)',
        minWidth: '280px'
      }}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-gray-900">Etiquetas</h3>
        <button onClick={onClose} className="p-1 hover:bg-gray-100 rounded">
          <X className="w-4 h-4" />
        </button>
      </div>

      {/* Search/Filter */}
      <div className="mb-4">
        <input
          type="text"
          placeholder="Buscar etiquetas..."
          className="w-full p-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Available Labels */}
      <div className="space-y-2 mb-4 max-h-48 overflow-y-auto">
        {availableLabels.map((label) => (
          <div
            key={label.id}
            onClick={() => onToggleLabel(label)}
            className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg cursor-pointer"
          >
            <div
              className="w-8 h-4 rounded"
              style={{ backgroundColor: label.color }}
            />
            <span className="flex-1 text-sm">{label.name}</span>
            {isLabelSelected(label) && (
              <Check className="w-4 h-4 text-green-600" />
            )}
          </div>
        ))}
      </div>

      {/* Create New Label */}
      {isCreating ? (
        <div className="border-t pt-4">
          <div className="space-y-3">
            <input
              type="text"
              value={newLabelName}
              onChange={(e) => setNewLabelName(e.target.value)}
              placeholder="Nombre de la etiqueta"
              className="w-full p-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              autoFocus
            />
            
            {/* Color Picker */}
            <div className="grid grid-cols-5 gap-2">
              {LABEL_COLORS.map((color) => (
                <button
                  key={color}
                  onClick={() => setSelectedColor(color)}
                  className={`w-8 h-6 rounded ${
                    selectedColor === color ? 'ring-2 ring-gray-400' : ''
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>

            <div className="flex space-x-2">
              <button
                onClick={handleCreateLabel}
                className="flex-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
              >
                Crear
              </button>
              <button
                onClick={() => {
                  setIsCreating(false);
                  setNewLabelName('');
                }}
                className="flex-1 px-3 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 text-sm"
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      ) : (
        <button
          onClick={() => setIsCreating(true)}
          className="w-full flex items-center justify-center space-x-2 p-2 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 text-gray-600 hover:text-gray-700"
        >
          <Plus className="w-4 h-4" />
          <span className="text-sm">Crear nueva etiqueta</span>
        </button>
      )}
    </div>
  );
}
