import React, { useState, useEffect } from 'react';
import { User } from '../../types';
import { X, Search, Check, UserPlus, Shield, Users } from 'lucide-react';
import { useDatabaseApp } from '../../context/DatabaseAppContext';
import { useUserPermissions } from '../../hooks/useUserPermissions';
import { useAuth } from '../../context/AuthContext';

interface TeamMemberSelectorProps {
  availableMembers?: User[];
  assignedMembers: User[];
  onToggleMember: (member: User) => void;
  onClose: () => void;
  teamId?: string;
}

export function TeamMemberSelector({ 
  availableMembers = [],
  assignedMembers, 
  onToggleMember, 
  onClose,
  teamId
}: TeamMemberSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const { getAllUsersForAdmin } = useDatabaseApp();
  const { isAdmin } = useUserPermissions();
  const { user } = useAuth();

  // Load all users for admin when component mounts
  useEffect(() => {
    const loadAllUsers = async () => {
      // Prevent multiple simultaneous calls
      if (loading) return;

      // Only load if we're admin and don't have users yet
      if (isAdmin() && allUsers.length === 0) {
        setLoading(true);
        try {
          const users = await getAllUsersForAdmin();
          setAllUsers(users);
          console.log('✅ Loaded', users.length, 'users for team management');
        } catch (error) {
          console.error('Error loading all users for admin:', error);
        } finally {
          setLoading(false);
        }
      } else if (!isAdmin() && availableMembers.length > 0 && allUsers.length === 0) {
        // For non-admin users, use the provided availableMembers
        setAllUsers(availableMembers);
      }
    };

    loadAllUsers();
  }, [isAdmin, allUsers.length, availableMembers.length, loading]);

  // Use all users for admin, filtered users for others
  const usersToShow = allUsers.length > 0 ? allUsers : availableMembers;

  const filteredMembers = usersToShow.filter(member => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return (
      member.name.toLowerCase().includes(query) ||
      member.email.toLowerCase().includes(query) ||
      member.username?.toLowerCase().includes(query)
    );
  });

  const isAssigned = (member: User) => {
    return assignedMembers.some(assigned => assigned.id === member.id);
  };

  if (loading) {
    return (
      <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
        <div className="p-4 text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-sm text-gray-500 mt-2">Cargando usuarios...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="font-semibold text-gray-900">Miembros del Equipo</h3>
            {isAdmin() && (
              <p className="text-xs text-blue-600 mt-1 flex items-center">
                <Shield className="w-3 h-3 mr-1" />
                Acceso completo (Admin) - {usersToShow.length} usuarios
              </p>
            )}
            {!isAdmin() && (
              <p className="text-xs text-gray-500 mt-1 flex items-center">
                <Users className="w-3 h-3 mr-1" />
                Usuarios disponibles - {usersToShow.length}
              </p>
            )}
          </div>
          <button onClick={onClose} className="p-1 hover:bg-gray-100 rounded">
            <X className="w-4 h-4" />
          </button>
        </div>

        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Buscar miembros..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Members list */}
        <div className="max-h-64 overflow-y-auto">
          {filteredMembers.length > 0 ? (
            filteredMembers.map(member => (
              <div
                key={member.id}
                onClick={() => onToggleMember(member)}
                className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                    {member.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {member.name}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {member.email}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={`text-xs px-2 py-0.5 rounded-full ${
                        member.role === 'admin' 
                          ? 'bg-red-100 text-red-800' 
                          : member.role === 'member'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {member.role === 'admin' ? 'Admin' : member.role === 'member' ? 'Miembro' : 'Observador'}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  {isAssigned(member) ? (
                    <div className="w-5 h-5 bg-blue-600 rounded flex items-center justify-center">
                      <Check className="w-3 h-3 text-white" />
                    </div>
                  ) : (
                    <div className="w-5 h-5 border-2 border-gray-300 rounded"></div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              {searchQuery ? (
                <>
                  <Search className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm">No se encontraron miembros que coincidan con "{searchQuery}"</p>
                </>
              ) : usersToShow.length === 0 ? (
                <>
                  <Shield className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm font-medium">No hay usuarios disponibles</p>
                  <p className="text-xs text-gray-400 mt-1">
                    Los usuarios disponibles están limitados por las restricciones del equipo
                  </p>
                </>
              ) : (
                <>
                  <UserPlus className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm">No se encontraron miembros</p>
                </>
              )}
            </div>
          )}
        </div>

        {/* Footer info */}
        <div className="mt-4 pt-3 border-t border-gray-100">
          <p className="text-xs text-gray-500">
            {assignedMembers.length} miembro{assignedMembers.length !== 1 ? 's' : ''} seleccionado{assignedMembers.length !== 1 ? 's' : ''}
          </p>
          {isAdmin() && (
            <p className="text-xs text-blue-600 mt-1">
              Como administrador, puedes agregar cualquier usuario del sistema
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
