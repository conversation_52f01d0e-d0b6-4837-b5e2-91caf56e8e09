import React, { useState } from 'react';
import { X, Plus, Check } from 'lucide-react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { Card as CardType } from '../types';

interface QuickAddCardModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function QuickAddCardModal({ isOpen, onClose }: QuickAddCardModalProps) {
  const { getCurrentBoard } = useDatabaseApp();
  const currentBoard = getCurrentBoard();
  const [selectedListId, setSelectedListId] = useState<string>('');
  const [cardTitle, setCardTitle] = useState('');
  const [cardDescription, setCardDescription] = useState('');

  // Set default list when modal opens
  React.useEffect(() => {
    if (isOpen && currentBoard && currentBoard.lists.length > 0 && !selectedListId) {
      setSelectedListId(currentBoard.lists[0].id);
    }
  }, [isOpen, currentBoard, selectedListId]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentBoard || !selectedListId || !cardTitle.trim()) return;

    const newCard: CardType = {
      id: Date.now().toString(),
      title: cardTitle.trim(),
      description: cardDescription.trim(),
      labels: [],
      assignedMembers: [],
      checklist: [],
      comments: [],
      attachments: [],
      position: currentBoard.lists.find(l => l.id === selectedListId)?.cards.length || 0,
      archived: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    dispatch({
      type: 'ADD_CARD',
      payload: { boardId: currentBoard.id, listId: selectedListId, card: newCard }
    });

    // Reset form
    setCardTitle('');
    setCardDescription('');
    onClose();
  };

  const handleCancel = () => {
    setCardTitle('');
    setCardDescription('');
    onClose();
  };

  if (!isOpen || !currentBoard) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">Agregar Nueva Tarjeta</h2>
            <button
              onClick={handleCancel}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* List Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Lista de destino
            </label>
            <select
              value={selectedListId}
              onChange={(e) => setSelectedListId(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              {currentBoard.lists.map((list) => (
                <option key={list.id} value={list.id}>
                  {list.title} ({list.cards.length} tarjetas)
                </option>
              ))}
            </select>
          </div>

          {/* Card Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Título de la tarjeta *
            </label>
            <input
              type="text"
              value={cardTitle}
              onChange={(e) => setCardTitle(e.target.value)}
              placeholder="Ej: Revisar propuesta de diseño"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              autoFocus
              required
              maxLength={200}
            />
            <div className="mt-1 text-xs text-gray-500 text-right">
              {cardTitle.length}/200
            </div>
          </div>

          {/* Card Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Descripción (opcional)
            </label>
            <textarea
              value={cardDescription}
              onChange={(e) => setCardDescription(e.target.value)}
              placeholder="Agrega más detalles sobre esta tarjeta..."
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={3}
              maxLength={500}
            />
            <div className="mt-1 text-xs text-gray-500 text-right">
              {cardDescription.length}/500
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-3 pt-4">
            <button
              type="submit"
              disabled={!cardTitle.trim() || !selectedListId}
              className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              <Check className="w-4 h-4" />
              <span>Crear Tarjeta</span>
            </button>
            
            <button
              type="button"
              onClick={handleCancel}
              className="flex items-center space-x-2 bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
            >
              <X className="w-4 h-4" />
              <span>Cancelar</span>
            </button>
          </div>
        </form>

        {/* Footer Info */}
        <div className="px-6 py-3 bg-gray-50 border-t">
          <p className="text-xs text-gray-600">
            💡 Tip: También puedes agregar tarjetas directamente desde cada lista usando el botón "Agregar una tarjeta"
          </p>
        </div>
      </div>
    </div>
  );
}
