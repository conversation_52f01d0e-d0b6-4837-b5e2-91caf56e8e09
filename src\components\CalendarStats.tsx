import React, { useMemo } from 'react';
import { Card as CardType } from '../types';
import { 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  Calendar,
  TrendingUp,
  Users,
  Target
} from 'lucide-react';

interface CalendarStatsProps {
  allCards: (CardType & { boardTitle: string; listTitle: string })[];
  currentUserId?: string;
}

export function CalendarStats({ allCards, currentUserId }: CalendarStatsProps) {
  const stats = useMemo(() => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
    const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

    const overdue = allCards.filter(card => new Date(card.dueDate!) < today);
    const dueToday = allCards.filter(card => {
      const dueDate = new Date(card.dueDate!);
      return dueDate >= today && dueDate < tomorrow;
    });
    const dueTomorrow = allCards.filter(card => {
      const dueDate = new Date(card.dueDate!);
      return dueDate >= tomorrow && dueDate < new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000);
    });
    const dueThisWeek = allCards.filter(card => {
      const dueDate = new Date(card.dueDate!);
      return dueDate >= today && dueDate <= weekFromNow;
    });

    const myTasks = currentUserId 
      ? allCards.filter(card => card.assignedMembers.some(member => member.id === currentUserId))
      : [];

    const myOverdue = currentUserId
      ? overdue.filter(card => card.assignedMembers.some(member => member.id === currentUserId))
      : [];

    // Productivity metrics
    const totalTasks = allCards.length;
    const completedTasks = 0; // We don't track completed tasks in this implementation
    const productivityRate = totalTasks > 0 ? ((totalTasks - overdue.length) / totalTasks) * 100 : 100;

    // Board distribution
    const boardStats = allCards.reduce((acc, card) => {
      acc[card.boardTitle] = (acc[card.boardTitle] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topBoards = Object.entries(boardStats)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3);

    return {
      overdue: overdue.length,
      dueToday: dueToday.length,
      dueTomorrow: dueTomorrow.length,
      dueThisWeek: dueThisWeek.length,
      myTasks: myTasks.length,
      myOverdue: myOverdue.length,
      totalTasks,
      productivityRate,
      topBoards
    };
  }, [allCards, currentUserId]);

  const StatCard = ({ 
    icon: Icon, 
    title, 
    value, 
    color, 
    subtitle 
  }: { 
    icon: any; 
    title: string; 
    value: number; 
    color: string; 
    subtitle?: string;
  }) => (
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Resumen General</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            icon={AlertTriangle}
            title="Tareas Vencidas"
            value={stats.overdue}
            color="bg-red-500"
            subtitle="Requieren atención inmediata"
          />
          <StatCard
            icon={Clock}
            title="Vencen Hoy"
            value={stats.dueToday}
            color="bg-orange-500"
            subtitle="Para completar hoy"
          />
          <StatCard
            icon={Calendar}
            title="Vencen Mañana"
            value={stats.dueTomorrow}
            color="bg-yellow-500"
            subtitle="Planificar para mañana"
          />
          <StatCard
            icon={CheckCircle}
            title="Esta Semana"
            value={stats.dueThisWeek}
            color="bg-blue-500"
            subtitle="Próximos 7 días"
          />
        </div>
      </div>

      {/* Personal Stats */}
      {currentUserId && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Mis Tareas</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <StatCard
              icon={Users}
              title="Asignadas a Mí"
              value={stats.myTasks}
              color="bg-purple-500"
              subtitle="Total de mis tareas"
            />
            <StatCard
              icon={AlertTriangle}
              title="Mis Vencidas"
              value={stats.myOverdue}
              color="bg-red-500"
              subtitle="Necesitan atención"
            />
            <StatCard
              icon={Target}
              title="Productividad"
              value={Math.round(stats.productivityRate)}
              color="bg-green-500"
              subtitle="% de tareas a tiempo"
            />
          </div>
        </div>
      )}

      {/* Board Distribution */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Distribución por Tablero</h3>
        <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
          {stats.topBoards.length > 0 ? (
            <div className="space-y-3">
              {stats.topBoards.map(([boardName, count], index) => (
                <div key={boardName} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      index === 0 ? 'bg-blue-500' : 
                      index === 1 ? 'bg-green-500' : 'bg-yellow-500'
                    }`} />
                    <span className="text-sm font-medium text-gray-900">{boardName}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">{count} tareas</span>
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          index === 0 ? 'bg-blue-500' : 
                          index === 1 ? 'bg-green-500' : 'bg-yellow-500'
                        }`}
                        style={{ width: `${(count / stats.totalTasks) * 100}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="w-12 h-12 mx-auto mb-3 text-gray-300" />
              <p>No hay tareas con fechas de vencimiento</p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Acciones Rápidas</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-4 text-white">
            <div className="flex items-center space-x-3">
              <TrendingUp className="w-8 h-8" />
              <div>
                <h4 className="font-semibold">Mejorar Productividad</h4>
                <p className="text-sm opacity-90">
                  {stats.overdue > 0 
                    ? `Tienes ${stats.overdue} tareas vencidas. ¡Ponte al día!`
                    : '¡Excelente! No tienes tareas vencidas.'
                  }
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-500 to-teal-600 rounded-lg p-4 text-white">
            <div className="flex items-center space-x-3">
              <Target className="w-8 h-8" />
              <div>
                <h4 className="font-semibold">Planificación</h4>
                <p className="text-sm opacity-90">
                  {stats.dueThisWeek > 0
                    ? `${stats.dueThisWeek} tareas programadas para esta semana`
                    : 'Semana libre de tareas programadas'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
