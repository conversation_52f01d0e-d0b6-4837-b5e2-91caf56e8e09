import React from 'react';
import { Loader2, HardDrive } from 'lucide-react';

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  type?: 'loading' | 'cleanup';
}

export function LoadingOverlay({ 
  isVisible, 
  message = 'Cargando...', 
  type = 'loading' 
}: LoadingOverlayProps) {
  if (!isVisible) return null;

  const Icon = type === 'cleanup' ? HardDrive : Loader2;
  const iconClass = type === 'cleanup' ? 'w-8 h-8 text-blue-600' : 'w-8 h-8 text-blue-600 animate-spin';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-sm w-full mx-4">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <Icon className={iconClass} />
          </div>
          
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {type === 'cleanup' ? 'Optimizando Almacenamiento' : 'Cargando'}
          </h3>
          
          <p className="text-gray-600 text-sm mb-4">
            {message}
          </p>
          
          {type === 'cleanup' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-blue-800 text-xs">
                Estamos optimizando el almacenamiento para mejorar el rendimiento. 
                Esto solo tomará unos segundos.
              </p>
            </div>
          )}
          
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
