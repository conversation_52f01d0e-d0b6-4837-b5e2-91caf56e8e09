import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedUsers() {
  try {
    console.log('🌱 Creando usuarios por defecto...');
    
    // Check if users already exist
    const existingUsers = await prisma.user.count();
    if (existingUsers > 0) {
      console.log('✅ Los usuarios ya existen en la base de datos');
      return;
    }

    const defaultUsers = [
      {
        username: 'admin',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'admin123',
        role: 'ADMIN',
      },
      {
        username: 'ana.garcia',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+1234567891',
        password: 'ana123',
        role: 'MEMBER',
      },
      {
        username: 'carlos.lopez',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+1234567892',
        password: 'carlos123',
        role: 'OBSERVER',
      }
    ];

    for (const userData of defaultUsers) {
      await prisma.user.create({
        data: {
          ...userData,
          avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.name)}&background=3b82f6&color=fff&size=128`,
          permissions: {
            create: {
              canCreateBoards: userData.role === 'ADMIN',
              canEditCards: true,
              canDeleteCards: userData.role === 'ADMIN',
              canManageUsers: userData.role === 'ADMIN',
              canViewReports: true,
              canManageSettings: userData.role === 'ADMIN',
              canInviteUsers: userData.role === 'ADMIN',
              canArchiveCards: true,
            }
          }
        }
      });
      console.log(`✅ Usuario creado: ${userData.name} (${userData.email})`);
    }

    console.log('🎉 Usuarios por defecto creados exitosamente');
    
  } catch (error) {
    console.error('❌ Error creando usuarios:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

seedUsers();
