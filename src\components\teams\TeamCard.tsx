import React from 'react';
import { Team } from '../../types';
import { formatDate } from '../../utils/dateUtils';
import {
  Users,
  Crown,
  Eye,
  Lock,
  Globe,
  Building,
  Calendar,
  MoreHorizontal,
  Settings
} from 'lucide-react';

interface TeamCardProps {
  team: Team;
  currentUserId: string;
  onClick: () => void;
}

export function TeamCard({ team, currentUserId, onClick }: TeamCardProps) {
  const userMembership = team.members.find(member => member.userId === currentUserId);
  const userRole = userMembership?.role || 'observer';
  const isAdmin = userRole === 'admin';

  const getVisibilityIcon = () => {
    switch (team.visibility) {
      case 'private':
        return <Lock className="w-4 h-4 text-red-500" />;
      case 'team':
        return <Users className="w-4 h-4 text-blue-500" />;
      case 'organization':
        return <Building className="w-4 h-4 text-purple-500" />;
      case 'public':
        return <Globe className="w-4 h-4 text-green-500" />;
    }
  };

  const getVisibilityLabel = () => {
    switch (team.visibility) {
      case 'private':
        return 'Privado';
      case 'team':
        return 'Equipo';
      case 'organization':
        return 'Organización';
      case 'public':
        return 'Público';
    }
  };

  const getRoleLabel = () => {
    switch (userRole) {
      case 'admin':
        return 'Administrador';
      case 'normal':
        return 'Miembro';
      case 'observer':
        return 'Observador';
      default:
        return 'Sin rol';
    }
  };

  const getRoleColor = () => {
    switch (userRole) {
      case 'admin':
        return 'text-yellow-600 bg-yellow-100';
      case 'normal':
        return 'text-blue-600 bg-blue-100';
      case 'observer':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div
      onClick={onClick}
      className="bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200 cursor-pointer group"
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            {team.avatar ? (
              <img
                src={team.avatar}
                alt={team.name}
                className="w-12 h-12 rounded-full object-cover border-2 border-gray-200"
              />
            ) : (
              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center">
                <Users className="w-6 h-6 text-white" />
              </div>
            )}
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 truncate group-hover:text-blue-600 transition-colors">
                {team.name}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                {getVisibilityIcon()}
                <span className="text-sm text-gray-500">{getVisibilityLabel()}</span>
              </div>
            </div>
          </div>

          {isAdmin && (
            <div className="opacity-0 group-hover:opacity-100 transition-opacity">
              <button className="p-1 hover:bg-gray-100 rounded">
                <Settings className="w-4 h-4 text-gray-500" />
              </button>
            </div>
          )}
        </div>

        {/* Role Badge */}
        <div className="mt-3">
          <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getRoleColor()}`}>
            {userRole === 'admin' && <Crown className="w-3 h-3" />}
            <span>{getRoleLabel()}</span>
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Description */}
        {team.description && (
          <p className="text-sm text-gray-600 mb-4 line-clamp-2">
            {team.description}
          </p>
        )}

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{team.members.length}</div>
            <div className="text-xs text-gray-500">Miembros</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{team.boards.length}</div>
            <div className="text-xs text-gray-500">Tableros</div>
          </div>
        </div>

        {/* Members Preview */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="flex -space-x-2">
              {team.members.slice(0, 4).map((member, index) => (
                <div
                  key={member.userId}
                  className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 border-2 border-white flex items-center justify-center text-white text-xs font-medium"
                  title={`Miembro ${index + 1}`}
                >
                  {index + 1}
                </div>
              ))}
              {team.members.length > 4 && (
                <div className="w-8 h-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-gray-600 text-xs font-medium">
                  +{team.members.length - 4}
                </div>
              )}
            </div>
            {team.members.length > 0 && (
              <span className="text-xs text-gray-500">
                {team.members.length} miembro{team.members.length !== 1 ? 's' : ''}
              </span>
            )}
          </div>

          <div className="text-xs text-gray-500 flex items-center space-x-1">
            <Calendar className="w-3 h-3" />
            <span>
              {formatDate(team.createdAt, {
                month: 'short',
                day: 'numeric'
              })}
            </span>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-100 rounded-b-lg">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-4">
            {team.settings.allowMembersToCreateBoards && (
              <span className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Crear tableros</span>
              </span>
            )}
            {team.settings.allowMembersToInvite && (
              <span className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Invitar miembros</span>
              </span>
            )}
          </div>
          
          <span className="text-gray-400">
            Actualizado {new Date(team.updatedAt).toLocaleDateString('es-ES')}
          </span>
        </div>
      </div>
    </div>
  );
}
