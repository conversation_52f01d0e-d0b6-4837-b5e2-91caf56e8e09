import React, { useState } from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { useAuth } from '../context/AuthContext';
import { User, BoardMember, Team } from '../types';
import { X, Plus, UserPlus, UserMinus, Crown, Shield, Eye, Search, Mail, Users } from 'lucide-react';

interface BoardMemberManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

type MemberRole = 'admin' | 'member' | 'observer';

const roleConfig = {
  admin: {
    label: 'Administrador',
    icon: Crown,
    color: 'text-yellow-600 bg-yellow-100',
    description: 'Puede editar el tablero y gestionar miembros'
  },
  member: {
    label: 'Miembro',
    icon: Shield,
    color: 'text-blue-600 bg-blue-100',
    description: 'Puede editar tarjetas y listas'
  },
  observer: {
    label: 'Observador',
    icon: Eye,
    color: 'text-gray-600 bg-gray-100',
    description: 'Solo puede ver el tablero'
  }
};

export function BoardMemberManager({ isOpen, onClose }: BoardMemberManagerProps) {
  const { state, getCurrentBoard, updateBoard, addMemberToBoard, removeMemberFromBoard, updateMemberRole } = useDatabaseApp();
  const { user: currentUser } = useAuth();
  const currentBoard = getCurrentBoard();
  const { users } = state;
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUserId, setSelectedUserId] = useState('');
  const [selectedRole, setSelectedRole] = useState<MemberRole>('member');
  const [showAddForm, setShowAddForm] = useState(false);
  const [memberToRemove, setMemberToRemove] = useState<string | null>(null);
  const [showAddTeamForm, setShowAddTeamForm] = useState(false);
  const [selectedTeamId, setSelectedTeamId] = useState('');

  if (!isOpen || !currentBoard) return null;

  const boardMembers = currentBoard.members || [];

  // Debug log for board members and their roles
  console.log('🔍 BoardMemberManager - Board members:', {
    boardId: currentBoard.id,
    boardTitle: currentBoard.title,
    membersCount: boardMembers.length,
    members: boardMembers.map(m => ({
      id: m.id,
      name: m.name,
      role: m.role,
      roleType: typeof m.role
    }))
  });

  const isCurrentUserAdmin = boardMembers.find(m => m.id === currentUser?.id)?.role === 'admin';

  // Filter current board members for search
  const filteredMembers = boardMembers.filter(member => {
    if (!member || typeof member !== 'object') return false;
    const memberName = member.name || '';
    const memberEmail = member.email || '';
    const query = searchQuery.toLowerCase();
    return memberName.toLowerCase().includes(query) || memberEmail.toLowerCase().includes(query);
  });

  // Get users that are not already members of this board
  const availableUsers = users.filter(user =>
    !boardMembers.some(member => member.id === user.id)
  );

  // Get teams that have members not already in this board
  const availableTeams = state.teams.filter(team => {
    const teamMemberIds = team.members.map(m => m.userId);
    const teamUsers = users.filter(user => teamMemberIds.includes(user.id));
    return teamUsers.some(user => !boardMembers.some(member => member.id === user.id));
  });

  // Filter available users for the add form
  const filteredAvailableUsers = availableUsers.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleAddMember = async () => {
    if (!selectedUserId) return;

    try {
      await addMemberToBoard(currentBoard.id, selectedUserId, selectedRole);
      setSelectedUserId('');
      setShowAddForm(false);
    } catch (error) {
      console.error('Error adding member to board:', error);
      alert('Error al agregar miembro al tablero. Por favor, inténtalo de nuevo.');
    }
  };

  const handleRemoveMember = (memberId: string) => {
    if (memberId === currentUser?.id) return; // No se puede remover a sí mismo
    setMemberToRemove(memberId);
  };

  const confirmRemoveMember = async () => {
    if (!memberToRemove) return;

    try {
      await removeMemberFromBoard(currentBoard.id, memberToRemove);
      setMemberToRemove(null);
    } catch (error) {
      console.error('Error removing member from board:', error);
      alert('Error al remover miembro del tablero. Por favor, inténtalo de nuevo.');
    }
  };

  const cancelRemoveMember = () => {
    setMemberToRemove(null);
  };

  const handleChangeRole = async (memberId: string, newRole: MemberRole) => {
    if (memberId === currentUser?.id && newRole !== 'admin') return; // El admin actual no puede cambiar su propio rol

    try {
      await updateMemberRole(currentBoard.id, memberId, newRole);
    } catch (error) {
      console.error('Error updating member role:', error);
      alert('Error al actualizar el rol del miembro. Por favor, inténtalo de nuevo.');
    }
  };

  const handleAddTeam = async () => {
    if (!selectedTeamId) return;

    const selectedTeam = state.teams.find(team => team.id === selectedTeamId);
    if (!selectedTeam) return;

    // Get team members that are not already board members
    const teamMemberIds = selectedTeam.members.map(m => m.userId);
    const teamUsers = users.filter(user => teamMemberIds.includes(user.id));
    const newMembers = teamUsers.filter(user =>
      !boardMembers.some(member => member.id === user.id)
    );

    if (newMembers.length === 0) {
      alert('Todos los miembros de este equipo ya están en el tablero');
      return;
    }

    try {
      // Add each team member to the board
      for (const user of newMembers) {
        await addMemberToBoard(currentBoard.id, user.id, 'member');
      }

      setSelectedTeamId('');
      setShowAddTeamForm(false);

      // Show success message
      alert(`Se agregaron ${newMembers.length} miembros del equipo "${selectedTeam.name}" al tablero`);
    } catch (error) {
      console.error('Error adding team members to board:', error);
      alert('Error al agregar miembros del equipo al tablero. Por favor, inténtalo de nuevo.');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Miembros del Tablero</h2>
            <div className="flex items-center space-x-4 mt-2">
              <p className="text-sm text-gray-600">{boardMembers.length} miembros activos</p>
              {availableUsers.length > 0 && (
                <p className="text-sm text-green-600">
                  {availableUsers.length} usuarios disponibles para agregar
                </p>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[calc(90vh-120px)] overflow-y-auto">
          {/* Search and Add */}
          <div className="space-y-4 mb-6">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar miembros..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Add Member Button */}
            {isCurrentUserAdmin && (
              <div className="space-y-3">
                <button
                  onClick={() => setShowAddForm(!showAddForm)}
                  className={`flex items-center space-x-2 w-full p-3 border-2 border-dashed rounded-lg transition-colors ${
                    availableUsers.length > 0
                      ? 'border-gray-300 hover:border-blue-400 hover:bg-blue-50 text-gray-600'
                      : 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed'
                  }`}
                  disabled={availableUsers.length === 0}
                >
                  <UserPlus className="w-5 h-5" />
                  <span>
                    {availableUsers.length > 0
                      ? `Agregar miembro individual (${availableUsers.length} disponibles)`
                      : 'No hay usuarios disponibles para agregar'
                    }
                  </span>
                </button>

                {/* Add Team Button */}
                <button
                  onClick={() => setShowAddTeamForm(!showAddTeamForm)}
                  className={`flex items-center space-x-2 w-full p-3 border-2 border-dashed rounded-lg transition-colors ${
                    availableTeams.length > 0
                      ? 'border-green-300 hover:border-green-400 hover:bg-green-50 text-green-600'
                      : 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed'
                  }`}
                  disabled={availableTeams.length === 0}
                >
                  <Users className="w-5 h-5" />
                  <span>
                    {availableTeams.length > 0
                      ? `Agregar equipo completo (${availableTeams.length} equipos disponibles)`
                      : 'No hay equipos disponibles para agregar'
                    }
                  </span>
                </button>
              </div>
            )}



            {/* Add Member Form */}
            {showAddForm && (
              <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Usuarios disponibles ({availableUsers.length})
                  </label>

                  {availableUsers.length > 0 ? (
                    /* Available users list */
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {availableUsers.map((user) => (
                        <div
                          key={user.id}
                          onClick={() => setSelectedUserId(user.id)}
                          className={`flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                            selectedUserId === user.id
                              ? 'bg-blue-50 border-blue-300 ring-2 ring-blue-500'
                              : 'bg-white border-gray-200 hover:bg-gray-50'
                          }`}
                        >
                          {user.avatar ? (
                            <img
                              src={user.avatar}
                              alt={user.name}
                              className="w-10 h-10 rounded-full object-cover"
                            />
                          ) : (
                            <div className="w-10 h-10 rounded-full bg-gray-500 flex items-center justify-center text-white text-sm font-bold">
                              {user.name.charAt(0).toUpperCase()}
                            </div>
                          )}
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">{user.name}</h4>
                            <p className="text-sm text-gray-600">{user.email}</p>
                            {user.username && (
                              <p className="text-xs text-gray-500">@{user.username}</p>
                            )}
                            <p className="text-xs text-gray-500">
                              Rol del sistema: {user.role === 'admin' ? 'Administrador' : user.role === 'member' ? 'Miembro' : 'Observador'}
                            </p>
                          </div>
                          {selectedUserId === user.id && (
                            <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                              <div className="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 bg-white rounded-lg border-2 border-dashed border-gray-200">
                      <UserPlus className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-sm text-gray-500 mb-2">
                        Todos los usuarios registrados ya son miembros de este tablero
                      </p>
                      <p className="text-xs text-gray-400">
                        Puedes crear nuevos usuarios en la sección de Gestión de Usuarios
                      </p>
                    </div>
                  )}
                </div>



                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Rol en el tablero
                  </label>
                  <select
                    value={selectedRole}
                    onChange={(e) => setSelectedRole(e.target.value as MemberRole)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {Object.entries(roleConfig).map(([role, config]) => (
                      <option key={role} value={role}>
                        {config.label} - {config.description}
                      </option>
                    ))}
                  </select>
                </div>

                {availableUsers.length > 0 && (
                  <div className="flex space-x-2">
                    <button
                      onClick={handleAddMember}
                      disabled={!selectedUserId}
                      className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                    >
                      Agregar Miembro
                    </button>
                    <button
                      onClick={() => {
                        setShowAddForm(false);
                        setSelectedUserId('');
                      }}
                      className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      Cancelar
                    </button>
                  </div>
                )}

                {availableUsers.length === 0 && (
                  <div className="flex justify-end">
                    <button
                      onClick={() => {
                        setShowAddForm(false);
                        setSelectedUserId('');
                      }}
                      className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      Cerrar
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Add Team Form */}
            {showAddTeamForm && (
              <div className="bg-green-50 p-4 rounded-lg space-y-3 border border-green-200">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Equipos disponibles ({availableTeams.length})
                  </label>

                  {availableTeams.length > 0 ? (
                    <div className="space-y-2">
                      <select
                        value={selectedTeamId}
                        onChange={(e) => setSelectedTeamId(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      >
                        <option value="">Seleccionar equipo...</option>
                        {availableTeams.map(team => {
                          const teamMemberIds = team.members.map(m => m.userId);
                          const teamUsers = users.filter(user => teamMemberIds.includes(user.id));
                          const newMembers = teamUsers.filter(user =>
                            !boardMembers.some(member => member.id === user.id)
                          );

                          return (
                            <option key={team.id} value={team.id}>
                              {team.name} ({newMembers.length} miembros nuevos)
                            </option>
                          );
                        })}
                      </select>

                      {selectedTeamId && (
                        <div className="mt-3 p-3 bg-white rounded-lg border">
                          {(() => {
                            const selectedTeam = state.teams.find(t => t.id === selectedTeamId);
                            if (!selectedTeam) return null;

                            const teamMemberIds = selectedTeam.members.map(m => m.userId);
                            const teamUsers = users.filter(user => teamMemberIds.includes(user.id));
                            const newMembers = teamUsers.filter(user =>
                              !boardMembers.some(member => member.id === user.id)
                            );

                            return (
                              <div>
                                <h4 className="text-sm font-medium text-gray-900 mb-2">
                                  Miembros que se agregarán:
                                </h4>
                                <div className="space-y-1">
                                  {newMembers.map(user => (
                                    <div key={user.id} className="flex items-center space-x-2 text-sm">
                                      <img
                                        src={user.avatar}
                                        alt={user.name}
                                        className="w-6 h-6 rounded-full object-cover"
                                      />
                                      <span className="text-gray-700">{user.name}</span>
                                      <span className="text-gray-500">({user.email})</span>
                                    </div>
                                  ))}
                                </div>
                                {newMembers.length === 0 && (
                                  <p className="text-sm text-gray-500">
                                    Todos los miembros de este equipo ya están en el tablero
                                  </p>
                                )}
                              </div>
                            );
                          })()}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8 bg-white rounded-lg border-2 border-dashed border-gray-200">
                      <Users className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-sm text-gray-500 mb-2">
                        No hay equipos disponibles para agregar
                      </p>
                      <p className="text-xs text-gray-400">
                        Todos los equipos ya tienen sus miembros en este tablero
                      </p>
                    </div>
                  )}
                </div>

                {availableTeams.length > 0 && (
                  <div className="flex space-x-2">
                    <button
                      onClick={handleAddTeam}
                      disabled={!selectedTeamId}
                      className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                    >
                      Agregar Equipo
                    </button>
                    <button
                      onClick={() => {
                        setShowAddTeamForm(false);
                        setSelectedTeamId('');
                      }}
                      className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      Cancelar
                    </button>
                  </div>
                )}

                {availableTeams.length === 0 && (
                  <div className="flex justify-end">
                    <button
                      onClick={() => {
                        setShowAddTeamForm(false);
                        setSelectedTeamId('');
                      }}
                      className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      Cerrar
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Members List */}
          <div className="space-y-3">
            {filteredMembers.map((member) => {
              // Normalize role to lowercase and provide fallback
              const normalizedRole = member.role?.toLowerCase() as MemberRole;
              const roleInfo = roleConfig[normalizedRole] || roleConfig.member; // fallback to member
              const RoleIcon = roleInfo.icon;
              const isCurrentUser = member.id === currentUser?.id;

              // Debug log for role issues
              if (!roleConfig[normalizedRole]) {
                console.warn('⚠️ Unknown role for member:', {
                  memberId: member.id,
                  memberName: member.name,
                  originalRole: member.role,
                  normalizedRole,
                  availableRoles: Object.keys(roleConfig)
                });
              }

              return (
                <div
                  key={member.id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <img
                      src={member.avatar}
                      alt={member.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium text-gray-900">
                          {member.name}
                          {isCurrentUser && (
                            <span className="text-sm text-gray-500 ml-1">(Tú)</span>
                          )}
                        </h3>
                      </div>
                      <p className="text-sm text-gray-600">{member.email}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    {/* Role Badge */}
                    <div className={`flex items-center space-x-1 px-2 py-1 rounded-full ${roleInfo.color}`}>
                      <RoleIcon className="w-3 h-3" />
                      <span className="text-xs font-medium">{roleInfo.label}</span>
                    </div>

                    {/* Actions */}
                    {isCurrentUserAdmin && !isCurrentUser && (
                      <div className="flex items-center space-x-2">
                        {/* Change Role */}
                        <div className="flex flex-col">
                          <label className="text-xs text-gray-500 mb-1">Rol</label>
                          <select
                            value={normalizedRole}
                            onChange={(e) => handleChangeRole(member.id, e.target.value as MemberRole)}
                            className="text-xs border border-gray-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-transparent min-w-24"
                          >
                            {Object.entries(roleConfig).map(([role, config]) => (
                              <option key={role} value={role}>
                                {config.label}
                              </option>
                            ))}
                          </select>
                        </div>

                        {/* Remove Member */}
                        <div className="flex flex-col">
                          <label className="text-xs text-gray-500 mb-1">Acción</label>
                          <button
                            onClick={() => handleRemoveMember(member.id)}
                            className="p-2 text-red-500 hover:bg-red-100 rounded-lg transition-colors border border-red-200 hover:border-red-300"
                            title="Remover miembro del tablero"
                          >
                            <UserMinus className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    )}

                    {/* Current User Badge */}
                    {isCurrentUser && (
                      <div className="flex items-center space-x-2">
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                          Eres tú
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}

            {filteredMembers.length === 0 && searchQuery && (
              <div className="text-center py-8 text-gray-500">
                <Search className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>No se encontraron miembros con "{searchQuery}"</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Confirmation Modal for Removing Member */}
      {memberToRemove && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-10">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <UserMinus className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Remover Miembro</h3>
                <p className="text-sm text-gray-600">Esta acción no se puede deshacer</p>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-gray-700">
                ¿Estás seguro de que quieres remover a{' '}
                <span className="font-semibold">
                  {boardMembers.find(m => m.id === memberToRemove)?.name}
                </span>{' '}
                del tablero?
              </p>
              <p className="text-sm text-gray-500 mt-2">
                El usuario perderá acceso a este tablero y todas sus tarjetas asignadas.
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={confirmRemoveMember}
                className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors"
              >
                Sí, Remover
              </button>
              <button
                onClick={cancelRemoveMember}
                className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
