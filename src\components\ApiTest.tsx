import React, { useState } from 'react';
import ApiService from '../services/ApiService';
import { getApiEndpoint } from '../config/api';

export function ApiTest() {
  const [testResult, setTestResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testApiConnection = async () => {
    setLoading(true);
    setTestResult('Testing API connection...');
    
    try {
      const response = await fetch(getApiEndpoint('/test'));
      const data = await response.json();
      setTestResult(`✅ API Test Success: ${JSON.stringify(data, null, 2)}`);
    } catch (error) {
      setTestResult(`❌ API Test Failed: ${error}`);
    }
    setLoading(false);
  };

  const testLogin = async () => {
    setLoading(true);
    setTestResult('Testing login...');
    
    try {
      const result = await ApiService.login({
        email: '<EMAIL>',
        password: 'admin123'
      });
      setTestResult(`✅ Login Test: ${JSON.stringify(result, null, 2)}`);
    } catch (error) {
      setTestResult(`❌ Login Test Failed: ${error}`);
    }
    setLoading(false);
  };

  const testHealthCheck = async () => {
    setLoading(true);
    setTestResult('Testing health check...');
    
    try {
      const result = await ApiService.healthCheck();
      setTestResult(`✅ Health Check: ${JSON.stringify(result, null, 2)}`);
    } catch (error) {
      setTestResult(`❌ Health Check Failed: ${error}`);
    }
    setLoading(false);
  };

  return (
    <div className="fixed top-4 right-4 bg-white p-4 border rounded-lg shadow-lg z-50 max-w-md">
      <h3 className="font-bold mb-2">API Test Panel</h3>
      
      <div className="space-y-2 mb-4">
        <button
          onClick={testHealthCheck}
          disabled={loading}
          className="w-full px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 disabled:bg-gray-400"
        >
          Test Health Check
        </button>
        
        <button
          onClick={testApiConnection}
          disabled={loading}
          className="w-full px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 disabled:bg-gray-400"
        >
          Test API Connection
        </button>
        
        <button
          onClick={testLogin}
          disabled={loading}
          className="w-full px-3 py-1 bg-purple-500 text-white rounded text-sm hover:bg-purple-600 disabled:bg-gray-400"
        >
          Test Login
        </button>
      </div>
      
      <div className="bg-gray-100 p-2 rounded text-xs max-h-40 overflow-y-auto">
        <pre>{testResult || 'Click a button to test...'}</pre>
      </div>
    </div>
  );
}
