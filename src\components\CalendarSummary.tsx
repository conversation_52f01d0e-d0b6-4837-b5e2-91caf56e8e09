import React, { useMemo } from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { 
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Target,
  Users,
  BarChart3
} from 'lucide-react';

interface CalendarSummaryProps {
  onOpenCalendar?: () => void;
}

export function CalendarSummary({ onOpenCalendar }: CalendarSummaryProps) {
  const { state } = useDatabaseApp();

  const summary = useMemo(() => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
    const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

    let totalTasks = 0;
    let overdueTasks = 0;
    let todayTasks = 0;
    let tomorrowTasks = 0;
    let weekTasks = 0;
    let myTasks = 0;
    let myOverdueTasks = 0;

    state.boards.forEach(board => {
      board.lists.forEach(list => {
        list.cards.forEach(card => {
          if (card.dueDate && !card.archived) {
            totalTasks++;
            const dueDate = new Date(card.dueDate);
            const isAssignedToMe = card.assignedMembers.some(member => member.id === state.currentUser?.id);

            if (isAssignedToMe) {
              myTasks++;
            }

            if (dueDate < today) {
              overdueTasks++;
              if (isAssignedToMe) myOverdueTasks++;
            } else if (dueDate >= today && dueDate < tomorrow) {
              todayTasks++;
            } else if (dueDate >= tomorrow && dueDate < new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000)) {
              tomorrowTasks++;
            } else if (dueDate >= today && dueDate <= weekFromNow) {
              weekTasks++;
            }
          }
        });
      });
    });

    const productivityScore = totalTasks > 0 ? Math.round(((totalTasks - overdueTasks) / totalTasks) * 100) : 100;

    return {
      totalTasks,
      overdueTasks,
      todayTasks,
      tomorrowTasks,
      weekTasks,
      myTasks,
      myOverdueTasks,
      productivityScore
    };
  }, [state.boards, state.currentUser]);

  const StatCard = ({ 
    icon: Icon, 
    title, 
    value, 
    subtitle, 
    color = 'text-blue-600',
    bgColor = 'bg-blue-50'
  }: { 
    icon: any; 
    title: string; 
    value: number | string; 
    subtitle?: string;
    color?: string;
    bgColor?: string;
  }) => (
    <div className={`${bgColor} rounded-lg p-4`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
        </div>
        <Icon className={`w-8 h-8 ${color}`} />
      </div>
    </div>
  );

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Calendar className="w-6 h-6 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Resumen del Calendario</h3>
        </div>
        <button
          onClick={onOpenCalendar}
          className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
        >
          Abrir calendario
        </button>
      </div>

      {summary.totalTasks === 0 ? (
        <div className="text-center py-8">
          <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-3" />
          <p className="text-gray-600 mb-2">No hay tareas programadas</p>
          <p className="text-sm text-gray-500">Agrega fechas de vencimiento a tus tareas para verlas aquí</p>
        </div>
      ) : (
        <>
          {/* Main Stats Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <StatCard
              icon={AlertTriangle}
              title="Vencidas"
              value={summary.overdueTasks}
              subtitle="Requieren atención"
              color="text-red-600"
              bgColor="bg-red-50"
            />
            <StatCard
              icon={Clock}
              title="Hoy"
              value={summary.todayTasks}
              subtitle="Para completar"
              color="text-orange-600"
              bgColor="bg-orange-50"
            />
            <StatCard
              icon={Calendar}
              title="Mañana"
              value={summary.tomorrowTasks}
              subtitle="Próximo día"
              color="text-yellow-600"
              bgColor="bg-yellow-50"
            />
            <StatCard
              icon={CheckCircle}
              title="Esta Semana"
              value={summary.weekTasks}
              subtitle="Próximos 7 días"
              color="text-blue-600"
              bgColor="bg-blue-50"
            />
          </div>

          {/* Personal Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <StatCard
              icon={Users}
              title="Mis Tareas"
              value={summary.myTasks}
              subtitle="Asignadas a mí"
              color="text-purple-600"
              bgColor="bg-purple-50"
            />
            <StatCard
              icon={Target}
              title="Productividad"
              value={`${summary.productivityScore}%`}
              subtitle="Tareas a tiempo"
              color="text-green-600"
              bgColor="bg-green-50"
            />
            <StatCard
              icon={TrendingUp}
              title="Total"
              value={summary.totalTasks}
              subtitle="Tareas programadas"
              color="text-indigo-600"
              bgColor="bg-indigo-50"
            />
          </div>

          {/* Quick Actions */}
          <div className="border-t border-gray-200 pt-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                {summary.overdueTasks > 0 ? (
                  <span className="text-red-600 font-medium">
                    ⚠️ Tienes {summary.overdueTasks} tarea{summary.overdueTasks > 1 ? 's' : ''} vencida{summary.overdueTasks > 1 ? 's' : ''}
                  </span>
                ) : summary.todayTasks > 0 ? (
                  <span className="text-orange-600 font-medium">
                    📅 {summary.todayTasks} tarea{summary.todayTasks > 1 ? 's' : ''} para hoy
                  </span>
                ) : (
                  <span className="text-green-600 font-medium">
                    ✅ ¡Todo bajo control!
                  </span>
                )}
              </div>
              
              <button
                onClick={onOpenCalendar}
                className="flex items-center space-x-2 text-sm bg-blue-600 text-white px-3 py-1.5 rounded-lg hover:bg-blue-700 transition-colors"
              >
                <BarChart3 className="w-4 h-4" />
                <span>Ver detalles</span>
              </button>
            </div>
          </div>

          {/* Progress Bar */}
          {summary.totalTasks > 0 && (
            <div className="mt-4">
              <div className="flex items-center justify-between text-xs text-gray-600 mb-2">
                <span>Progreso general</span>
                <span>{summary.productivityScore}% a tiempo</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    summary.productivityScore >= 80 ? 'bg-green-500' :
                    summary.productivityScore >= 60 ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`}
                  style={{ width: `${summary.productivityScore}%` }}
                />
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
