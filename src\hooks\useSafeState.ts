import { useState, useCallback, useRef, useEffect } from 'react';

/**
 * Hook que proporciona un setState seguro que previene actualizaciones
 * de estado en componentes desmontados
 */
export function useSafeState<T>(initialState: T | (() => T)) {
  const [state, setState] = useState(initialState);
  const mountedRef = useRef(true);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const safeSetState = useCallback((newState: T | ((prevState: T) => T)) => {
    if (mountedRef.current) {
      setState(newState);
    }
  }, []);

  return [state, safeSetState] as const;
}

/**
 * Hook para manejar operaciones asíncronas de forma segura
 */
export function useSafeAsync() {
  const mountedRef = useRef(true);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const safeAsync = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    onSuccess?: (result: T) => void,
    onError?: (error: Error) => void
  ) => {
    try {
      const result = await asyncFn();
      if (mountedRef.current && onSuccess) {
        onSuccess(result);
      }
      return result;
    } catch (error) {
      if (mountedRef.current && onError) {
        onError(error as Error);
      }
      throw error;
    }
  }, []);

  return { safeAsync, isMounted: () => mountedRef.current };
}

/**
 * Hook para manejar loading states de forma segura
 */
export function useSafeLoading(initialLoading = false) {
  const [loading, setLoading] = useSafeState(initialLoading);
  const [error, setError] = useSafeState<string | null>(null);
  const { safeAsync } = useSafeAsync();

  const executeAsync = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    onSuccess?: (result: T) => void
  ): Promise<T | null> => {
    setLoading(true);
    setError(null);

    try {
      const result = await safeAsync(asyncFn);
      if (onSuccess) {
        onSuccess(result);
      }
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('🚨 useSafeLoading error:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, [safeAsync, setLoading, setError]);

  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  return {
    loading,
    error,
    executeAsync,
    clearError
  };
}

/**
 * Hook para validar props y prevenir errores de renderizado
 */
export function useSafeProps<T extends Record<string, any>>(
  props: T,
  requiredProps: (keyof T)[]
): { isValid: boolean; missingProps: string[] } {
  const missingProps = requiredProps.filter(prop => 
    props[prop] === undefined || props[prop] === null
  );

  return {
    isValid: missingProps.length === 0,
    missingProps: missingProps as string[]
  };
}

/**
 * Hook para manejar clicks de forma segura con debounce
 */
export function useSafeClick(
  onClick: () => void | Promise<void>,
  debounceMs = 300
) {
  const [isProcessing, setIsProcessing] = useSafeState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const safeClick = useCallback(async () => {
    if (isProcessing) return;

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    setIsProcessing(true);

    try {
      await onClick();
    } catch (error) {
      console.error('🚨 useSafeClick error:', error);
    } finally {
      // Debounce the reset
      timeoutRef.current = setTimeout(() => {
        setIsProcessing(false);
      }, debounceMs);
    }
  }, [onClick, isProcessing, debounceMs, setIsProcessing]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return { safeClick, isProcessing };
}
