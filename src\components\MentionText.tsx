import React from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { BoardMember } from '../types';
import { getAvailableUsersForMentions, extractMentionsFromText, validateMentions } from '../utils/mentionUtils';

interface MentionTextProps {
  text: string;
  className?: string;
  onMentionClick?: (member: BoardMember) => void;
}

export function MentionText({ text, className = "", onMentionClick }: MentionTextProps) {
  const { getCurrentBoard, state } = useDatabaseApp();
  const currentBoard = getCurrentBoard();

  const availableUsers = getAvailableUsersForMentions(currentBoard, state.teams);

  const renderTextWithMentions = (text: string) => {
    if (!text) return null;

    // Regex para detectar menciones @username
    const mentionRegex = /@(\w+)/g;
    const parts = text.split(mentionRegex);
    
    return parts.map((part, index) => {
      // Los índices impares son las menciones (grupos capturados)
      if (index % 2 === 1) {
        const member = availableUsers.find(m =>
          m.name.toLowerCase() === part.toLowerCase()
        );
        
        if (member) {
          return (
            <span
              key={index}
              onClick={() => onMentionClick?.(member)}
              className={`inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium cursor-pointer hover:bg-blue-200 transition-colors ${
                onMentionClick ? 'cursor-pointer' : ''
              }`}
              title={`${member.name} (${member.email}) - ${member.role}`}
            >
              <img
                src={member.avatar}
                alt={member.name}
                className="w-4 h-4 rounded-full mr-1"
              />
              @{member.name}
            </span>
          );
        } else {
          // Mención no válida (usuario no encontrado)
          return (
            <span
              key={index}
              className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-sm"
              title="Usuario no encontrado o no es miembro del equipo"
            >
              @{part}
            </span>
          );
        }
      }
      
      // Texto normal - preservar saltos de línea
      return part.split('\n').map((line, lineIndex, lines) => (
        <React.Fragment key={`${index}-${lineIndex}`}>
          {line}
          {lineIndex < lines.length - 1 && <br />}
        </React.Fragment>
      ));
    });
  };

  return (
    <div className={className}>
      {renderTextWithMentions(text)}
    </div>
  );
}

// Componente para mostrar una lista de miembros mencionados
interface MentionSummaryProps {
  text: string;
  className?: string;
  showTitle?: boolean;
}

export function MentionSummary({ text, className = "", showTitle = true }: MentionSummaryProps) {
  const { getCurrentBoard, state } = useDatabaseApp();
  const currentBoard = getCurrentBoard();

  const availableUsers = getAvailableUsersForMentions(currentBoard, state.teams);

  const mentionedMembers = extractMentionsFromText(text, availableUsers);

  if (mentionedMembers.length === 0) {
    return null;
  }

  return (
    <div className={`${className}`}>
      {showTitle && (
        <div className="text-sm text-gray-600 mb-2">
          Miembros mencionados:
        </div>
      )}
      <div className="flex flex-wrap gap-2">
        {mentionedMembers.map((member) => (
          <div
            key={member.id}
            className="flex items-center space-x-2 bg-blue-50 text-blue-800 px-3 py-1 rounded-full text-sm"
            title={`${member.name} (${member.email}) - ${member.role}`}
          >
            <img
              src={member.avatar}
              alt={member.name}
              className="w-5 h-5 rounded-full"
            />
            <span className="font-medium">{member.name}</span>
            <span className="text-xs">
              {member.role === 'admin' ? '👑' : member.role === 'member' ? '🛡️' : '👁️'}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}

// Hook para trabajar con menciones
export function useMentionUtils() {
  const { getCurrentBoard, state } = useDatabaseApp();
  const currentBoard = getCurrentBoard();

  const availableUsers = getAvailableUsersForMentions(currentBoard, state.teams);

  const extractMentions = (text: string): BoardMember[] => {
    return extractMentionsFromText(text, availableUsers);
  };

  const hasMentions = (text: string): boolean => {
    return extractMentions(text).length > 0;
  };

  const getMentionCount = (text: string): number => {
    return extractMentions(text).length;
  };

  const validateMentionsInText = (text: string): { valid: BoardMember[], invalid: string[] } => {
    return validateMentions(text, availableUsers);
  };

  return {
    extractMentions,
    hasMentions,
    getMentionCount,
    validateMentions: validateMentionsInText,
    availableUsers
  };
}
