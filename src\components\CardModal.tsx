import React, { useState, useEffect } from 'react';
import { Card as CardType, ChecklistItem, Comment, Label, User as UserType, Attachment } from '../types';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { useAuth } from '../context/AuthContext';
import { useResponsive } from '../hooks/useResponsive';
import { ResponsiveModal } from './ResponsiveModal';
import { LabelSelector } from './modals/LabelSelector';
import { MemberSelector } from './modals/MemberSelector';
import { DatePicker } from './modals/DatePicker';
import { AttachmentUploader } from './modals/AttachmentUploader';
import { CoverSelector } from './CoverSelector';
import { MentionInput } from './MentionInput';
import { MentionText, MentionSummary } from './MentionText';
import { useNotifications } from '../hooks/useNotifications';
import { formatDate, formatDateTime } from '../utils/dateUtils';
import { getAvailableUsersForMentions, getAvailableUsersForCardAssignment } from '../utils/mentionUtils';
import ApiService from '../services/ApiService';
import {
  X,
  Calendar,
  User,
  Tag,
  MessageCircle,
  Paperclip,
  CheckSquare,
  Plus,
  Archive,
  Copy,
  Eye,
  Download,
  ExternalLink,
  Edit3,
  Trash2,
  Check,
  Save
} from 'lucide-react';

interface CardModalProps {
  card: CardType;
  onClose: () => void;
  onUpdate: (card: CardType) => void;
}

export function CardModal({ card, onClose, onUpdate }: CardModalProps) {
  const { state, dispatch, getCurrentBoard, loadBoards, createLabel } = useDatabaseApp();
  const { user: currentUser } = useAuth();
  const { isMobile, isTablet } = useResponsive();

  // Ensure card has required arrays initialized
  const normalizedCard = {
    ...card,
    checklist: card.checklist || [],
    comments: card.comments || [],
    labels: card.labels || [],
    assignedMembers: card.assignedMembers || [],
    attachments: card.attachments || []
  };

  // Simple notification functions using only AppContext
  const checkForMentions = (text: string, card: CardType, author: UserType, boardId: string) => {
    if (!state.currentBoard || !currentUser) return;

    const mentionRegex = /@(\w+)/g;
    // Use team-based filtering for mentions
    const availableUsers = getAvailableUsersForMentions(state.currentBoard, state.teams);
    let match;

    while ((match = mentionRegex.exec(text)) !== null) {
      const memberName = match[1];
      const mentionedMember = availableUsers.find(m =>
        m.name.toLowerCase() === memberName.toLowerCase()
      );

      if (mentionedMember && mentionedMember.id !== author.id && mentionedMember.id === currentUser?.id) {
        // Create a simple notification
        const notification = {
          id: Date.now().toString(),
          type: 'mention' as const,
          title: 'Te mencionaron',
          message: `${author.name} te mencionó en "${card.title}"`,
          read: false,
          createdAt: new Date(),
          boardId,
          cardId: card.id
        };

        dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
      }
    }
  };

  const checkForNewAssignments = (
    oldAssignedMembers: UserType[],
    newAssignedMembers: UserType[],
    card: CardType,
    assigner: UserType,
    boardId: string
  ) => {
    if (!currentUser) return;

    // Find newly assigned members
    const newlyAssigned = newAssignedMembers.filter(newMember =>
      !oldAssignedMembers.some(oldMember => oldMember.id === newMember.id)
    );

    // Create notifications for newly assigned members
    newlyAssigned.forEach(assignedUser => {
      if (assignedUser.id !== assigner.id && assignedUser.id === currentUser.id) {
        const notification = {
          id: Date.now().toString(),
          type: 'assignment' as const,
          title: 'Nueva asignación',
          message: `${assigner.name} te asignó la tarjeta "${card.title}"`,
          read: false,
          createdAt: new Date(),
          boardId,
          cardId: card.id
        };

        dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
      }
    });
  };
  const [editingTitle, setEditingTitle] = useState(false);
  const [title, setTitle] = useState(normalizedCard.title);
  const [description, setDescription] = useState(normalizedCard.description || '');
  const [newComment, setNewComment] = useState('');
  const [newChecklistItem, setNewChecklistItem] = useState('');
  const [editingChecklistItem, setEditingChecklistItem] = useState<string | null>(null);
  const [editingChecklistText, setEditingChecklistText] = useState('');
  const [descriptionSaved, setDescriptionSaved] = useState(false);
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editingCommentText, setEditingCommentText] = useState('');

  // Modal states
  const [showLabelSelector, setShowLabelSelector] = useState(false);
  const [showMemberSelector, setShowMemberSelector] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showAttachmentUploader, setShowAttachmentUploader] = useState(false);
  const [showCoverSelector, setShowCoverSelector] = useState(false);

  // Sync local states when card prop changes
  useEffect(() => {
    setTitle(normalizedCard.title);
    setDescription(normalizedCard.description || '');
  }, [normalizedCard.title, normalizedCard.description]);

  const handleTitleSave = () => {
    onUpdate({ ...normalizedCard, title, updatedAt: new Date() });
    setEditingTitle(false);
  };

  const handleDescriptionSave = () => {
    if (description !== normalizedCard.description) {
      const updatedCard = { ...normalizedCard, description, updatedAt: new Date() };

      console.log('💾 Saving card description:', {
        cardId: normalizedCard.id,
        oldDescription: normalizedCard.description,
        newDescription: description
      });

      // Check for mentions in the description and trigger notifications
      if (currentUser && state.currentBoard) {
        checkForMentions(description, updatedCard, currentUser, state.currentBoard.id);
      }

      onUpdate(updatedCard);

      // Show feedback visual
      setDescriptionSaved(true);
      setTimeout(() => setDescriptionSaved(false), 2000);
    }
  };

  const handleChecklistToggle = (itemId: string) => {
    if (!normalizedCard.checklist) return;
    const updatedChecklist = normalizedCard.checklist.map(item =>
      item.id === itemId ? { ...item, completed: !item.completed } : item
    );

    console.log('✅ Toggling checklist item:', { itemId, updatedChecklist });
    onUpdate({ ...normalizedCard, checklist: updatedChecklist, updatedAt: new Date() });
  };

  const handleAddChecklistItem = () => {
    if (!newChecklistItem.trim()) return;

    const newItem: ChecklistItem = {
      id: Date.now().toString(),
      text: newChecklistItem.trim(),
      completed: false
    };

    const updatedCard = {
      ...normalizedCard,
      checklist: [...normalizedCard.checklist, newItem],
      updatedAt: new Date()
    };

    console.log('➕ Adding checklist item:', { newItem, totalItems: updatedCard.checklist.length });

    // Check for mentions in the checklist item and trigger notifications
    if (currentUser && state.currentBoard) {
      checkForMentions(newChecklistItem.trim(), updatedCard, currentUser, state.currentBoard.id);
    }

    onUpdate(updatedCard);
    setNewChecklistItem('');
  };

  const handleEditChecklistItem = (itemId: string, newText: string) => {
    if (!newText.trim() || !normalizedCard.checklist) return;

    const updatedChecklist = normalizedCard.checklist.map(item =>
      item.id === itemId ? { ...item, text: newText.trim() } : item
    );

    const updatedCard = {
      ...normalizedCard,
      checklist: updatedChecklist,
      updatedAt: new Date()
    };

    // Check for mentions in the edited checklist item
    if (currentUser && state.currentBoard) {
      checkForMentions(newText.trim(), updatedCard, currentUser, state.currentBoard.id);
    }

    onUpdate(updatedCard);
    setEditingChecklistItem(null);
    setEditingChecklistText('');
  };

  const handleDeleteChecklistItem = (itemId: string) => {
    if (!normalizedCard.checklist) return;
    const updatedChecklist = normalizedCard.checklist.filter(item => item.id !== itemId);
    const updatedCard = {
      ...normalizedCard,
      checklist: updatedChecklist,
      updatedAt: new Date()
    };
    onUpdate(updatedCard);
  };

  const startEditingChecklistItem = (item: ChecklistItem) => {
    setEditingChecklistItem(item.id);
    setEditingChecklistText(item.text);
  };

  const cancelEditingChecklistItem = () => {
    setEditingChecklistItem(null);
    setEditingChecklistText('');
  };

  const handleAddComment = () => {
    if (!newComment.trim()) return;

    const comment: Comment = {
      id: Date.now().toString(),
      text: newComment.trim(),
      author: currentUser,
      createdAt: new Date(),
      mentions: []
    };

    const updatedCard = {
      ...normalizedCard,
      comments: [...normalizedCard.comments, comment],
      updatedAt: new Date()
    };

    console.log('💬 Adding comment:', { comment, totalComments: updatedCard.comments.length });

    // Check for mentions in the comment and trigger notifications
    if (currentUser && state.currentBoard) {
      checkForMentions(newComment.trim(), updatedCard, currentUser, state.currentBoard.id);
    }

    onUpdate(updatedCard);
    setNewComment('');
  };

  const handleEditComment = (commentId: string) => {
    const comment = normalizedCard.comments.find(c => c.id === commentId);
    if (comment) {
      setEditingCommentId(commentId);
      setEditingCommentText(comment.text);
    }
  };

  const handleSaveCommentEdit = () => {
    if (!editingCommentId || !editingCommentText.trim()) return;

    const updatedComments = normalizedCard.comments.map(comment => {
      if (comment.id === editingCommentId) {
        return {
          ...comment,
          text: editingCommentText.trim(),
          updatedAt: new Date()
        };
      }
      return comment;
    });

    const updatedCard = {
      ...normalizedCard,
      comments: updatedComments,
      updatedAt: new Date()
    };

    console.log('✏️ Editing comment:', { commentId: editingCommentId, newText: editingCommentText.trim() });

    // Check for mentions in the edited comment and trigger notifications
    if (currentUser && state.currentBoard) {
      checkForMentions(editingCommentText.trim(), updatedCard, currentUser, state.currentBoard.id);
    }

    onUpdate(updatedCard);
    setEditingCommentId(null);
    setEditingCommentText('');
  };

  const handleCancelCommentEdit = () => {
    setEditingCommentId(null);
    setEditingCommentText('');
  };

  const handleDeleteComment = (commentId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este comentario?')) return;

    const updatedComments = normalizedCard.comments.filter(comment => comment.id !== commentId);
    const updatedCard = {
      ...normalizedCard,
      comments: updatedComments,
      updatedAt: new Date()
    };

    console.log('🗑️ Deleting comment:', { commentId, remainingComments: updatedComments.length });

    onUpdate(updatedCard);
  };

  // Label management
  const handleToggleLabel = (label: Label) => {
    const cardLabels = card.labels || [];
    const isSelected = cardLabels.some(l => l.id === label.id);
    const updatedLabels = isSelected
      ? cardLabels.filter(l => l.id !== label.id)
      : [...cardLabels, label];

    onUpdate({ ...card, labels: updatedLabels, updatedAt: new Date() });
  };

  const handleCreateLabel = async (name: string, color: string) => {
    try {
      const currentBoard = getCurrentBoard();
      if (!currentBoard) return;

      const newLabel = await createLabel(currentBoard.id, { name, color });

      // Add to card labels
      onUpdate({
        ...card,
        labels: [...(card.labels || []), newLabel],
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error creating label:', error);
      alert('Error al crear la etiqueta. Por favor, inténtalo de nuevo.');
    }
  };

  // Member management
  const handleToggleMember = (member: UserType) => {
    const isAssigned = normalizedCard.assignedMembers.some(m => m.id === member.id);
    const oldAssignedMembers = [...normalizedCard.assignedMembers];
    const updatedMembers = isAssigned
      ? normalizedCard.assignedMembers.filter(m => m.id !== member.id)
      : [...normalizedCard.assignedMembers, member];

    const updatedCard = { ...normalizedCard, assignedMembers: updatedMembers, updatedAt: new Date() };

    // Check for new assignments and trigger notifications
    if (currentUser && state.currentBoard) {
      checkForNewAssignments(
        oldAssignedMembers,
        updatedMembers,
        updatedCard,
        currentUser,
        state.currentBoard.id
      );
    }

    onUpdate(updatedCard);
  };

  // Date management
  const handleDateChange = (date: Date | undefined) => {
    onUpdate({ ...card, dueDate: date, updatedAt: new Date() });
  };

  // Attachment management
  const handleAddAttachment = (attachmentData: Omit<Attachment, 'id'>) => {
    const newAttachment: Attachment = {
      id: Date.now().toString(),
      ...attachmentData
    };

    onUpdate({
      ...normalizedCard,
      attachments: [...normalizedCard.attachments, newAttachment],
      updatedAt: new Date()
    });
  };

  const handleRemoveAttachment = (attachmentId: string) => {
    if (!normalizedCard.attachments) return;
    const updatedAttachments = normalizedCard.attachments.filter(a => a.id !== attachmentId);
    onUpdate({ ...normalizedCard, attachments: updatedAttachments, updatedAt: new Date() });
  };

  const handleCopyCard = async () => {
    const currentBoard = getCurrentBoard();
    if (!currentBoard) return;

    // Find the list containing this card
    const currentList = currentBoard.lists.find((list: any) =>
      list.cards.some((c: any) => c.id === normalizedCard.id)
    );

    if (!currentList) return;

    try {
      await ApiService.copyCard(currentBoard.id, currentList.id, normalizedCard.id);

      // Reload board data to show the copied card
      if (loadBoards) {
        await loadBoards();
      }

      onClose();
    } catch (error) {
      console.error('Error copying card:', error);
    }
  };

  const handleSetCover = (coverUrl: string) => {
    const updatedCard = {
      ...card,
      cover: coverUrl,
      updatedAt: new Date()
    };
    onUpdate(updatedCard);
    setShowCoverSelector(false);
  };

  const handleRemoveCover = () => {
    const updatedCard = {
      ...card,
      cover: undefined,
      updatedAt: new Date()
    };
    onUpdate(updatedCard);
    setShowCoverSelector(false);
  };

  const handleArchiveCard = () => {
    if (confirm('¿Estás seguro de que quieres archivar esta tarjeta?')) {
      const updatedCard = {
        ...card,
        archived: true,
        updatedAt: new Date()
      };
      onUpdate(updatedCard);
      onClose();
    }
  };

  const completedTasks = normalizedCard.checklist.filter(item => item.completed).length;
  const totalTasks = normalizedCard.checklist.length;
  const progressPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  // Close modals when clicking outside
  const closeAllModals = () => {
    setShowLabelSelector(false);
    setShowMemberSelector(false);
    setShowDatePicker(false);
    setShowAttachmentUploader(false);
    setShowCoverSelector(false);
  };

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (showLabelSelector || showMemberSelector || showDatePicker || showAttachmentUploader) {
          closeAllModals();
        } else {
          onClose();
        }
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [showLabelSelector, showMemberSelector, showDatePicker, showAttachmentUploader, onClose]);

  return (
    <div
      className={`fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center ${isMobile ? 'p-0' : 'p-4'}`}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        className={`bg-white shadow-xl w-full ${
          isMobile
            ? 'h-full max-h-full rounded-none overflow-hidden'
            : 'rounded-lg max-w-7xl max-h-[95vh] overflow-visible'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        <div className={`${isMobile ? 'flex flex-col' : 'flex'} h-full max-h-[95vh]`}>
          {/* Main Content */}
          <div className="flex-1 overflow-y-auto min-h-0">
            {/* Header */}
            <div className={`${isMobile ? 'p-4' : 'p-6'} border-b`}>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {/* Labels */}
                  {normalizedCard.labels.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-3">
                      {normalizedCard.labels.map((label) => (
                        <span
                          key={label.id}
                          className="inline-block px-3 py-1 text-sm font-medium text-white rounded-full"
                          style={{ backgroundColor: label.color || '#gray' }}
                        >
                          {label.name || 'Etiqueta'}
                        </span>
                      ))}
                    </div>
                  )}

                  {/* Title */}
                  {editingTitle ? (
                    <div className="mb-3">
                      <input
                        type="text"
                        value={title}
                        onChange={(e) => setTitle(e.target.value)}
                        onBlur={handleTitleSave}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') handleTitleSave();
                          if (e.key === 'Escape') {
                            setTitle(card.title);
                            setEditingTitle(false);
                          }
                        }}
                        className="text-2xl font-bold text-gray-900 bg-transparent border-none outline-none w-full"
                        autoFocus
                      />
                    </div>
                  ) : (
                    <h1
                      className="text-2xl font-bold text-gray-900 mb-3 cursor-pointer hover:bg-gray-50 p-1 rounded"
                      onClick={() => setEditingTitle(true)}
                    >
                      {card.title}
                    </h1>
                  )}

                  {/* Meta Info */}
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span>en lista <strong>Por Hacer</strong></span>
                    <span>•</span>
                    <span>Creado el {formatDate(card.createdAt)}</span>
                    {card.dueDate && (
                      <>
                        <span>•</span>
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>Vence el {formatDate(card.dueDate)}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
                
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <X className="w-5 h-5 text-gray-500" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {/* Assigned Members */}
              {normalizedCard.assignedMembers.length > 0 && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <User className="w-4 h-4 mr-2" />
                    Miembros
                  </h3>
                  <div className="flex space-x-2">
                    {normalizedCard.assignedMembers.map((member) => (
                      <div key={member.id} className="flex items-center space-x-2 bg-gray-50 p-2 rounded-lg">
                        <img
                          src={member.avatar || '/default-avatar.png'}
                          alt={member.name || 'Usuario'}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                        <span className="text-sm font-medium">{member.name || 'Usuario'}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Description */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-900">Descripción</h3>
                  {description !== card.description && !descriptionSaved && (
                    <span className="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded-full animate-pulse">
                      Cambios pendientes
                    </span>
                  )}
                </div>
                <MentionInput
                  value={description}
                  onChange={(value) => {
                    setDescription(value);
                    // Reset saved state when user types
                    if (descriptionSaved) {
                      setDescriptionSaved(false);
                    }
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && e.ctrlKey) {
                      handleDescriptionSave();
                    }
                  }}
                  placeholder="Agregar una descripción más detallada... (usa @ para mencionar miembros)"
                  multiline={true}
                  rows={4}
                />

                {/* Save Button and Tips */}
                <div className="mt-3 flex items-center justify-between">
                  <div className="text-xs text-gray-500">
                    Tip: Usa @ para mencionar miembros del tablero. Presiona Ctrl+Enter para guardar.
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* Success feedback */}
                    {descriptionSaved && (
                      <div className="flex items-center space-x-1 text-green-600 text-sm animate-fade-in">
                        <Check className="w-4 h-4" />
                        <span>Guardado</span>
                      </div>
                    )}

                    {/* Save Button */}
                    <button
                      onClick={handleDescriptionSave}
                      disabled={description === card.description || descriptionSaved}
                      className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                        description !== card.description && !descriptionSaved
                          ? 'bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md transform hover:scale-105 animate-pulse-blue'
                          : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      }`}
                    >
                      <Save className="w-4 h-4" />
                      <span>Guardar Descripción</span>
                    </button>
                  </div>
                </div>

                {/* Show mentioned members */}
                <MentionSummary text={description} className="mt-3" />
              </div>

              {/* Checklist */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-900 flex items-center text-lg">
                    <CheckSquare className="w-5 h-5 mr-2" />
                    LISTA DE VERIFICACIÓN
                  </h3>
                  {normalizedCard.checklist.length > 0 && (
                    <span className="text-sm text-gray-600 font-medium">
                      {completedTasks} de {totalTasks} completadas ({Math.round(progressPercentage)}%)
                    </span>
                  )}
                </div>

                {/* Progress Bar */}
                {normalizedCard.checklist.length > 0 && (
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                    <div
                      className={`h-3 rounded-full transition-all duration-500 ${
                        progressPercentage === 100 ? 'bg-green-500' : 'bg-blue-500'
                      }`}
                      style={{ width: `${progressPercentage}%` }}
                    />
                  </div>
                )}

                {/* Checklist Items */}
                {normalizedCard.checklist.length > 0 && (
                  <div className="space-y-3 mb-4">
                    {normalizedCard.checklist.map((item) => (
                      <div key={item.id} className="group flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <input
                          type="checkbox"
                          checked={item.completed}
                          onChange={() => handleChecklistToggle(item.id)}
                          className="w-5 h-5 text-blue-600 rounded focus:ring-blue-500 mt-0.5"
                        />

                        <div className="flex-1 min-w-0">
                          {editingChecklistItem === item.id ? (
                            <div className="space-y-2">
                              <MentionInput
                                value={editingChecklistText}
                                onChange={setEditingChecklistText}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter' && !e.shiftKey) {
                                    e.preventDefault();
                                    handleEditChecklistItem(item.id, editingChecklistText);
                                  }
                                  if (e.key === 'Escape') {
                                    cancelEditingChecklistItem();
                                  }
                                }}
                                placeholder="Editar elemento..."
                                className="text-sm"
                                autoFocus
                              />
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => handleEditChecklistItem(item.id, editingChecklistText)}
                                  disabled={!editingChecklistText.trim()}
                                  className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:bg-gray-400 transition-colors"
                                >
                                  <Save className="w-3 h-3" />
                                  <span>Guardar</span>
                                </button>
                                <button
                                  onClick={cancelEditingChecklistItem}
                                  className="flex items-center space-x-1 px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
                                >
                                  <X className="w-3 h-3" />
                                  <span>Cancelar</span>
                                </button>
                              </div>
                            </div>
                          ) : (
                            <div
                              className={`cursor-pointer ${item.completed ? 'line-through text-gray-500' : 'text-gray-900'}`}
                              onClick={() => startEditingChecklistItem(item)}
                            >
                              <MentionText
                                text={item.text}
                                onMentionClick={(member) => {
                                  console.log('Mentioned member in checklist:', member);
                                }}
                              />
                            </div>
                          )}
                        </div>

                        {/* Action Buttons */}
                        {editingChecklistItem !== item.id && (
                          <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <button
                              onClick={() => startEditingChecklistItem(item)}
                              className="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                              title="Editar elemento"
                            >
                              <Edit3 className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteChecklistItem(item.id)}
                              className="p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                              title="Eliminar elemento"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Add Checklist Item */}
              <div className="space-y-3">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-blue-400 hover:bg-blue-50 transition-colors">
                  <div className="flex items-start space-x-3">
                    <Plus className="w-5 h-5 text-gray-400 mt-1" />
                    <div className="flex-1 space-y-2">
                      <MentionInput
                        value={newChecklistItem}
                        onChange={setNewChecklistItem}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            e.stopPropagation();
                            handleAddChecklistItem();
                          }
                        }}
                        placeholder="Agregar nuevo elemento a la lista de verificación... (usa @ para mencionar miembros)"
                        className="text-sm"
                      />
                      {newChecklistItem && (
                        <MentionSummary text={newChecklistItem} className="text-xs" showTitle={false} />
                      )}
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddChecklistItem();
                      }}
                      disabled={!newChecklistItem.trim()}
                      className="flex items-center space-x-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                      <span>Agregar</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* Attachments */}
              {normalizedCard.attachments.length > 0 && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <Paperclip className="w-4 h-4 mr-2" />
                    Archivos Adjuntos ({normalizedCard.attachments.length})
                  </h3>
                  <div className="space-y-2">
                    {normalizedCard.attachments.map((attachment) => (
                      <div key={attachment.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg group">
                        {attachment.type === 'link' ? (
                          <ExternalLink className="w-4 h-4 text-blue-500" />
                        ) : (
                          <Paperclip className="w-4 h-4 text-gray-500" />
                        )}
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900">{attachment.name}</div>
                          {attachment.size && (
                            <div className="text-xs text-gray-500">
                              {Math.round(attachment.size / 1024)} KB
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => window.open(attachment.url, '_blank')}
                            className="text-blue-600 hover:text-blue-700 text-sm flex items-center space-x-1"
                          >
                            <Download className="w-3 h-3" />
                            <span>{attachment.type === 'link' ? 'Abrir' : 'Descargar'}</span>
                          </button>
                          <button
                            onClick={() => handleRemoveAttachment(attachment.id)}
                            className="text-red-600 hover:text-red-700 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Comments */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Actividad
                </h3>
                
                {/* Add Comment */}
                <div className="mb-4 space-y-2">
                  <MentionInput
                    value={newComment}
                    onChange={setNewComment}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && e.ctrlKey) {
                        e.preventDefault();
                        e.stopPropagation();
                        handleAddComment();
                      }
                    }}
                    placeholder="Escribir un comentario... (usa @ para mencionar miembros)"
                    multiline={true}
                    rows={3}
                  />
                  <div className="flex justify-between items-center">
                    <div className="text-xs text-gray-500">
                      Tip: Presiona Ctrl+Enter para enviar
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddComment();
                      }}
                      disabled={!newComment.trim()}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                    >
                      Comentar
                    </button>
                  </div>
                  {newComment && (
                    <MentionSummary text={newComment} showTitle={false} />
                  )}
                </div>

                {/* Comments List */}
                <div className="space-y-4">
                  {normalizedCard.comments.map((comment) => {
                    const isEditing = editingCommentId === comment.id;
                    const canEdit = currentUser && comment.author?.id === currentUser.id;

                    return (
                      <div key={comment.id} className="flex space-x-3">
                        <img
                          src={comment.author?.avatar || '/default-avatar.png'}
                          alt={comment.author?.name || 'Usuario'}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                        <div className="flex-1">
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <div className="flex items-center justify-between mb-1">
                              <div className="flex items-center space-x-2">
                                <span className="font-medium text-sm">{comment.author?.name || 'Usuario'}</span>
                                <span className="text-xs text-gray-500">
                                  {formatDateTime(comment.createdAt)}
                                  {comment.updatedAt && comment.updatedAt !== comment.createdAt && (
                                    <span className="ml-1">(editado)</span>
                                  )}
                                </span>
                              </div>
                              {canEdit && !isEditing && (
                                <div className="flex items-center space-x-1">
                                  <button
                                    onClick={() => handleEditComment(comment.id)}
                                    className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                    title="Editar comentario"
                                  >
                                    <Edit3 className="w-3 h-3" />
                                  </button>
                                  <button
                                    onClick={() => handleDeleteComment(comment.id)}
                                    className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                    title="Eliminar comentario"
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </button>
                                </div>
                              )}
                            </div>

                            {isEditing ? (
                              <div className="space-y-2">
                                <MentionInput
                                  value={editingCommentText}
                                  onChange={setEditingCommentText}
                                  placeholder="Editar comentario..."
                                  multiline={true}
                                  rows={3}
                                  autoFocus={true}
                                />
                                <div className="flex justify-end space-x-2">
                                  <button
                                    onClick={handleCancelCommentEdit}
                                    className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                                  >
                                    Cancelar
                                  </button>
                                  <button
                                    onClick={handleSaveCommentEdit}
                                    disabled={!editingCommentText.trim()}
                                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                                  >
                                    Guardar
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <MentionText
                                text={comment.text}
                                className="text-sm text-gray-900"
                                onMentionClick={(member) => {
                                  // Opcional: mostrar perfil del miembro o hacer algo al hacer click
                                  console.log('Clicked on member:', member);
                                }}
                              />
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar Actions */}
          <div className={`sidebar-actions ${isMobile ? 'w-full' : 'w-80 min-w-80'} bg-gray-50 ${isMobile ? 'p-3' : 'p-4'} border-l relative overflow-visible min-h-0 flex-shrink-0`}>
            <h3 className="font-semibold text-gray-900 mb-3">Acciones</h3>
            <div className="space-y-2">
              <div className="relative overflow-visible">
                <button
                  onClick={() => {
                    closeAllModals();
                    setShowMemberSelector(!showMemberSelector);
                  }}
                  className="w-full flex items-center space-x-2 p-2 text-left hover:bg-gray-100 rounded-lg"
                >
                  <User className="w-4 h-4" />
                  <span className="text-sm">Miembros</span>
                </button>
                {showMemberSelector && (
                  <MemberSelector
                    availableMembers={getAvailableUsersForCardAssignment(
                      getCurrentBoard(),
                      state.teams,
                      currentUser!,
                      state.users
                    )}
                    assignedMembers={normalizedCard.assignedMembers}
                    onToggleMember={handleToggleMember}
                    onClose={() => setShowMemberSelector(false)}
                    currentUser={currentUser!}
                    isTeamRestricted={!!getCurrentBoard()?.teamId}
                  />
                )}
              </div>
              <div className="relative overflow-visible">
                <button
                  onClick={() => {
                    closeAllModals();
                    setShowLabelSelector(!showLabelSelector);
                  }}
                  className="w-full flex items-center space-x-2 p-2 text-left hover:bg-gray-100 rounded-lg"
                >
                  <Tag className="w-4 h-4" />
                  <span className="text-sm">Etiquetas</span>
                </button>
                {showLabelSelector && (
                  <LabelSelector
                    availableLabels={state.labels}
                    selectedLabels={card.labels || []}
                    onToggleLabel={handleToggleLabel}
                    onCreateLabel={handleCreateLabel}
                    onClose={() => setShowLabelSelector(false)}
                  />
                )}
              </div>
              <div className="relative overflow-visible">
                <button
                  onClick={() => {
                    closeAllModals();
                    setShowDatePicker(!showDatePicker);
                  }}
                  className="w-full flex items-center space-x-2 p-2 text-left hover:bg-gray-100 rounded-lg"
                >
                  <Calendar className="w-4 h-4" />
                  <span className="text-sm">Fecha de Vencimiento</span>
                </button>
                {showDatePicker && (
                  <DatePicker
                    currentDate={card.dueDate}
                    onDateChange={handleDateChange}
                    onClose={() => setShowDatePicker(false)}
                  />
                )}
              </div>
              <div className="relative overflow-visible">
                <button
                  onClick={() => {
                    closeAllModals();
                    setShowAttachmentUploader(!showAttachmentUploader);
                  }}
                  className="w-full flex items-center space-x-2 p-2 text-left hover:bg-gray-100 rounded-lg"
                >
                  <Paperclip className="w-4 h-4" />
                  <span className="text-sm">Archivo Adjunto</span>
                </button>
                {showAttachmentUploader && (
                  <AttachmentUploader
                    onAddAttachment={handleAddAttachment}
                    onClose={() => setShowAttachmentUploader(false)}
                  />
                )}
              </div>
              <div className="relative overflow-visible">
                <button
                  onClick={() => {
                    closeAllModals();
                    setShowCoverSelector(!showCoverSelector);
                  }}
                  className="w-full flex items-center space-x-2 p-2 text-left hover:bg-gray-100 rounded-lg"
                >
                  <Eye className="w-4 h-4" />
                  <span className="text-sm">Portada</span>
                </button>
                {showCoverSelector && (
                  <CoverSelector
                    currentCover={card.cover}
                    onSetCover={handleSetCover}
                    onRemoveCover={handleRemoveCover}
                    onClose={() => setShowCoverSelector(false)}
                  />
                )}
              </div>
              <hr className="my-2" />
              <button
                onClick={handleCopyCard}
                className="w-full flex items-center space-x-2 p-2 text-left hover:bg-gray-100 rounded-lg"
              >
                <Copy className="w-4 h-4" />
                <span className="text-sm">Copiar</span>
              </button>
              <button
                onClick={handleArchiveCard}
                className="w-full flex items-center space-x-2 p-2 text-left hover:bg-gray-100 rounded-lg text-red-600"
              >
                <Archive className="w-4 h-4" />
                <span className="text-sm">Archivar</span>
              </button>
            </div>


          </div>
        </div>
      </div>
    </div>
  );
}