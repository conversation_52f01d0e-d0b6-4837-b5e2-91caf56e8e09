{"timestamp": "2025-06-25T06:50:20.040Z", "totalReferences": 16, "files": ["\\src\\components\\DatabaseSettings.tsx", "\\src\\context\\AppContext.tsx", "\\src\\services\\SimpleAuthService.ts"], "details": [{"file": "\\src\\components\\DatabaseSettings.tsx", "line": 85, "content": "{usePrisma ? 'SQLite con Prisma ORM' : 'localStorage del navegador'}"}, {"file": "\\src\\components\\DatabaseSettings.tsx", "line": 149, "content": "Almacena los datos en el navegador usando localStorage. Ideal para desarrollo y pruebas."}, {"file": "\\src\\context\\AppContext.tsx", "line": 602, "content": "// const persistence = usePersistence(); // DEPRECATED: No longer using localStorage"}, {"file": "\\src\\context\\AppContext.tsx", "line": 612, "content": "// DEPRECATED: No longer loading from localStorage - data is loaded via DatabaseAppContext"}, {"file": "\\src\\context\\AppContext.tsx", "line": 619, "content": "// Perform localStorage maintenance before loading data"}, {"file": "\\src\\context\\AppContext.tsx", "line": 623, "content": "// Check if localStorage is critically full"}, {"file": "\\src\\context\\AppContext.tsx", "line": 625, "content": "console.warn('localStorage critically full, performing progressive cleanup');"}, {"file": "\\src\\context\\AppContext.tsx", "line": 637, "content": "console.warn('Failed to perform localStorage maintenance:', error);"}, {"file": "\\src\\context\\AppContext.tsx", "line": 642, "content": "// const savedData = persistence.loadAppData(); // DEPRECATED: No longer using localStorage"}, {"file": "\\src\\context\\AppContext.tsx", "line": 644, "content": "// DEPRECATED: No longer loading from localStorage"}, {"file": "\\src\\context\\AppContext.tsx", "line": 743, "content": "// DEPRECATED: No longer loading from localStorage"}, {"file": "\\src\\context\\AppContext.tsx", "line": 774, "content": "// DEPRECATED: No longer auto-saving to localStorage - data is persisted via API"}, {"file": "\\src\\context\\AppContext.tsx", "line": 799, "content": "// DEPRECATED: No longer saving preferences to localStorage"}, {"file": "\\src\\services\\SimpleAuthService.ts", "line": 14, "content": "// No longer loading from localStorage - user state is managed in memory only"}, {"file": "\\src\\services\\SimpleAuthService.ts", "line": 25, "content": "// No longer using localStorage - user state is managed in memory only"}, {"file": "\\src\\services\\SimpleAuthService.ts", "line": 168, "content": "// No longer using localStorage - user state is managed in memory only"}]}