import React from 'react';
import { useResponsive } from '../hooks/useResponsive';

interface TouchOptimizedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
}

export function TouchOptimizedButton({
  children,
  variant = 'secondary',
  size = 'md',
  icon,
  iconPosition = 'left',
  fullWidth = false,
  className = '',
  disabled = false,
  ...props
}: TouchOptimizedButtonProps) {
  const { isMobile } = useResponsive();

  const getVariantClasses = () => {
    const baseClasses = 'transition-all duration-200 font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    switch (variant) {
      case 'primary':
        return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-300`;
      case 'secondary':
        return `${baseClasses} bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500 disabled:bg-gray-50 disabled:text-gray-400`;
      case 'ghost':
        return `${baseClasses} text-gray-600 hover:bg-gray-100 focus:ring-gray-500 disabled:text-gray-400`;
      case 'danger':
        return `${baseClasses} bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 disabled:bg-red-300`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500`;
    }
  };

  const getSizeClasses = () => {
    // Ensure minimum touch target size of 44px on mobile
    const mobileMinHeight = 'min-h-[44px]';
    
    switch (size) {
      case 'sm':
        return isMobile 
          ? `px-3 py-2 text-sm ${mobileMinHeight}` 
          : 'px-3 py-1.5 text-sm';
      case 'md':
        return isMobile 
          ? `px-4 py-3 text-base ${mobileMinHeight}` 
          : 'px-4 py-2 text-sm';
      case 'lg':
        return isMobile 
          ? `px-6 py-4 text-lg ${mobileMinHeight}` 
          : 'px-6 py-3 text-base';
      default:
        return isMobile 
          ? `px-4 py-3 text-base ${mobileMinHeight}` 
          : 'px-4 py-2 text-sm';
    }
  };

  const getSpacingClasses = () => {
    if (!icon) return '';
    return isMobile ? 'space-x-2' : 'space-x-1.5';
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'md':
        return isMobile ? 'w-5 h-5' : 'w-4 h-4';
      case 'lg':
        return 'w-6 h-6';
      default:
        return isMobile ? 'w-5 h-5' : 'w-4 h-4';
    }
  };

  const buttonClasses = `
    ${getVariantClasses()}
    ${getSizeClasses()}
    ${fullWidth ? 'w-full' : ''}
    ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}
    ${className}
  `.trim();

  const iconElement = icon && (
    <span className={`${getIconSize()} flex-shrink-0`}>
      {icon}
    </span>
  );

  return (
    <button
      className={buttonClasses}
      disabled={disabled}
      {...props}
    >
      <div className={`flex items-center justify-center ${getSpacingClasses()}`}>
        {icon && iconPosition === 'left' && iconElement}
        <span className={fullWidth ? 'flex-1 text-center' : ''}>{children}</span>
        {icon && iconPosition === 'right' && iconElement}
      </div>
    </button>
  );
}

// Specialized button variants for common use cases
export function PrimaryButton(props: Omit<TouchOptimizedButtonProps, 'variant'>) {
  return <TouchOptimizedButton variant="primary" {...props} />;
}

export function SecondaryButton(props: Omit<TouchOptimizedButtonProps, 'variant'>) {
  return <TouchOptimizedButton variant="secondary" {...props} />;
}

export function GhostButton(props: Omit<TouchOptimizedButtonProps, 'variant'>) {
  return <TouchOptimizedButton variant="ghost" {...props} />;
}

export function DangerButton(props: Omit<TouchOptimizedButtonProps, 'variant'>) {
  return <TouchOptimizedButton variant="danger" {...props} />;
}
