import React, { useState } from 'react';
import { Plus, X, Check } from 'lucide-react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { List } from '../types';

interface AddListFormProps {
  boardId: string;
}

export function AddListForm({ boardId }: AddListFormProps) {
  const { addList } = useDatabaseApp();
  const [isAdding, setIsAdding] = useState(false);
  const [title, setTitle] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim()) return;

    const newListData = {
      title: title.trim(),
      cards: [],
      // Position will be calculated by the server
    };

    await addList(boardId, newListData);

    // Reset form
    setTitle('');
    setIsAdding(false);
  };

  const handleCancel = () => {
    setTitle('');
    setIsAdding(false);
  };

  if (!isAdding) {
    return (
      <div className="w-72 flex-shrink-0">
        <button 
          onClick={() => setIsAdding(true)}
          className="w-full p-4 bg-white bg-opacity-20 backdrop-blur-sm text-white rounded-lg border-2 border-dashed border-white border-opacity-50 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center space-x-2 group"
        >
          <Plus className="w-5 h-5 group-hover:scale-110 transition-transform" />
          <span>Agregar otra lista</span>
        </button>
      </div>
    );
  }

  return (
    <div className="w-72 flex-shrink-0">
      <div className="bg-white bg-opacity-90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
        <form onSubmit={handleSubmit} className="space-y-3">
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Introduce el título de la lista..."
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500"
            autoFocus
            maxLength={100}
          />
          
          <div className="flex items-center space-x-2">
            <button
              type="submit"
              disabled={!title.trim()}
              className="flex items-center space-x-1 bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              <Check className="w-4 h-4" />
              <span>Agregar lista</span>
            </button>
            
            <button
              type="button"
              onClick={handleCancel}
              className="flex items-center space-x-1 bg-gray-200 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-300 transition-colors"
            >
              <X className="w-4 h-4" />
              <span>Cancelar</span>
            </button>
          </div>
        </form>
        
        {/* Character counter */}
        <div className="mt-2 text-xs text-gray-500 text-right">
          {title.length}/100
        </div>
      </div>
    </div>
  );
}
