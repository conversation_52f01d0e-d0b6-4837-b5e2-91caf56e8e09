import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateToDatabase() {
  console.log('🚀 Starting migration from localStorage to database...\n');

  try {
    // Clear existing data (optional - for clean migration)
    console.log('🧹 Clearing existing data...');

    // Delete in correct order to respect foreign key constraints
    try {
      await prisma.cardLabel.deleteMany();
      await prisma.cardMember.deleteMany();
      await prisma.checklistItem.deleteMany();
      await prisma.comment.deleteMany();
      await prisma.attachment.deleteMany();
      await prisma.card.deleteMany();
      await prisma.list.deleteMany();
      await prisma.boardMember.deleteMany();
      await prisma.board.deleteMany();
      await prisma.teamMember.deleteMany();
      await prisma.teamSettings.deleteMany();
      await prisma.team.deleteMany();
      await prisma.label.deleteMany();
      await prisma.userPermissions.deleteMany();
      await prisma.user.deleteMany();
    } catch (error) {
      console.log('⚠️ Some tables may not exist yet, continuing...');
    }

    console.log('✅ Existing data cleared\n');

    // Get existing users or create default ones
    console.log('👥 Getting existing users...');
    let createdUsers = await prisma.user.findMany({
      include: {
        permissions: true
      }
    });

    if (createdUsers.length === 0) {
      console.log('No users found, creating default users...');
      const defaultUsers = [
        {
          username: 'admin',
          name: 'Administrador',
          email: '<EMAIL>',
          phone: '+1234567890',
          password: 'admin123',
          role: 'ADMIN',
        },
        {
          username: 'ana.garcia',
          name: 'Ana García',
          email: '<EMAIL>',
          phone: '+1234567891',
          password: 'ana123',
          role: 'MEMBER',
        },
        {
          username: 'carlos.lopez',
          name: 'Carlos López',
          email: '<EMAIL>',
          phone: '+1234567892',
          password: 'carlos123',
          role: 'OBSERVER',
        }
      ];

      createdUsers = [];
      for (const userData of defaultUsers) {
        const user = await prisma.user.create({
          data: {
            ...userData,
            avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.name)}&background=3b82f6&color=fff&size=128`,
            permissions: {
              create: {
                canCreateBoards: userData.role === 'ADMIN',
                canEditCards: true,
                canDeleteCards: userData.role === 'ADMIN',
                canManageUsers: userData.role === 'ADMIN',
                canViewReports: true,
                canManageSettings: userData.role === 'ADMIN',
                canInviteUsers: userData.role === 'ADMIN',
                canArchiveCards: true,
              }
            }
          }
        });
        createdUsers.push(user);
        console.log(`✅ User created: ${userData.name} (${userData.email})`);
      }
    } else {
      console.log(`✅ Found ${createdUsers.length} existing users`);
      createdUsers.forEach(user => {
        console.log(`   - ${user.name} (${user.email})`);
      });
    }

    // We'll create labels after creating the board since they need a boardId

    // Create sample team
    console.log('\n🏢 Creating sample team...');
    const adminUser = createdUsers.find(u => u.role === 'ADMIN');
    const sampleTeam = await prisma.team.create({
      data: {
        name: 'Equipo de Desarrollo',
        description: 'Equipo principal de desarrollo de la aplicación',
        avatar: 'https://ui-avatars.com/api/?name=Equipo+Desarrollo&background=10b981&color=fff&size=128',
        visibility: 'PRIVATE',
        settings: {
          create: {
            allowMembersToCreateBoards: false,
            allowMembersToInvite: false,
            defaultBoardVisibility: 'TEAM',
            requireApprovalForJoining: true,
          }
        },
        members: {
          create: [
            {
              userId: adminUser.id,
              role: 'ADMIN',
            },
            {
              userId: createdUsers.find(u => u.role === 'MEMBER').id,
              role: 'NORMAL',
            }
          ]
        }
      }
    });
    console.log(`✅ Team created: ${sampleTeam.name}`);

    // Create sample board
    console.log('\n📋 Creating sample board...');
    const sampleBoard = await prisma.board.create({
      data: {
        title: 'Proyecto Principal',
        description: 'Tablero principal para el desarrollo del proyecto',
        background: '#3b82f6',
        isPublic: false,
        isFavorite: true,
        visibility: 'TEAM',
        teamId: sampleTeam.id,
        createdById: adminUser.id,
        members: {
          create: [
            {
              userId: adminUser.id,
              role: 'ADMIN',
            },
            {
              userId: createdUsers.find(u => u.role === 'MEMBER').id,
              role: 'MEMBER',
            }
          ]
        },

      }
    });
    console.log(`✅ Board created: ${sampleBoard.title}`);

    // Create default labels for the board
    console.log('\n🏷️ Creating default labels...');
    const defaultLabels = [
      { name: 'Alta Prioridad', color: '#ef4444' },
      { name: 'Prioridad Media', color: '#f97316' },
      { name: 'Baja Prioridad', color: '#22c55e' },
      { name: 'Error', color: '#dc2626' },
      { name: 'Funcionalidad', color: '#3b82f6' },
      { name: 'Documentación', color: '#8b5cf6' }
    ];

    const createdLabels = [];
    for (const labelData of defaultLabels) {
      const label = await prisma.label.create({
        data: {
          ...labelData,
          boardId: sampleBoard.id
        }
      });
      createdLabels.push(label);
      console.log(`✅ Label created: ${labelData.name}`);
    }

    // Create sample lists
    console.log('\n📝 Creating sample lists...');
    const sampleLists = [
      { title: 'Por Hacer', position: 0 },
      { title: 'En Progreso', position: 1 },
      { title: 'Revisión', position: 2 },
      { title: 'Completado', position: 3 }
    ];

    const createdLists = [];
    for (const listData of sampleLists) {
      const list = await prisma.list.create({
        data: {
          ...listData,
          boardId: sampleBoard.id
        }
      });
      createdLists.push(list);
      console.log(`✅ List created: ${listData.title}`);
    }

    // Create sample cards
    console.log('\n🃏 Creating sample cards...');
    const sampleCards = [
      {
        title: 'Configurar base de datos',
        description: 'Configurar Prisma y SQLite para el proyecto',
        listIndex: 3, // Completado
        labels: [0] // Alta Prioridad
      },
      {
        title: 'Implementar autenticación',
        description: 'Sistema de login y registro de usuarios',
        listIndex: 2, // Revisión
        labels: [0, 1] // Alta y Media Prioridad
      },
      {
        title: 'Crear interfaz de usuario',
        description: 'Diseñar y desarrollar la interfaz principal',
        listIndex: 1, // En Progreso
        labels: [1] // Media Prioridad
      },
      {
        title: 'Implementar notificaciones',
        description: 'Sistema de notificaciones en tiempo real',
        listIndex: 0, // Por Hacer
        labels: [2] // Baja Prioridad
      }
    ];

    for (let i = 0; i < sampleCards.length; i++) {
      const cardData = sampleCards[i];
      const card = await prisma.card.create({
        data: {
          title: cardData.title,
          description: cardData.description,
          position: i,
          listId: createdLists[cardData.listIndex].id,
          assignedMembers: {
            create: {
              userId: createdUsers[i % createdUsers.length].id
            }
          },
          labels: {
            create: cardData.labels.map(labelIndex => ({
              labelId: createdLabels[labelIndex].id
            }))
          }
        }
      });
      console.log(`✅ Card created: ${cardData.title}`);
    }

    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   - Users: ${createdUsers.length}`);
    console.log(`   - Labels: ${createdLabels.length}`);
    console.log(`   - Teams: 1`);
    console.log(`   - Boards: 1`);
    console.log(`   - Lists: ${createdLists.length}`);
    console.log(`   - Cards: ${sampleCards.length}`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

migrateToDatabase();
