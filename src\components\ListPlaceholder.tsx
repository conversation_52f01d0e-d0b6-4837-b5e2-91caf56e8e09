import React from 'react';

interface ListPlaceholderProps {
  draggedList?: {
    id: string;
    title: string;
    cards: any[];
  } | null;
  position: number;
  onDragOver: (e: React.DragEvent, targetId: string, targetType?: 'card' | 'list') => void;
  onDragLeave: () => void;
  onDrop: (e: React.DragEvent, targetId: string) => void;
  isDragOver: boolean;
}

export function ListPlaceholder({
  draggedList,
  position,
  onDragOver,
  onDragLeave,
  onDrop,
  isDragOver
}: ListPlaceholderProps) {
  if (!draggedList) {
    return (
      <div
        className={`w-72 h-96 border-2 border-dashed rounded-lg flex items-center justify-center transition-all duration-200 ${
          isDragOver
            ? 'bg-blue-100 border-blue-500 shadow-lg'
            : 'bg-gray-200 border-gray-400 opacity-60'
        }`}
        onDragOver={(e) => onDragOver(e, position.toString(), 'list')}
        onDragLeave={onDragLeave}
        onDrop={(e) => onDrop(e, position.toString())}
      >
        <div className={`text-sm font-medium ${isDragOver ? 'text-blue-700' : 'text-gray-500'}`}>
          Soltar lista aquí
        </div>
      </div>
    );
  }

  return (
    <div
      className={`w-72 border-2 border-dashed rounded-lg shadow-sm transition-all duration-200 ${
        isDragOver
          ? 'bg-blue-50 border-blue-500 opacity-80 shadow-lg'
          : 'bg-gray-100 border-blue-400 opacity-60'
      }`}
      onDragOver={(e) => onDragOver(e, position.toString(), 'list')}
      onDragLeave={onDragLeave}
      onDrop={(e) => onDrop(e, position.toString())}
    >
      {/* Placeholder Header */}
      <div className="flex items-center justify-between p-3 border-b bg-gray-50 rounded-t-lg">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-gray-300 rounded"></div>
          <h2 className="font-semibold text-gray-500">{draggedList.title}</h2>
        </div>
        <div className="flex items-center space-x-1">
          <span className="text-sm text-gray-400 bg-gray-200 px-2 py-1 rounded-full">
            {draggedList.cards.length}
          </span>
          <div className="w-4 h-4 bg-gray-300 rounded"></div>
        </div>
      </div>

      {/* Placeholder Cards */}
      <div className="p-3 space-y-3">
        {draggedList.cards.slice(0, 3).map((_, index) => (
          <div
            key={index}
            className="h-20 bg-gray-200 rounded-lg border border-gray-300 animate-pulse"
          />
        ))}
        {draggedList.cards.length > 3 && (
          <div className="text-center text-xs text-gray-400 py-2">
            +{draggedList.cards.length - 3} tarjetas más
          </div>
        )}

        {/* Add card placeholder */}
        <div className="h-10 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
          <span className="text-xs text-gray-400">+ Agregar tarjeta</span>
        </div>
      </div>
    </div>
  );
}
