import React, { useState } from 'react';
import { User } from '../types';
import { ResponsiveTable, useResponsiveColumns } from './ResponsiveTable';
import { TouchOptimizedButton } from './TouchOptimizedButton';
import { UserEditModal } from './UserEditModal';
import { useResponsive } from '../hooks/useResponsive';
import {
  Edit,
  Trash2,
  ToggleLeft,
  ToggleRight,
  Mail,
  Crown,
  Shield,
  Eye,
  Activity
} from 'lucide-react';

interface UserListProps {
  users: User[];
  loading: boolean;
  onUpdateUser: (id: string, updates: Partial<User>) => void;
  onDeleteUser: (id: string) => void;
  onToggleStatus: (id: string) => void;
  onResetPassword: (id: string) => void;
}

export function UserList({
  users,
  loading,
  onUpdateUser,
  onDeleteUser,
  onToggleStatus,
  onResetPassword
}: UserListProps) {
  const [showActions, setShowActions] = useState<string | null>(null);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const { isMobile } = useResponsive();
  const { createColumn } = useResponsiveColumns();

  // UserService no longer needed - using API directly via props

  const handleEditStart = (user: User) => {
    setEditingUser(user);
    setShowEditModal(true);
  };

  const handleEditSave = (userId: string, userData: Partial<User>) => {
    console.log('💾 Saving user edits via modal:', {
      userId,
      fields: Object.keys(userData),
      hasPassword: !!userData.password
    });

    onUpdateUser(userId, userData);
    setShowEditModal(false);
    setEditingUser(null);
  };

  const handleEditCancel = () => {
    setShowEditModal(false);
    setEditingUser(null);
  };

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'member':
        return <Shield className="w-4 h-4 text-blue-500" />;
      case 'observer':
        return <Eye className="w-4 h-4 text-gray-500" />;
      default:
        return <Shield className="w-4 h-4 text-gray-500" />;
    }
  };

  const getRoleBadgeColor = (role: User['role']) => {
    switch (role) {
      case 'admin':
        return 'bg-yellow-100 text-yellow-800';
      case 'member':
        return 'bg-blue-100 text-blue-800';
      case 'observer':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return 'Never';
    return new Date(date).toLocaleDateString();
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };



  const getRoleBadge = (role: string) => {
    const colors = {
      admin: 'bg-yellow-100 text-yellow-800',
      member: 'bg-blue-100 text-blue-800',
      observer: 'bg-gray-100 text-gray-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[role.toLowerCase() as keyof typeof colors] || colors.observer}`}>
        {getRoleIcon(role)}
        <span className="ml-1 capitalize">{role}</span>
      </span>
    );
  };

  // Define columns for responsive table - optimized for better visibility
  const columns = [
    createColumn('user', 'Usuario', {
      render: (_, user: User) => (
        <div className="flex items-center space-x-3">
          <img
            src={user.avatar}
            alt={user.name}
            className="w-8 h-8 rounded-full object-cover"
          />
          <div className="min-w-0 flex-1">
            <div className="font-medium text-gray-900 truncate">{user.name}</div>
            <div className="text-sm text-gray-500 truncate">@{user.username}</div>
          </div>
        </div>
      ),
      mobileLabel: 'Usuario',
      width: '25%'
    }),
    createColumn('email', 'Email', {
      render: (email: string) => (
        <div className="flex items-center space-x-2">
          <Mail className="w-4 h-4 text-gray-400 flex-shrink-0" />
          <span className="text-sm text-gray-900 truncate">{email}</span>
        </div>
      ),
      mobileHidden: true,
      width: '20%'
    }),

    createColumn('role', 'Rol', {
      render: (role: string) => getRoleBadge(role),
      mobileLabel: 'Rol',
      width: '15%'
    }),
    createColumn('score', 'Score', {
      render: (score: number) => (
        <span className={`font-medium text-sm ${getScoreColor(score)}`}>
          {score}
        </span>
      ),
      mobileLabel: 'Score',
      width: '10%'
    }),
    createColumn('lastLogin', 'Último Acceso', {
      render: (lastLogin: Date | undefined) => (
        <span className="text-xs text-gray-500">
          {formatDate(lastLogin)}
        </span>
      ),
      mobileHidden: true,
      width: '12%'
    }),
    createColumn('actions', 'Acciones', {
      render: (_, user: User) => (
        <div className="flex items-center space-x-1">
          <TouchOptimizedButton
            size="sm"
            variant="ghost"
            icon={<Edit className="w-4 h-4" />}
            onClick={() => handleEditStart(user)}
          >
            {isMobile ? '' : 'Editar'}
          </TouchOptimizedButton>
          <TouchOptimizedButton
            size="sm"
            variant="ghost"
            icon={user.isActive !== false ? <ToggleLeft className="w-4 h-4" /> : <ToggleRight className="w-4 h-4" />}
            onClick={() => onToggleStatus(user.id)}
          >
            {isMobile ? '' : (user.isActive !== false ? 'Desactivar' : 'Activar')}
          </TouchOptimizedButton>
          <TouchOptimizedButton
            size="sm"
            variant="danger"
            icon={<Trash2 className="w-4 h-4" />}
            onClick={() => onDeleteUser(user.id)}
          >
            {isMobile ? '' : 'Eliminar'}
          </TouchOptimizedButton>
        </div>
      ),
      mobileLabel: 'Acciones',
      width: '18%'
    })
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (users.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron usuarios</h3>
        <p className="text-gray-500">Intenta ajustar tus criterios de búsqueda o filtro.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center">
            <Crown className="w-5 h-5 text-blue-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-blue-600">Administradores</p>
              <p className="text-2xl font-bold text-blue-900">
                {users.filter(u => u.role === 'admin').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="flex items-center">
            <Shield className="w-5 h-5 text-green-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-green-600">Miembros</p>
              <p className="text-2xl font-bold text-green-900">
                {users.filter(u => u.role === 'member').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex items-center">
            <Eye className="w-5 h-5 text-gray-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-gray-600">Observadores</p>
              <p className="text-2xl font-bold text-gray-900">
                {users.filter(u => u.role === 'observer').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg">
          <div className="flex items-center">
            <Activity className="w-5 h-5 text-purple-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-purple-600">Activos</p>
              <p className="text-2xl font-bold text-purple-900">
                {users.filter(u => u.isActive !== false).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* User Table */}
      <ResponsiveTable
        columns={columns}
        data={users}
        keyField="id"
        emptyMessage="No se encontraron usuarios"
        onRowClick={isMobile ? (user) => handleEditStart(user) : undefined}
      />

      <UserEditModal
        user={editingUser}
        isOpen={showEditModal}
        onClose={handleEditCancel}
        onSave={handleEditSave}
        loading={loading}
      />
    </div>
  );
}
