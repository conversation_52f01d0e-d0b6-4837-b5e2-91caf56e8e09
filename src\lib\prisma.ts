import { PrismaClient } from '@prisma/client';

// Singleton pattern for Prisma Client
declare global {
  var __prisma: PrismaClient | undefined;
}

export const prisma = globalThis.__prisma || new PrismaClient({
  log: ['query', 'error', 'warn'],
});

if (process.env.NODE_ENV !== 'production') {
  globalThis.__prisma = prisma;
}

// Helper function to handle Prisma errors
export function handlePrismaError(error: any) {
  console.error('Prisma error:', error);
  
  if (error.code === 'P2002') {
    throw new Error('A record with this information already exists');
  }
  
  if (error.code === 'P2025') {
    throw new Error('Record not found');
  }
  
  throw new Error('Database operation failed');
}

// Helper function to convert Prisma dates to JavaScript dates
export function convertDates<T extends Record<string, any>>(obj: T): T {
  const converted = { ...obj };
  
  for (const key in converted) {
    if (converted[key] instanceof Date) {
      // Keep as Date object
      continue;
    } else if (typeof converted[key] === 'string' && isDateString(converted[key])) {
      converted[key] = new Date(converted[key]) as any;
    } else if (typeof converted[key] === 'object' && converted[key] !== null) {
      converted[key] = convertDates(converted[key]);
    }
  }
  
  return converted;
}

function isDateString(value: string): boolean {
  return /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value);
}
