import React from 'react';
import { Card as CardType } from '../types';
import {
  Calendar,
  MessageCircle,
  Paperclip,
  CheckSquare,
  User,
  MapPin,
  Clock,
  AlertCircle
} from 'lucide-react';
import { ensureDate } from '../utils/dateUtils';

interface SearchResultsProps {
  cards: (CardType & { listId: string; listTitle: string })[];
  query: string;
  onCardClick: (card: CardType) => void;
  highlightText: (text: string, query: string) => string;
}

export function SearchResults({ cards, query, onCardClick, highlightText }: SearchResultsProps) {
  if (cards.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron resultados</h3>
          <p className="text-gray-500">
            {query 
              ? `No hay tarjetas que coincidan con "${query}"`
              : 'Ajusta los filtros para ver resultados'
            }
          </p>
        </div>
      </div>
    );
  }

  const formatDate = (date: Date | string | null | undefined) => {
    const dateObj = ensureDate(date);
    if (!dateObj || isNaN(dateObj.getTime())) return 'Sin fecha';

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const cardDate = new Date(dateObj.getFullYear(), dateObj.getMonth(), dateObj.getDate());
    
    if (cardDate.getTime() === today.getTime()) {
      return 'Hoy';
    } else if (cardDate.getTime() === today.getTime() - 24 * 60 * 60 * 1000) {
      return 'Ayer';
    } else if (cardDate.getTime() === today.getTime() + 24 * 60 * 60 * 1000) {
      return 'Mañana';
    } else {
      return date.toLocaleDateString('es-ES', { 
        day: 'numeric', 
        month: 'short',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  };

  const isOverdue = (date: Date | string | null | undefined) => {
    const dateObj = ensureDate(date);
    if (!dateObj || isNaN(dateObj.getTime())) return false;

    return dateObj < new Date();
  };

  return (
    <div className="flex-1 overflow-y-auto bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Resultados de búsqueda
          </h2>
          <p className="text-gray-600">
            {cards.length} tarjeta{cards.length !== 1 ? 's' : ''} encontrada{cards.length !== 1 ? 's' : ''}
            {query && ` para "${query}"`}
          </p>
        </div>

        <div className="space-y-4">
          {cards.map((card) => {
            const completedTasks = card.checklist.filter(item => item.completed).length;
            const totalTasks = card.checklist.length;
            const isCardOverdue = card.dueDate && isOverdue(card.dueDate);

            return (
              <div
                key={card.id}
                onClick={() => onCardClick(card)}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-all duration-200 cursor-pointer"
              >
                {/* Card Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h3 
                      className="text-lg font-medium text-gray-900 mb-1"
                      dangerouslySetInnerHTML={{ 
                        __html: highlightText(card.title, query) 
                      }}
                    />
                    <div className="flex items-center text-sm text-gray-500 space-x-2">
                      <MapPin className="w-3 h-3" />
                      <span>en {card.listTitle}</span>
                    </div>
                  </div>

                  {/* Due Date Badge */}
                  {card.dueDate && (
                    <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                      isCardOverdue 
                        ? 'bg-red-100 text-red-700' 
                        : 'bg-blue-100 text-blue-700'
                    }`}>
                      {isCardOverdue ? (
                        <AlertCircle className="w-3 h-3" />
                      ) : (
                        <Clock className="w-3 h-3" />
                      )}
                      <span>{formatDate(card.dueDate)}</span>
                    </div>
                  )}
                </div>

                {/* Labels */}
                {card.labels.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-3">
                    {card.labels.map((label) => (
                      <span
                        key={label.id}
                        className="inline-block px-2 py-1 text-xs font-medium text-white rounded"
                        style={{ backgroundColor: label.color }}
                      >
                        {label.name}
                      </span>
                    ))}
                  </div>
                )}

                {/* Description Preview */}
                {card.description && (
                  <p 
                    className="text-sm text-gray-600 mb-3 line-clamp-2"
                    dangerouslySetInnerHTML={{ 
                      __html: highlightText(card.description, query) 
                    }}
                  />
                )}

                {/* Card Stats */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    {/* Checklist Progress */}
                    {totalTasks > 0 && (
                      <div className="flex items-center space-x-1">
                        <CheckSquare className="w-3 h-3" />
                        <span className={completedTasks === totalTasks ? 'text-green-600 font-medium' : ''}>
                          {completedTasks}/{totalTasks}
                        </span>
                      </div>
                    )}

                    {/* Comments */}
                    {card.comments.length > 0 && (
                      <div className="flex items-center space-x-1">
                        <MessageCircle className="w-3 h-3" />
                        <span>{card.comments.length}</span>
                      </div>
                    )}

                    {/* Attachments */}
                    {card.attachments.length > 0 && (
                      <div className="flex items-center space-x-1">
                        <Paperclip className="w-3 h-3" />
                        <span>{card.attachments.length}</span>
                      </div>
                    )}
                  </div>

                  {/* Assigned Members */}
                  {card.assignedMembers.length > 0 && (
                    <div className="flex -space-x-1">
                      {card.assignedMembers.slice(0, 3).map((member) => (
                        <img
                          key={member.id}
                          src={member.avatar}
                          alt={member.name}
                          className="w-6 h-6 rounded-full border-2 border-white object-cover"
                          title={member.name}
                        />
                      ))}
                      {card.assignedMembers.length > 3 && (
                        <div className="w-6 h-6 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-xs font-medium text-gray-600">
                          +{card.assignedMembers.length - 3}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Matching Comments Preview */}
                {query && card.comments.some(comment => 
                  comment.text.toLowerCase().includes(query.toLowerCase())
                ) && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="text-xs text-gray-500 mb-1">Comentarios que coinciden:</div>
                    {card.comments
                      .filter(comment => comment.text.toLowerCase().includes(query.toLowerCase()))
                      .slice(0, 2)
                      .map(comment => (
                        <div key={comment.id} className="text-xs text-gray-600 bg-gray-50 p-2 rounded mb-1">
                          <span className="font-medium">{comment.author.name}:</span>{' '}
                          <span 
                            dangerouslySetInnerHTML={{ 
                              __html: highlightText(comment.text, query) 
                            }}
                          />
                        </div>
                      ))
                    }
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
