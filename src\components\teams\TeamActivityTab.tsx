import React from 'react';
import { Team } from '../../types';
import { formatDate } from '../../utils/dateUtils';
import {
  Activity,
  UserPlus,
  UserMinus,
  Settings,
  Crown,
  Calendar,
  MessageCircle,
  Plus,
  Edit3
} from 'lucide-react';

interface TeamActivityTabProps {
  team: Team;
}

export function TeamActivityTab({ team }: TeamActivityTabProps) {
  // Mock activity data - in a real app, this would come from the backend
  const activities = [
    {
      id: '1',
      type: 'member_added',
      user: '<PERSON>',
      target: '<PERSON>',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      icon: UserPlus,
      color: 'text-green-600 bg-green-100'
    },
    {
      id: '2',
      type: 'role_changed',
      user: '<PERSON>',
      target: '<PERSON>',
      details: 'de Miembro a Administrador',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      icon: Crown,
      color: 'text-yellow-600 bg-yellow-100'
    },
    {
      id: '3',
      type: 'team_updated',
      user: '<PERSON>',
      details: 'actualizó la configuración del equipo',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      icon: Settings,
      color: 'text-blue-600 bg-blue-100'
    },
    {
      id: '4',
      type: 'board_created',
      user: 'María García',
      details: 'creó el tablero "Proyecto Alpha"',
      timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      icon: Plus,
      color: 'text-purple-600 bg-purple-100'
    },
    {
      id: '5',
      type: 'team_created',
      user: 'Juan Pérez',
      details: 'creó el equipo',
      timestamp: new Date(team.createdAt),
      icon: Activity,
      color: 'text-indigo-600 bg-indigo-100'
    }
  ];

  const getActivityMessage = (activity: any) => {
    switch (activity.type) {
      case 'member_added':
        return `${activity.user} agregó a ${activity.target} al equipo`;
      case 'member_removed':
        return `${activity.user} removió a ${activity.target} del equipo`;
      case 'role_changed':
        return `${activity.user} cambió el rol de ${activity.target} ${activity.details}`;
      case 'team_updated':
        return `${activity.user} ${activity.details}`;
      case 'board_created':
        return `${activity.user} ${activity.details}`;
      case 'team_created':
        return `${activity.user} ${activity.details}`;
      default:
        return `${activity.user} realizó una acción`;
    }
  };

  const formatRelativeTime = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    const diffWeeks = Math.floor(diffDays / 7);

    if (diffHours < 1) return 'Hace menos de una hora';
    if (diffHours < 24) return `Hace ${diffHours} hora${diffHours > 1 ? 's' : ''}`;
    if (diffDays < 7) return `Hace ${diffDays} día${diffDays > 1 ? 's' : ''}`;
    if (diffWeeks < 4) return `Hace ${diffWeeks} semana${diffWeeks > 1 ? 's' : ''}`;
    
    return formatDate(date, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900">
          Actividad del Equipo
        </h3>
        <p className="text-sm text-gray-500">
          Historial de cambios y actividades en el equipo
        </p>
      </div>

      {/* Activity Timeline */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        {activities.length === 0 ? (
          <div className="p-8 text-center">
            <Activity className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <h4 className="text-lg font-medium text-gray-600 mb-2">
              No hay actividad reciente
            </h4>
            <p className="text-gray-500">
              La actividad del equipo aparecerá aquí
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {activities.map((activity, index) => {
              const Icon = activity.icon;
              
              return (
                <div key={activity.id} className="p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-full ${activity.color}`}>
                      <Icon className="w-4 h-4" />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">
                        {getActivityMessage(activity)}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-xs text-gray-500 flex items-center space-x-1">
                          <Calendar className="w-3 h-3" />
                          <span>{formatRelativeTime(activity.timestamp)}</span>
                        </span>
                      </div>
                    </div>

                    <div className="text-xs text-gray-400">
                      {activity.timestamp.toLocaleTimeString('es-ES', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Activity Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
          <UserPlus className="w-6 h-6 text-green-500 mx-auto mb-2" />
          <div className="text-lg font-semibold text-gray-900">{team.members.length}</div>
          <div className="text-sm text-gray-500">Miembros totales</div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
          <Crown className="w-6 h-6 text-yellow-500 mx-auto mb-2" />
          <div className="text-lg font-semibold text-gray-900">
            {team.members.filter(m => m.role === 'admin').length}
          </div>
          <div className="text-sm text-gray-500">Administradores</div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
          <Calendar className="w-6 h-6 text-blue-500 mx-auto mb-2" />
          <div className="text-lg font-semibold text-gray-900">
            {Math.floor((new Date().getTime() - new Date(team.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
          </div>
          <div className="text-sm text-gray-500">Días activo</div>
        </div>
      </div>

      {/* Recent Activity Summary */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-3">Resumen de Actividad</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700">Última actividad:</span>
            <span className="text-gray-600 ml-2">
              {activities.length > 0 ? formatRelativeTime(activities[0].timestamp) : 'Sin actividad'}
            </span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Equipo creado:</span>
            <span className="text-gray-600 ml-2">
              {new Date(team.createdAt).toLocaleDateString('es-ES', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
