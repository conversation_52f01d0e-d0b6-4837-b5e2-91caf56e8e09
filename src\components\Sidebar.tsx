import React, { useState } from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { useAuth } from '../context/AuthContext';
import { useResponsive } from '../hooks/useResponsive';
import { BoardManager } from './BoardManager';
import { MiniCalendar } from './MiniCalendar';
import { Calendar as FullCalendar } from './Calendar';
import {
  Plus,
  Star,
  Archive,
  Calendar,
  BarChart3,
  X,
  Grid3X3,
  Clock
} from 'lucide-react';

export function Sidebar() {
  const { state, setCurrentBoard, getCurrentBoard, updateUserPreferences, getVisibleBoards } = useDatabaseApp();
  const { user } = useAuth();
  const { isMobile } = useResponsive();
  const { userPreferences } = state;
  const currentBoard = getCurrentBoard();
  const [showBoardManager, setShowBoardManager] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);

  // Get only boards visible to the current user based on their role
  const visibleBoards = getVisibleBoards();

  // Remove duplicates and ensure unique IDs
  const uniqueBoards = visibleBoards.filter((board, index, self) =>
    index === self.findIndex(b => b.id === board.id)
  );

  const favoriteBoards = uniqueBoards.filter(board => board.isFavorite);
  const recentBoards = uniqueBoards
    .filter(board => !board.isFavorite)
    .sort((a, b) => {
      const dateA = new Date(a.updatedAt).getTime();
      const dateB = new Date(b.updatedAt).getTime();
      return dateB - dateA;
    })
    .slice(0, 5);

  const handleSelectBoard = (board: any) => {
    setCurrentBoard(board.id);
  };

  const toggleSidebar = () => {
    // This will be handled by the parent component or context
  };

  return (
    <>
      {/* Sidebar */}
      <div className={`${isMobile ? 'w-full' : 'w-64'} h-full bg-white shadow-xl flex-shrink-0`}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-blue-600 to-purple-600">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-blue-600" />
              </div>
              <span className="text-white font-bold text-lg">SapiaFlow</span>
            </div>
            <button
              onClick={() => updateUserPreferences({ sidebarOpen: false })}
              className="text-white hover:bg-white hover:bg-opacity-20 p-1.5 rounded-md transition-colors"
              title="Ocultar sidebar"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* User Profile */}
          <div className={`${isMobile ? 'p-3' : 'p-4'} border-b`}>
            <div className="flex items-center space-x-3">
              <img
                src={user?.avatar || 'https://ui-avatars.com/api/?name=User&background=3b82f6&color=fff&size=128'}
                alt={user?.name || 'Usuario'}
                className={`${isMobile ? 'w-8 h-8' : 'w-10 h-10'} rounded-full object-cover`}
              />
              <div className="min-w-0 flex-1">
                <p className={`font-medium text-gray-900 ${isMobile ? 'text-sm' : ''} truncate`}>
                  {user?.name || 'Usuario'}
                </p>
                {!isMobile && (
                  <p className="text-sm text-gray-500 truncate">{user?.email || '<EMAIL>'}</p>
                )}
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex-1 overflow-y-auto">
            <nav className={`${isMobile ? 'p-3' : 'p-4'} space-y-2`}>
              <button
                onClick={() => setShowBoardManager(true)}
                className={`w-full flex items-center space-x-3 ${isMobile ? 'p-3' : 'p-2'} rounded-lg hover:bg-gray-100 text-gray-700 min-h-[44px]`}
              >
                <Grid3X3 className="w-5 h-5 flex-shrink-0" />
                <span className={`${isMobile ? 'text-sm' : ''}`}>Todos los Tableros</span>
              </button>

              <button
                onClick={() => setShowCalendar(true)}
                className={`w-full flex items-center space-x-3 ${isMobile ? 'p-3' : 'p-2'} rounded-lg hover:bg-gray-100 text-gray-700 min-h-[44px]`}
              >
                <Calendar className="w-5 h-5 flex-shrink-0" />
                <span className={`${isMobile ? 'text-sm' : ''}`}>Calendario</span>
              </button>
              <button className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 text-gray-700">
                <Star className="w-5 h-5" />
                <span>Favoritos</span>
              </button>
              <button className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 text-gray-700">
                <Archive className="w-5 h-5" />
                <span>Archivo</span>
              </button>
            </nav>

            {/* Favorite Boards */}
            {favoriteBoards.length > 0 && (
              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Favoritos</h3>
                  <Star className="w-4 h-4 text-yellow-400" />
                </div>

                <div className="space-y-1">
                  {favoriteBoards.map((board) => (
                    <button
                      key={board.id}
                      onClick={() => handleSelectBoard(board)}
                      className={`w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer group text-left ${
                        currentBoard?.id === board.id ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                      }`}
                    >
                      <div className={`w-6 h-4 rounded-sm ${board.background}`} />
                      <span className="text-sm text-gray-700 flex-1 truncate">{board.title}</span>
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Recent Boards */}
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-semibold text-gray-600 uppercase tracking-wide">
                  {favoriteBoards.length > 0 ? 'Recientes' : 'Tus Tableros'}
                </h3>
                <button
                  onClick={() => setShowBoardManager(true)}
                  className="p-1 hover:bg-gray-100 rounded"
                  title="Gestionar tableros"
                >
                  <Plus className="w-4 h-4 text-gray-500" />
                </button>
              </div>

              <div className="space-y-1">
                {recentBoards.length > 0 ? (
                  recentBoards.map((board) => (
                    <button
                      key={board.id}
                      onClick={() => handleSelectBoard(board)}
                      className={`w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer group text-left ${
                        currentBoard?.id === board.id ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                      }`}
                    >
                      <div className={`w-6 h-4 rounded-sm ${board.background}`} />
                      <span className="text-sm text-gray-700 flex-1 truncate">{board.title}</span>
                      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100">
                        <Clock className="w-3 h-3 text-gray-400" />
                      </div>
                    </button>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500 mb-2">No hay tableros</p>
                    <button
                      onClick={() => setShowBoardManager(true)}
                      className="text-xs text-blue-600 hover:text-blue-700"
                    >
                      Crear tu primer tablero
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Mini Calendar */}
            <div className="p-4">
              <MiniCalendar
                onOpenFullCalendar={() => setShowCalendar(true)}
                onDateSelect={(date) => {
                  // Could implement date filtering here
                  setShowCalendar(true);
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Board Manager Modal */}
      <BoardManager
        isOpen={showBoardManager}
        onClose={() => setShowBoardManager(false)}
      />

      {/* Calendar Modal */}
      {showCalendar && (
        <FullCalendar
          onClose={() => setShowCalendar(false)}
        />
      )}
    </>
  );
}