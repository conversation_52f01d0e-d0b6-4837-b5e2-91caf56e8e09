// Centralized API configuration for dynamic URL handling
// Supports both local development and tunnel environments

/**
 * Detects the appropriate API base URL based on the current environment
 * @returns The base URL for API requests
 */
export const getApiBaseUrl = (): string => {
  // Priority 1: Check for environment variable first (useful for dev tunnels)
  if (typeof import.meta !== 'undefined' && import.meta.env?.VITE_API_URL) {
    console.log('🔧 Using VITE_API_URL from environment:', import.meta.env.VITE_API_URL);
    return import.meta.env.VITE_API_URL;
  }

  // Priority 2: Check if we're running in a tunnel environment
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    const protocol = window.location.protocol;

    // Tunnel environments detection
    if (hostname.includes('devtunnels.ms') ||
        hostname.includes('ngrok.io') ||
        hostname.includes('tunnels.dev') ||
        hostname.includes('localhost.run') ||
        hostname.includes('serveo.net')) {

      // For devtunnels.ms format: 607q6b85-5173.brs.devtunnels.ms -> 607q6b85-3001.brs.devtunnels.ms
      if (hostname.includes('devtunnels.ms')) {
        const tunnelBase = hostname.replace('-5173', '-3001');
        const apiUrl = `${protocol}//${tunnelBase}`;
        console.log('🔧 Using dev tunnel API URL:', apiUrl);
        return apiUrl;
      }

      // For other tunnel services, try to construct the backend URL
      // This is a fallback that might need adjustment based on the tunnel service
      const tunnelBase = hostname.replace('5173', '3001');
      const apiUrl = `${protocol}//${tunnelBase}`;
      console.log('🔧 Using tunnel API URL:', apiUrl);
      return apiUrl;
    }
  }

  // Priority 3: For local development
  console.log('🔧 Using default localhost API URL');
  return 'http://localhost:3002';
};

/**
 * Gets the full API base URL with /api path
 * @returns The complete API base URL
 */
export const getApiUrl = (): string => {
  return `${getApiBaseUrl()}/api`;
};

/**
 * Configuration object for API settings
 */
export const API_CONFIG = {
  get baseUrl() {
    return getApiBaseUrl();
  },
  get apiUrl() {
    return getApiUrl();
  },
  get healthUrl() {
    return `${getApiBaseUrl()}/health`;
  },
  // Default headers for API requests
  defaultHeaders: {
    'Content-Type': 'application/json',
  },
  // Request timeout in milliseconds
  timeout: 10000,
};

/**
 * Helper function to construct API endpoint URLs
 * @param endpoint - The endpoint path (e.g., '/users', '/teams')
 * @returns Complete URL for the endpoint
 */
export const getApiEndpoint = (endpoint: string): string => {
  // Ensure endpoint starts with /
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${getApiUrl()}${cleanEndpoint}`;
};

/**
 * Helper function for making API requests with proper error handling
 * @param endpoint - The API endpoint
 * @param options - Fetch options
 * @returns Promise with the response
 */
export const apiRequest = async (endpoint: string, options: RequestInit = {}): Promise<Response> => {
  const url = getApiEndpoint(endpoint);
  
  const config: RequestInit = {
    ...options,
    headers: {
      ...API_CONFIG.defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }
    
    return response;
  } catch (error) {
    console.error(`API request to ${url} failed:`, error);
    throw error;
  }
};

/**
 * Debug function to log current API configuration
 */
export const debugApiConfig = (): void => {
  console.log('🔧 API Configuration Debug:', {
    hostname: typeof window !== 'undefined' ? window.location.hostname : 'N/A',
    protocol: typeof window !== 'undefined' ? window.location.protocol : 'N/A',
    baseUrl: getApiBaseUrl(),
    apiUrl: getApiUrl(),
    healthUrl: API_CONFIG.healthUrl,
    isTunnel: typeof window !== 'undefined' && (
      window.location.hostname.includes('devtunnels.ms') ||
      window.location.hostname.includes('ngrok.io') ||
      window.location.hostname.includes('tunnels.dev')
    ),
  });
};

// Export the configuration as default
export default API_CONFIG;
