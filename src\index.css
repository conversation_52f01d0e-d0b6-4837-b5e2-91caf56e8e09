@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

@keyframes pulse-blue {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  }
}

.animate-pulse-blue {
  animation: pulse-blue 2s infinite;
}

/* Card layout improvements */
.card-container {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.card-title {
  word-break: break-word;
  overflow-wrap: break-word;
  line-height: 1.3;
}

.card-description {
  word-break: break-word;
  overflow-wrap: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Ensure proper text wrapping in cards */
.break-words {
  word-break: break-word;
  overflow-wrap: break-word;
}

/* Improve responsive layout */
@media (max-width: 640px) {
  .card-mobile {
    font-size: 0.75rem;
  }

  .card-mobile .card-title {
    font-size: 0.75rem;
    line-height: 1.2;
  }

  .card-mobile .card-description {
    font-size: 0.7rem;
    line-height: 1.2;
  }
}

/* List and board layout improvements */
.board-container {
  overflow-x: auto;
  overflow-y: hidden;
}

.list-container {
  flex-shrink: 0;
  max-width: 100%;
}

.card-stats {
  flex-wrap: wrap;
  gap: 0.25rem;
}

/* Prevent text overflow in small containers */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Ensure proper spacing and alignment */
.flex-container {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

@media (max-width: 768px) {
  .flex-container {
    gap: 0.75rem;
  }
}

/* Modal positioning improvements */
.modal-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 0.5rem;
  z-index: 50;
  width: 100%;
  max-width: 24rem;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  min-width: 280px;
}

/* Prevent modal overflow */
.modal-dropdown:before {
  content: '';
  position: absolute;
  top: -0.5rem;
  right: 1rem;
  width: 0;
  height: 0;
  border-left: 0.5rem solid transparent;
  border-right: 0.5rem solid transparent;
  border-bottom: 0.5rem solid white;
}

/* Sidebar actions improvements */
.sidebar-actions {
  position: relative;
  overflow: visible;
  contain: layout;
}

.sidebar-actions .relative {
  position: relative;
  overflow: visible;
}

/* Ensure modals stay within bounds */
.sidebar-actions .absolute {
  position: absolute;
  left: 0;
  right: auto;
  width: 100%;
  max-width: 100%;
}

/* Mobile modal adjustments */
@media (max-width: 640px) {
  .modal-dropdown {
    position: fixed;
    top: 50%;
    left: 50%;
    right: auto;
    transform: translate(-50%, -50%);
    max-width: calc(100vw - 1rem);
    max-height: calc(100vh - 2rem);
    margin: 0;
  }

  .modal-dropdown:before {
    display: none;
  }
}
