async function testDebug() {
  try {
    console.log('🐛 Testing debug endpoint...');
    
    const response = await fetch('http://localhost:3001/api/debug/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    console.log('📊 Response status:', response.status);
    const data = await response.json();
    console.log('📊 Response data:', JSON.stringify(data, null, 2));

  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

testDebug();
