import React, { useMemo } from 'react';
import { Card as CardType } from '../types';
import { Clock, Users, Tag } from 'lucide-react';

interface CalendarWeekViewProps {
  currentDate: Date;
  filteredCards: (CardType & { boardTitle: string; listTitle: string })[];
  onCardClick: (card: CardType) => void;
}

export function CalendarWeekView({ currentDate, filteredCards, onCardClick }: CalendarWeekViewProps) {
  const weekDays = useMemo(() => {
    const startOfWeek = new Date(currentDate);
    const day = startOfWeek.getDay();
    startOfWeek.setDate(startOfWeek.getDate() - day);

    const days = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      
      const dayCards = filteredCards.filter(card => {
        const cardDate = new Date(card.dueDate!);
        return cardDate.toDateString() === date.toDateString();
      });

      days.push({
        date,
        cards: dayCards,
        isToday: date.toDateString() === new Date().toDateString()
      });
    }

    return days;
  }, [currentDate, filteredCards]);

  const hours = Array.from({ length: 24 }, (_, i) => i);
  const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];

  const getCardPosition = (card: CardType) => {
    const dueDate = new Date(card.dueDate!);
    const hour = dueDate.getHours();
    const minute = dueDate.getMinutes();
    return (hour * 60 + minute) / 60; // Position in hours
  };

  const getCardPriorityColor = (card: CardType) => {
    const now = new Date();
    const dueDate = new Date(card.dueDate!);
    const diffHours = (dueDate.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    if (diffHours < 0) return 'bg-red-500 border-red-600'; // Overdue
    if (diffHours < 24) return 'bg-orange-500 border-orange-600'; // Due today
    if (diffHours < 72) return 'bg-yellow-500 border-yellow-600'; // Due in 3 days
    return 'bg-blue-500 border-blue-600'; // Normal
  };

  return (
    <div className="flex flex-col h-full">
      {/* Week Header */}
      <div className="grid grid-cols-8 border-b">
        <div className="p-4 border-r bg-gray-50">
          <div className="text-sm font-medium text-gray-600">Hora</div>
        </div>
        {weekDays.map((day, index) => (
          <div
            key={index}
            className={`p-4 border-r text-center ${
              day.isToday ? 'bg-blue-50 border-blue-200' : 'bg-gray-50'
            }`}
          >
            <div className={`text-sm font-medium ${
              day.isToday ? 'text-blue-600' : 'text-gray-600'
            }`}>
              {dayNames[index]}
            </div>
            <div className={`text-lg font-bold ${
              day.isToday ? 'text-blue-600' : 'text-gray-900'
            }`}>
              {day.date.getDate()}
            </div>
            <div className="text-xs text-gray-500">
              {day.cards.length} tarea{day.cards.length !== 1 ? 's' : ''}
            </div>
          </div>
        ))}
      </div>

      {/* Week Grid */}
      <div className="flex-1 overflow-auto">
        <div className="grid grid-cols-8 min-h-full">
          {/* Time Column */}
          <div className="border-r bg-gray-50">
            {hours.map(hour => (
              <div key={hour} className="h-16 border-b border-gray-200 p-2 text-right">
                <div className="text-xs text-gray-500">
                  {hour.toString().padStart(2, '0')}:00
                </div>
              </div>
            ))}
          </div>

          {/* Day Columns */}
          {weekDays.map((day, dayIndex) => (
            <div key={dayIndex} className="border-r relative">
              {/* Hour Grid Lines */}
              {hours.map(hour => (
                <div key={hour} className="h-16 border-b border-gray-100" />
              ))}

              {/* Cards */}
              <div className="absolute inset-0 p-1">
                {day.cards.map(card => {
                  const position = getCardPosition(card);
                  const topPosition = (position / 24) * 100;
                  
                  return (
                    <div
                      key={card.id}
                      onClick={() => onCardClick(card)}
                      className={`absolute left-1 right-1 p-2 rounded-lg border-l-4 cursor-pointer hover:shadow-md transition-all duration-200 text-white text-xs ${getCardPriorityColor(card)}`}
                      style={{
                        top: `${topPosition}%`,
                        minHeight: '40px',
                        zIndex: 10
                      }}
                      title={`${card.title} - ${card.boardTitle} > ${card.listTitle}`}
                    >
                      <div className="font-medium truncate mb-1">
                        {card.title}
                      </div>
                      
                      <div className="flex items-center space-x-1 text-xs opacity-90">
                        <Clock className="w-3 h-3" />
                        <span>
                          {new Date(card.dueDate!).toLocaleTimeString('es-ES', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </span>
                      </div>

                      {/* Labels */}
                      {card.labels.length > 0 && (
                        <div className="flex items-center space-x-1 mt-1">
                          <Tag className="w-3 h-3" />
                          <div className="flex space-x-1">
                            {card.labels.slice(0, 2).map(label => (
                              <div
                                key={label.id}
                                className="w-2 h-2 rounded-full"
                                style={{ backgroundColor: label.color }}
                                title={label.name}
                              />
                            ))}
                            {card.labels.length > 2 && (
                              <span className="text-xs">+{card.labels.length - 2}</span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Assigned Members */}
                      {card.assignedMembers.length > 0 && (
                        <div className="flex items-center space-x-1 mt-1">
                          <Users className="w-3 h-3" />
                          <div className="flex -space-x-1">
                            {card.assignedMembers.slice(0, 2).map(member => (
                              <img
                                key={member.id}
                                src={member.avatar}
                                alt={member.name}
                                className="w-4 h-4 rounded-full border border-white"
                                title={member.name}
                              />
                            ))}
                            {card.assignedMembers.length > 2 && (
                              <div className="w-4 h-4 rounded-full bg-gray-600 border border-white flex items-center justify-center text-xs">
                                +{card.assignedMembers.length - 2}
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Board and List Info */}
                      <div className="text-xs opacity-75 mt-1 truncate">
                        {card.boardTitle} › {card.listTitle}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
