import React, { useState, useRef, useEffect, useMemo } from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { BoardMember } from '../types';
import { getAvailableUsersForMentions, extractMentionsFromText } from '../utils/mentionUtils';

interface MentionInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  multiline?: boolean;
  rows?: number;
  disabled?: boolean;
  autoFocus?: boolean;
  onKeyDown?: (e: React.KeyboardEvent) => void;
}

interface MentionSuggestion {
  member: BoardMember;
  startIndex: number;
  query: string;
}

export function MentionInput({
  value,
  onChange,
  placeholder = "Escribe aquí...",
  className = "",
  multiline = false,
  rows = 3,
  disabled = false,
  autoFocus = false,
  onKeyDown
}: MentionInputProps) {
  const { getCurrentBoard, state, syncTeamMembersToBoard } = useDatabaseApp();
  const currentBoard = getCurrentBoard();
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<BoardMember[]>([]);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  const [mentionQuery, setMentionQuery] = useState<MentionSuggestion | null>(null);
  const inputRef = useRef<HTMLTextAreaElement | HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Calculate available users with proper memoization
  const [availableUsers, setAvailableUsers] = useState<BoardMember[]>([]);

  useEffect(() => {
    const users = getAvailableUsersForMentions(currentBoard, state.teams);

    console.log('🔄 MentionInput - Updating availableUsers:', {
      currentBoard: currentBoard ? {
        id: currentBoard.id,
        title: currentBoard.title,
        teamId: currentBoard.teamId,
        membersCount: currentBoard.members?.length || 0,
        memberNames: currentBoard.members?.map(m => m.name) || []
      } : null,
      teamsCount: state.teams?.length || 0,
      teamDetails: state.teams?.map(t => ({
        id: t.id,
        name: t.name,
        memberCount: t.members?.length || 0,
        memberUserIds: t.members?.map(m => m.userId) || []
      })) || [],
      calculatedUsers: users.length,
      userNames: users.map(u => u.name)
    });

    // Auto-sync team members if board has team but no team members in board
    if (currentBoard && currentBoard.teamId && users.length === 0) {
      const team = state.teams?.find(t => t.id === currentBoard.teamId);
      if (team && team.members && team.members.length > 0) {
        console.log('🔄 Auto-syncing team members to board (no team members found in board)');
        syncTeamMembersToBoard(currentBoard.id).catch(error => {
          console.error('❌ Error auto-syncing team members:', error);
        });
      }
    }

    setAvailableUsers(users);
  }, [
    currentBoard?.id,
    currentBoard?.teamId,
    JSON.stringify(currentBoard?.members?.map(m => m.id) || []),
    JSON.stringify(state.teams?.map(t => ({ id: t.id, memberIds: t.members?.map(m => m.userId) })) || []),
    syncTeamMembersToBoard
  ]);



  // Detectar menciones mientras se escribe
  useEffect(() => {
    const cursorPosition = inputRef.current?.selectionStart || 0;
    const textBeforeCursor = value.substring(0, cursorPosition);
    const mentionMatch = textBeforeCursor.match(/@(\w*)$/);

    if (mentionMatch) {
      const query = mentionMatch[1].toLowerCase();
      const startIndex = cursorPosition - mentionMatch[0].length;

      const filteredMembers = availableUsers.filter(member =>
        member.name.toLowerCase().includes(query) ||
        member.email.toLowerCase().includes(query)
      );

      if (filteredMembers.length > 0) {
        setSuggestions(filteredMembers);
        setMentionQuery({
          member: filteredMembers[0], // placeholder
          startIndex,
          query: mentionMatch[0]
        });
        setShowSuggestions(true);
        setSelectedSuggestion(0);
      } else {
        setShowSuggestions(false);
      }
    } else {
      setShowSuggestions(false);
      setMentionQuery(null);
    }
  }, [value, availableUsers]);

  const insertMention = (member: BoardMember) => {
    if (!mentionQuery || !inputRef.current) return;

    const cursorPosition = inputRef.current.selectionStart || 0;
    const beforeMention = value.substring(0, mentionQuery.startIndex);
    const afterMention = value.substring(cursorPosition);
    const mentionText = `@${member.name}`;
    
    const newValue = beforeMention + mentionText + afterMention;
    onChange(newValue);
    
    setShowSuggestions(false);
    setMentionQuery(null);
    
    // Posicionar cursor después de la mención
    setTimeout(() => {
      if (inputRef.current) {
        const newCursorPosition = mentionQuery.startIndex + mentionText.length;
        inputRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
        inputRef.current.focus();
      }
    }, 0);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (showSuggestions) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedSuggestion(prev => 
            prev < suggestions.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedSuggestion(prev => 
            prev > 0 ? prev - 1 : suggestions.length - 1
          );
          break;
        case 'Enter':
        case 'Tab':
          e.preventDefault();
          if (suggestions[selectedSuggestion]) {
            insertMention(suggestions[selectedSuggestion]);
          }
          break;
        case 'Escape':
          e.preventDefault();
          setShowSuggestions(false);
          setMentionQuery(null);
          break;
      }
    }
    
    if (onKeyDown) {
      onKeyDown(e);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    onChange(e.target.value);
  };

  const renderFormattedText = (text: string) => {
    // Resaltar menciones en el texto
    const mentionRegex = /@(\w+)/g;
    const parts = text.split(mentionRegex);

    return parts.map((part, index) => {
      if (index % 2 === 1) {
        // Es una mención
        const member = availableUsers.find(m => m.name === part);
        return (
          <span
            key={index}
            className="bg-blue-100 text-blue-800 px-1 rounded font-medium"
            title={member ? `${member.name} (${member.email})` : part}
          >
            @{part}
          </span>
        );
      }
      return part;
    });
  };

  const InputComponent = multiline ? 'textarea' : 'input';

  return (
    <div className="relative">
      <InputComponent
        ref={inputRef as any}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        autoFocus={autoFocus}
        rows={multiline ? rows : undefined}
        className={`w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${className}`}
      />

      {/* Suggestions Dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto"
        >
          <div className="p-2 text-xs text-gray-500 border-b">
            Mencionar miembro
          </div>
          {suggestions.map((member, index) => (
            <div
              key={member.id}
              onClick={() => insertMention(member)}
              className={`flex items-center space-x-3 p-3 cursor-pointer transition-colors ${
                index === selectedSuggestion
                  ? 'bg-blue-50 border-l-4 border-blue-500'
                  : 'hover:bg-gray-50'
              }`}
            >
              <img
                src={member.avatar}
                alt={member.name}
                className="w-8 h-8 rounded-full object-cover"
              />
              <div className="flex-1">
                <div className="font-medium text-gray-900">{member.name}</div>
                <div className="text-sm text-gray-500">{member.email}</div>
              </div>
              <div className="text-xs text-gray-400">
                {member.role === 'admin' ? '👑' : member.role === 'member' ? '🛡️' : '👁️'}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Preview de menciones (opcional) */}
      {value && !showSuggestions && (
        <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
          <div className="text-gray-600 mb-1">Vista previa:</div>
          <div>{renderFormattedText(value)}</div>
        </div>
      )}
    </div>
  );
}

// Hook para extraer menciones de un texto
export function useMentions(text: string) {
  const { getCurrentBoard, state } = useDatabaseApp();
  const currentBoard = getCurrentBoard();

  const availableUsers = getAvailableUsersForMentions(currentBoard, state.teams);
  const mentionedMembers = extractMentionsFromText(text, availableUsers);

  return {
    mentionedMembers,
    hasMentions: mentionedMembers.length > 0,
    extractMentions: (text: string) => extractMentionsFromText(text, availableUsers),
    availableUsers // Export available users for other components that might need it
  };
}
