import React, { useState } from 'react';
import { User } from '../types';
import { 
  BarChart3, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  Target,
  Calendar,
  Award,
  Activity,
  Users,
  Filter
} from 'lucide-react';

interface UserMetricsProps {
  users: User[];
}

export function UserMetrics({ users }: UserMetricsProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter'>('month');
  const [sortBy, setSortBy] = useState<'score' | 'tasks' | 'efficiency'>('score');

  // Calculate overall metrics
  const totalUsers = users.length;
  const activeUsers = users.filter(u => u.isActive !== false).length;
  const totalTasks = users.reduce((sum, user) => sum + (user.metrics?.totalTasks || 0), 0);
  const completedTasks = users.reduce((sum, user) => sum + (user.metrics?.tasksCompleted || 0), 0);
  const averageScore = users.length > 0 
    ? Math.round(users.reduce((sum, user) => sum + (user.metrics?.score || 0), 0) / users.length)
    : 0;

  // Sort users based on selected criteria
  const sortedUsers = [...users].sort((a, b) => {
    switch (sortBy) {
      case 'score':
        return (b.metrics?.score || 0) - (a.metrics?.score || 0);
      case 'tasks':
        return (b.metrics?.tasksCompleted || 0) - (a.metrics?.tasksCompleted || 0);
      case 'efficiency':
        const aEfficiency = a.metrics ? (a.metrics.tasksOnTime / Math.max(a.metrics.tasksCompleted, 1)) : 0;
        const bEfficiency = b.metrics ? (b.metrics.tasksOnTime / Math.max(b.metrics.tasksCompleted, 1)) : 0;
        return bEfficiency - aEfficiency;
      default:
        return 0;
    }
  });

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getEfficiencyColor = (efficiency: number) => {
    if (efficiency >= 0.8) return 'text-green-600';
    if (efficiency >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatHours = (hours: number) => {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    if (hours < 24) return `${Math.round(hours)}h`;
    return `${Math.round(hours / 24)}d`;
  };

  const getTopPerformers = () => {
    return sortedUsers.slice(0, 3).filter(user => user.metrics?.score && user.metrics.score > 0);
  };

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Métricas de Rendimiento de Usuarios</h3>
          <p className="text-sm text-gray-600">Rastrea la productividad y rendimiento de usuarios en la plataforma</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Calendar className="w-4 h-4 text-gray-500" />
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as any)}
              className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="week">Última Semana</option>
              <option value="month">Último Mes</option>
              <option value="quarter">Último Trimestre</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="score">Ordenar por Puntuación</option>
              <option value="tasks">Ordenar por Tareas</option>
              <option value="efficiency">Ordenar por Eficiencia</option>
            </select>
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-blue-50 p-6 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">Total de Usuarios</p>
              <p className="text-3xl font-bold text-blue-900">{totalUsers}</p>
              <p className="text-sm text-blue-700">{activeUsers} activos</p>
            </div>
            <Users className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-green-50 p-6 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600">Tareas Completadas</p>
              <p className="text-3xl font-bold text-green-900">{completedTasks}</p>
              <p className="text-sm text-green-700">de {totalTasks} total</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-purple-50 p-6 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600">Puntuación Promedio</p>
              <p className="text-3xl font-bold text-purple-900">{averageScore}</p>
              <p className="text-sm text-purple-700">de 100</p>
            </div>
            <Award className="w-8 h-8 text-purple-600" />
          </div>
        </div>

        <div className="bg-orange-50 p-6 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-orange-600">Tasa de Finalización</p>
              <p className="text-3xl font-bold text-orange-900">
                {totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0}%
              </p>
              <p className="text-sm text-orange-700">general</p>
            </div>
            <Target className="w-8 h-8 text-orange-600" />
          </div>
        </div>
      </div>

      {/* Top Performers */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center mb-4">
          <Award className="w-5 h-5 text-yellow-500 mr-2" />
          <h4 className="text-lg font-semibold text-gray-900">Mejores Desempeños</h4>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {getTopPerformers().map((user, index) => (
            <div key={user.id} className="relative">
              <div className={`p-4 rounded-lg border-2 ${
                index === 0 ? 'border-yellow-300 bg-yellow-50' :
                index === 1 ? 'border-gray-300 bg-gray-50' :
                'border-orange-300 bg-orange-50'
              }`}>
                <div className="flex items-center mb-3">
                  <div className="flex-shrink-0 h-10 w-10">
                    <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                      <span className="text-white font-medium text-sm">
                        {user.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  </div>
                  <div className="ml-3">
                    <p className="font-medium text-gray-900">{user.name}</p>
                    <p className="text-sm text-gray-500">{user.role}</p>
                  </div>
                  <div className={`ml-auto text-2xl ${
                    index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'
                  }`}>
                  </div>
                </div>
                
                {user.metrics && (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Puntuación</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(user.metrics.score)}`}>
                        {user.metrics.score}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Tareas</span>
                      <span className="text-sm font-medium text-gray-900">
                        {user.metrics.tasksCompleted}/{user.metrics.totalTasks}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">A Tiempo</span>
                      <span className={`text-sm font-medium ${getEfficiencyColor(user.metrics.tasksOnTime / Math.max(user.metrics.tasksCompleted, 1))}`}>
                        {Math.round((user.metrics.tasksOnTime / Math.max(user.metrics.tasksCompleted, 1)) * 100)}%
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Detailed User Metrics Table */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center">
            <BarChart3 className="w-5 h-5 text-blue-600 mr-2" />
            <h4 className="text-lg font-semibold text-gray-900">Métricas Detalladas de Rendimiento</h4>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usuario
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Puntuación
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tareas Completadas
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tasa A Tiempo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tiempo Prom. Finalización
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actividad Reciente
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedUsers.map((user) => {
                const efficiency = user.metrics 
                  ? (user.metrics.tasksOnTime / Math.max(user.metrics.tasksCompleted, 1))
                  : 0;
                
                return (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                            <span className="text-white font-medium text-xs">
                              {user.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">{user.name}</div>
                          <div className="text-sm text-gray-500">{user.role}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.metrics ? (
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getScoreColor(user.metrics.score)}`}>
                          {user.metrics.score}
                        </span>
                      ) : (
                        <span className="text-gray-400 text-sm">Sin datos</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.metrics ? (
                        <div className="text-sm text-gray-900">
                          <div className="font-medium">{user.metrics.tasksCompleted}</div>
                          <div className="text-gray-500">de {user.metrics.totalTasks}</div>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">Sin datos</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.metrics ? (
                        <div className="flex items-center">
                          <div className="flex-1">
                            <div className={`text-sm font-medium ${getEfficiencyColor(efficiency)}`}>
                              {Math.round(efficiency * 100)}%
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                              <div 
                                className={`h-1.5 rounded-full ${
                                  efficiency >= 0.8 ? 'bg-green-500' :
                                  efficiency >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${Math.min(efficiency * 100, 100)}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">Sin datos</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.metrics ? (
                        <div className="flex items-center text-sm text-gray-900">
                          <Clock className="w-3 h-3 mr-1 text-gray-400" />
                          {formatHours(user.metrics.averageCompletionTime)}
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">Sin datos</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.metrics && user.metrics.recentActivity.length > 0 ? (
                        <div className="flex items-center text-sm text-gray-500">
                          <Activity className="w-3 h-3 mr-1" />
                          {user.metrics.recentActivity.length} recent
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">Sin actividad</span>
                      )}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
