import React from 'react';
import { Shield, Users, Plus } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useUserPermissions } from '../hooks/useUserPermissions';

interface NoAccessMessageProps {
  onCreateBoard?: () => void;
  onRequestAccess?: () => void;
}

export function NoAccessMessage({ onCreateBoard, onRequestAccess }: NoAccessMessageProps) {
  const { user } = useAuth();
  const { isAdmin, canCreateBoards } = useUserPermissions();

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="max-w-md w-full mx-4">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            {isAdmin() ? (
              <Plus className="w-8 h-8 text-gray-400" />
            ) : (
              <Shield className="w-8 h-8 text-gray-400" />
            )}
          </div>

          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {isAdmin() ? 'No hay tableros disponibles' : 'Sin acceso a tableros'}
          </h2>

          <p className="text-gray-600 mb-6">
            {isAdmin() ? (
              'Como administrador, puedes crear un nuevo tablero para comenzar a organizar tu trabajo.'
            ) : (
              'No tienes acceso a ningún tablero en este momento. Contacta con un administrador para que te agregue como miembro a un tablero.'
            )}
          </p>

          <div className="space-y-3">
            {isAdmin() && canCreateBoards() && onCreateBoard && (
              <button
                onClick={onCreateBoard}
                className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-5 h-5 mr-2" />
                Crear Nuevo Tablero
              </button>
            )}

            {!isAdmin() && onRequestAccess && (
              <button
                onClick={onRequestAccess}
                className="w-full flex items-center justify-center px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <Users className="w-5 h-5 mr-2" />
                Solicitar Acceso
              </button>
            )}

            <div className="text-sm text-gray-500">
              <p className="mb-2">
                <strong>Tu rol actual:</strong> {user?.role === 'admin' ? 'Administrador' : user?.role === 'member' ? 'Miembro' : 'Observador'}
              </p>
              {!isAdmin() && (
                <p className="text-xs">
                  Los usuarios con rol de Miembro u Observador solo pueden ver tableros donde han sido agregados como miembros.
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Additional info for non-admin users */}
        {!isAdmin() && (
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <Shield className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
              <div className="text-sm">
                <h3 className="font-medium text-blue-900 mb-1">Restricciones de Acceso</h3>
                <p className="text-blue-700">
                  Por seguridad, solo puedes acceder a tableros donde eres miembro. 
                  Un administrador debe agregarte a un tablero para que puedas verlo y trabajar en él.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
