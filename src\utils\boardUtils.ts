import { Board, User } from '../types';

/**
 * Filter boards based on user role and membership
 * - ADMIN users: Can see all boards
 * - MEMBER/OBSERVER users: Only see boards where they are members
 */
export function getVisibleBoardsForUser(boards: Board[], user: User): Board[] {
  if (!user) return [];

  // ADMIN users can see all boards
  if (user.role === 'admin') {
    return boards;
  }

  // MEMBER and OBSERVER users can only see boards where they are members
  return boards.filter(board => {
    // Check if user is a member of this board
    return board.members?.some(member => member.id === user.id) || false;
  });
}

/**
 * Check if user has access to a specific board
 */
export function canUserAccessBoard(board: Board, user: User): boolean {
  if (!user || !board) return false;

  // ADMIN users can access all boards
  if (user.role === 'admin') {
    return true;
  }

  // MEMBER and OBSERVER users can only access boards where they are members
  return board.members?.some(member => member.id === user.id) || false;
}

/**
 * Get the first accessible board for a user
 */
export function getFirstAccessibleBoard(boards: Board[], user: User): Board | null {
  const visibleBoards = getVisibleBoardsForUser(boards, user);
  return visibleBoards.length > 0 ? visibleBoards[0] : null;
}

/**
 * Check if user can access user management functionality
 */
export function canUserManageUsers(user: User): boolean {
  return user?.role === 'admin';
}

/**
 * Check if user can access admin functionality
 */
export function canUserAccessAdmin(user: User): boolean {
  return user?.role === 'admin';
}
