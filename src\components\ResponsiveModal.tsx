import React, { useEffect } from 'react';
import { X } from 'lucide-react';
import { useResponsive } from '../hooks/useResponsive';

interface ResponsiveModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  showCloseButton?: boolean;
  className?: string;
}

export function ResponsiveModal({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  showCloseButton = true,
  className = '',
}: ResponsiveModalProps) {
  const { isMobile, isTablet } = useResponsive();

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const getSizeClasses = () => {
    if (isMobile) {
      return 'w-full h-full max-w-none max-h-none rounded-none';
    }

    if (isTablet) {
      switch (size) {
        case 'sm':
          return 'w-full max-w-md max-h-[90vh]';
        case 'md':
          return 'w-full max-w-2xl max-h-[90vh]';
        case 'lg':
          return 'w-full max-w-4xl max-h-[90vh]';
        case 'xl':
          return 'w-full max-w-6xl max-h-[90vh]';
        case 'full':
          return 'w-[95vw] h-[95vh]';
        default:
          return 'w-full max-w-2xl max-h-[90vh]';
      }
    }

    // Desktop
    switch (size) {
      case 'sm':
        return 'w-full max-w-md max-h-[90vh]';
      case 'md':
        return 'w-full max-w-2xl max-h-[90vh]';
      case 'lg':
        return 'w-full max-w-4xl max-h-[90vh]';
      case 'xl':
        return 'w-full max-w-6xl max-h-[90vh]';
      case 'full':
        return 'w-[95vw] h-[95vh]';
      default:
        return 'w-full max-w-2xl max-h-[90vh]';
    }
  };

  const getAnimationClasses = () => {
    if (isMobile) {
      return 'animate-in slide-in-from-bottom duration-300';
    }
    return 'animate-in fade-in zoom-in-95 duration-200';
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div
        className={`
          relative bg-white shadow-xl overflow-hidden
          ${getSizeClasses()}
          ${getAnimationClasses()}
          ${isMobile ? 'flex flex-col' : 'rounded-lg'}
          ${className}
        `}
      >
        {/* Header */}
        <div className={`flex items-center justify-between ${isMobile ? 'p-4' : 'p-6'} border-b border-gray-200 flex-shrink-0`}>
          <h2 className={`font-semibold text-gray-900 ${isMobile ? 'text-lg' : 'text-xl'} truncate pr-4`}>
            {title}
          </h2>
          {showCloseButton && (
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors flex-shrink-0 min-w-[44px] min-h-[44px] flex items-center justify-center"
              aria-label="Cerrar modal"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className={`flex-1 overflow-y-auto ${isMobile ? 'p-4' : 'p-6'}`}>
          {children}
        </div>
      </div>
    </div>
  );
}

// Hook for managing modal state
export function useModal(initialState = false) {
  const [isOpen, setIsOpen] = React.useState(initialState);

  const openModal = React.useCallback(() => setIsOpen(true), []);
  const closeModal = React.useCallback(() => setIsOpen(false), []);
  const toggleModal = React.useCallback(() => setIsOpen(prev => !prev), []);

  return {
    isOpen,
    openModal,
    closeModal,
    toggleModal,
  };
}
