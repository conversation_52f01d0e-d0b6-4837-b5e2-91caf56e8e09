import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Board, List, Card, User, Team, Activity, AppNotification, Label } from '../types';
import ApiService from '../services/ApiService';
import UserPreferencesService from '../services/UserPreferencesService';
import { getVisibleBoardsForUser } from '../utils/boardUtils';
import { useAuth } from './AuthContext';
import { getApiBaseUrl } from '../config/api';

interface AppState {
  boards: Board[];
  currentBoardId: string | null;
  users: User[];
  teams: Team[];
  activities: Activity[];
  notifications: AppNotification[];
  labels: Label[];
  searchQuery: string;
  cardFilter: 'all' | 'assigned' | 'created' | 'due-today' | 'due-soon' | 'overdue';
  userPreferences: {
    sidebarOpen: boolean;
    theme: 'light' | 'dark';
    language: 'es' | 'en';
    lastBoardId?: string;
  };
  loading: boolean;
  error: string | null;
}

type AppAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_BOARDS'; payload: Board[] }
  | { type: 'ADD_BOARD'; payload: Board }
  | { type: 'UPDATE_BOARD'; payload: Board }
  | { type: 'DELETE_BOARD'; payload: string }
  | { type: 'SET_CURRENT_BOARD_ID'; payload: string | null }
  | { type: 'SET_USERS'; payload: User[] }
  | { type: 'ADD_USER'; payload: User }
  | { type: 'UPDATE_USER'; payload: User }
  | { type: 'DELETE_USER'; payload: string }
  | { type: 'SET_TEAMS'; payload: Team[] }
  | { type: 'ADD_TEAM'; payload: Team }
  | { type: 'UPDATE_TEAM'; payload: Team }
  | { type: 'DELETE_TEAM'; payload: string }
  | { type: 'SET_ACTIVITIES'; payload: Activity[] }
  | { type: 'ADD_ACTIVITY'; payload: Activity }
  | { type: 'SET_NOTIFICATIONS'; payload: AppNotification[] }
  | { type: 'ADD_NOTIFICATION'; payload: AppNotification }
  | { type: 'UPDATE_NOTIFICATION'; payload: AppNotification }
  | { type: 'SET_LABELS'; payload: Label[] }
  | { type: 'ADD_LABEL'; payload: Label }
  | { type: 'SET_USER_PREFERENCES'; payload: Partial<AppState['userPreferences']> }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_CARD_FILTER'; payload: 'all' | 'assigned' | 'created' | 'due-today' | 'due-soon' | 'overdue' }
  | { type: 'ADD_LIST'; payload: { boardId: string; list: List } }
  | { type: 'UPDATE_LIST'; payload: { boardId: string; list: List } }
  | { type: 'DELETE_LIST'; payload: { boardId: string; listId: string } }
  | { type: 'ADD_CARD'; payload: { boardId: string; listId: string; card: Card } }
  | { type: 'UPDATE_CARD'; payload: { boardId: string; listId: string; card: Card } }
  | { type: 'DELETE_CARD'; payload: { boardId: string; listId: string; cardId: string } }
  | { type: 'MOVE_CARD'; payload: { boardId: string; cardId: string; sourceListId: string; targetListId: string; newPosition: number } };

const initialState: AppState = {
  boards: [],
  currentBoardId: null,
  users: [],
  teams: [],
  activities: [],
  notifications: [],
  labels: [],
  searchQuery: '',
  cardFilter: 'all',
  userPreferences: {
    sidebarOpen: false,
    theme: 'light',
    language: 'es',
    lastBoardId: undefined
  },
  loading: false,
  error: null
};

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    
    case 'SET_BOARDS':
      return { ...state, boards: action.payload };
    
    case 'ADD_BOARD':
      return { ...state, boards: [...state.boards, action.payload] };
    
    case 'UPDATE_BOARD':
      console.log('🔄 Reducer UPDATE_BOARD:', {
        boardId: action.payload.id,
        currentBoardId: state.currentBoardId,
        isCurrentBoard: action.payload.id === state.currentBoardId,
        membersCount: action.payload.members?.length,
        members: action.payload.members?.map(m => ({ name: m.name, role: m.role }))
      });
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.id ? action.payload : board
        )
      };
    
    case 'DELETE_BOARD':
      return {
        ...state,
        boards: state.boards.filter(board => board.id !== action.payload),
        currentBoardId: state.currentBoardId === action.payload ? null : state.currentBoardId
      };
    
    case 'SET_CURRENT_BOARD_ID':
      return { ...state, currentBoardId: action.payload };
    
    case 'SET_USERS':
      return { ...state, users: action.payload };
    
    case 'ADD_USER':
      return { ...state, users: [...state.users, action.payload] };
    
    case 'UPDATE_USER':
      return {
        ...state,
        users: state.users.map(user =>
          user.id === action.payload.id ? action.payload : user
        )
      };
    
    case 'DELETE_USER':
      return {
        ...state,
        users: state.users.filter(user => user.id !== action.payload)
      };
    
    case 'SET_TEAMS':
      return { ...state, teams: action.payload };
    
    case 'ADD_TEAM':
      return { ...state, teams: [...state.teams, action.payload] };
    
    case 'UPDATE_TEAM':
      return {
        ...state,
        teams: state.teams.map(team =>
          team.id === action.payload.id ? action.payload : team
        )
      };
    
    case 'DELETE_TEAM':
      return {
        ...state,
        teams: state.teams.filter(team => team.id !== action.payload)
      };
    
    case 'SET_ACTIVITIES':
      return { ...state, activities: action.payload };
    
    case 'ADD_ACTIVITY':
      return { ...state, activities: [action.payload, ...state.activities] };
    
    case 'SET_NOTIFICATIONS':
      return { ...state, notifications: action.payload };
    
    case 'ADD_NOTIFICATION':
      return { ...state, notifications: [action.payload, ...state.notifications] };
    
    case 'UPDATE_NOTIFICATION':
      return {
        ...state,
        notifications: state.notifications.map(notification =>
          notification.id === action.payload.id ? action.payload : notification
        )
      };
    
    case 'SET_LABELS':
      return { ...state, labels: action.payload };

    case 'ADD_LABEL':
      return { ...state, labels: [...state.labels, action.payload] };
    
    case 'SET_USER_PREFERENCES':
      return {
        ...state,
        userPreferences: { ...state.userPreferences, ...action.payload }
      };

    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload };

    case 'SET_CARD_FILTER':
      return { ...state, cardFilter: action.payload };
    
    case 'ADD_LIST':
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.boardId
            ? { ...board, lists: [...board.lists, action.payload.list] }
            : board
        )
      };
    
    case 'UPDATE_LIST':
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.boardId
            ? {
                ...board,
                lists: board.lists.map(list =>
                  list.id === action.payload.list.id ? action.payload.list : list
                )
              }
            : board
        )
      };
    
    case 'DELETE_LIST':
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.boardId
            ? {
                ...board,
                lists: board.lists.filter(list => list.id !== action.payload.listId)
              }
            : board
        )
      };
    
    case 'ADD_CARD':
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.boardId
            ? {
                ...board,
                lists: board.lists.map(list =>
                  list.id === action.payload.listId
                    ? { ...list, cards: [...list.cards, action.payload.card] }
                    : list
                )
              }
            : board
        )
      };
    
    case 'UPDATE_CARD':
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.boardId
            ? {
                ...board,
                lists: board.lists.map(list =>
                  list.id === action.payload.listId
                    ? {
                        ...list,
                        cards: list.cards.map(card =>
                          card.id === action.payload.card.id ? action.payload.card : card
                        )
                      }
                    : list
                )
              }
            : board
        )
      };
    
    case 'DELETE_CARD':
      return {
        ...state,
        boards: state.boards.map(board =>
          board.id === action.payload.boardId
            ? {
                ...board,
                lists: board.lists.map(list =>
                  list.id === action.payload.listId
                    ? {
                        ...list,
                        cards: list.cards.filter(card => card.id !== action.payload.cardId)
                      }
                    : list
                )
              }
            : board
        )
      };
    
    case 'MOVE_CARD':
      return {
        ...state,
        boards: state.boards.map(board => {
          if (board.id !== action.payload.boardId) return board;
          
          const { cardId, sourceListId, targetListId, newPosition } = action.payload;
          
          // Find the card to move
          let cardToMove: Card | null = null;
          const updatedLists = board.lists.map(list => {
            if (list.id === sourceListId) {
              const cardIndex = list.cards.findIndex(card => card.id === cardId);
              if (cardIndex !== -1) {
                cardToMove = list.cards[cardIndex];
                return {
                  ...list,
                  cards: list.cards.filter(card => card.id !== cardId)
                };
              }
            }
            return list;
          });
          
          if (!cardToMove) return board;
          
          // Add card to target list
          const finalLists = updatedLists.map(list => {
            if (list.id === targetListId) {
              const newCards = [...list.cards];
              newCards.splice(newPosition, 0, { ...cardToMove!, position: newPosition });
              return { ...list, cards: newCards };
            }
            return list;
          });
          
          return { ...board, lists: finalLists };
        })
      };
    
    default:
      return state;
  }
}

interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  
  // Data loading functions
  loadAllData: () => Promise<void>;
  loadBoards: () => Promise<void>;
  loadUsers: () => Promise<void>;
  loadTeams: () => Promise<void>;
  
  // Board operations
  createBoard: (boardData: Omit<Board, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Board>;
  updateBoard: (id: string, updates: Partial<Board>) => Promise<void>;
  deleteBoard: (id: string) => Promise<void>;
  addMemberToBoard: (boardId: string, userId: string, role?: string) => Promise<void>;
  removeMemberFromBoard: (boardId: string, userId: string) => Promise<void>;
  updateMemberRole: (boardId: string, userId: string, role: string) => Promise<void>;
  setCurrentBoard: (boardId: string | null) => void;
  getCurrentBoard: () => Board | null;
  syncTeamMembersToBoard: (boardId: string) => Promise<void>;
  
  // User operations
  createUser: (userData: any) => Promise<User>;
  updateUser: (id: string, updates: any) => Promise<void>;
  deleteUser: (id: string) => Promise<void>;
  
  // Team operations
  createTeam: (teamData: any) => Promise<Team>;
  updateTeam: (id: string, updates: any) => Promise<void>;
  deleteTeam: (id: string) => Promise<void>;
  
  // Preferences
  updateUserPreferences: (preferences: Partial<AppState['userPreferences']>) => Promise<void>;
  updateLastBoard: (boardId: string) => Promise<void>;
  getVisibleBoards: () => Board[];
  getLastBoardId: () => string | null;
  getAllUsersForAdmin: () => Promise<User[]>;

  // Search and filter
  updateSearchQuery: (query: string) => void;
  updateCardFilter: (filter: 'all' | 'assigned' | 'created' | 'due-today' | 'due-soon' | 'overdue') => void;

  // List operations
  addList: (boardId: string, listData: Omit<List, 'id'>) => Promise<void>;
  updateList: (boardId: string, listData: List) => Promise<void>;
  deleteList: (boardId: string, listId: string) => Promise<void>;

  // Card operations
  addCard: (boardId: string, listId: string, cardData: any) => Promise<void>;
  updateCard: (boardId: string, listId: string, cardData: any) => Promise<void>;

  // Label operations
  createLabel: (boardId: string, labelData: { name: string; color: string }) => Promise<any>;
}

const AppContext = createContext<AppContextType | null>(null);

export function DatabaseAppProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);
  const { user } = useAuth();

  // Load all data on mount and when user changes
  useEffect(() => {
    if (user) {
      loadAllData();
    }
  }, [user]);

  // Handle auto-redirect when boards are loaded and user is available
  useEffect(() => {
    if (user && state.boards.length > 0 && !state.loading) {
      // Only redirect if no current board is set
      if (!state.currentBoardId) {
        console.log('🔄 Boards loaded, triggering auto-redirect');
        handleAutoRedirectToLastBoard();
      }
    }
  }, [user, state.boards.length, state.loading, state.currentBoardId]);

  // Handle automatic redirection to last board
  const handleAutoRedirectToLastBoard = async () => {
    if (!user) return;

    console.log('🔄 Handling auto-redirect to last board for user:', user.name);

    try {
      // Get user preferences to find last board
      const preferences = await UserPreferencesService.getUserPreferences(user.id);
      const lastBoardId = preferences?.lastBoardId;

      console.log('📋 User preferences loaded:', { lastBoardId, hasPreferences: !!preferences });

      // Wait a bit to ensure boards are loaded
      await new Promise(resolve => setTimeout(resolve, 100));

      const visibleBoards = getVisibleBoards();
      console.log('👁️ Visible boards for user:', visibleBoards.map(b => ({ id: b.id, title: b.title })));

      if (lastBoardId && visibleBoards.length > 0) {
        // Check if the last board is still accessible to the user
        const lastBoard = visibleBoards.find(board => board.id === lastBoardId);

        if (lastBoard) {
          console.log('✅ Redirecting to last board:', lastBoard.title);
          setCurrentBoard(lastBoardId);
          return;
        } else {
          console.log('⚠️ Last board not accessible, redirecting to first available');
        }
      }

      // If no last board or not accessible, redirect to first available board
      if (visibleBoards.length > 0) {
        console.log('📌 Redirecting to first available board:', visibleBoards[0].title);
        setCurrentBoard(visibleBoards[0].id);
      } else {
        console.log('❌ No boards available, setting to null');
        setCurrentBoard(null);
      }
    } catch (error) {
      console.error('❌ Error handling auto-redirect to last board:', error);

      // Fallback: redirect to first available board
      const visibleBoards = getVisibleBoards();
      if (visibleBoards.length > 0) {
        console.log('🔄 Fallback: redirecting to first available board');
        setCurrentBoard(visibleBoards[0].id);
      }
    }
  };

  const loadUserPreferences = async () => {
    if (!user) return;

    try {
      const preferences = await UserPreferencesService.getUserPreferences(user.id);
      if (preferences) {
        dispatch({
          type: 'SET_USER_PREFERENCES',
          payload: {
            sidebarOpen: preferences.sidebarOpen,
            theme: preferences.theme as 'light' | 'dark',
            language: preferences.language as 'es' | 'en',
            lastBoardId: preferences.lastBoardId
          }
        });
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }
  };

  const loadAllData = async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      await Promise.all([
        loadBoards(),
        loadUsers(),
        loadTeams(),
        loadUserPreferences()
      ]);
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Error loading application data' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const loadBoards = async () => {
    try {
      const boards = await ApiService.getAllBoards(user?.id);
      dispatch({ type: 'SET_BOARDS', payload: boards });
    } catch (error) {
      console.error('Error loading boards:', error);
    }
  };

  const loadUsers = async () => {
    try {
      // The backend now handles filtering based on user role
      // Pass the current user ID so the backend can apply appropriate filters
      const allUsers = await ApiService.getAllUsers();

      console.log(`👥 Loaded ${allUsers.length} users (filtered by backend)`);
      dispatch({ type: 'SET_USERS', payload: allUsers });
    } catch (error) {
      console.error('Error loading users:', error);
    }
  };

  const loadTeams = async () => {
    try {
      const teams = await ApiService.getAllTeams();
      dispatch({ type: 'SET_TEAMS', payload: teams });
    } catch (error) {
      console.error('Error loading teams:', error);
    }
  };

  // Board operations
  const createBoard = async (boardData: Omit<Board, 'id' | 'createdAt' | 'updatedAt'>): Promise<Board> => {
    if (!user) throw new Error('User not authenticated');
    
    const newBoard = await ApiService.createBoard({
      ...boardData,
      creatorId: user.id
    });
    
    dispatch({ type: 'ADD_BOARD', payload: newBoard });
    return newBoard;
  };

  const updateBoard = async (id: string, updates: Partial<Board>): Promise<void> => {
    const updatedBoard = await ApiService.updateBoard(id, updates);
    dispatch({ type: 'UPDATE_BOARD', payload: updatedBoard });
  };

  const deleteBoard = async (id: string): Promise<void> => {
    await ApiService.deleteBoard(id);
    dispatch({ type: 'DELETE_BOARD', payload: id });
  };

  const addMemberToBoard = async (boardId: string, userId: string, role: string = 'member'): Promise<void> => {
    const updatedBoard = await ApiService.addMemberToBoard(boardId, userId, role);
    dispatch({ type: 'UPDATE_BOARD', payload: updatedBoard });
  };

  const removeMemberFromBoard = async (boardId: string, userId: string): Promise<void> => {
    const updatedBoard = await ApiService.removeMemberFromBoard(boardId, userId);
    dispatch({ type: 'UPDATE_BOARD', payload: updatedBoard });
  };

  const updateMemberRole = async (boardId: string, userId: string, role: string): Promise<void> => {
    console.log('🔄 Updating member role in context:', { boardId, userId, role });
    const updatedBoard = await ApiService.updateMemberRole(boardId, userId, role);
    console.log('✅ Member role updated, dispatching UPDATE_BOARD:', {
      boardId: updatedBoard.id,
      membersCount: updatedBoard.members?.length,
      members: updatedBoard.members?.map(m => ({ name: m.name, role: m.role }))
    });
    dispatch({ type: 'UPDATE_BOARD', payload: updatedBoard });
  };

  const setCurrentBoard = (boardId: string | null) => {
    console.log('🎯 Setting current board:', { boardId, userId: user?.id });
    dispatch({ type: 'SET_CURRENT_BOARD_ID', payload: boardId });

    // Update last board in preferences if boardId is not null
    if (boardId && user) {
      console.log('💾 Updating last board in preferences:', boardId);
      updateLastBoard(boardId).catch(error => {
        console.error('❌ Error updating last board:', error);
      });
    }
  };

  const getCurrentBoard = (): Board | null => {
    return state.boards.find(board => board.id === state.currentBoardId) || null;
  };

  const syncTeamMembersToBoard = async (boardId: string): Promise<void> => {
    const board = state.boards.find(b => b.id === boardId);
    if (!board || !board.teamId) return;

    const team = state.teams.find(t => t.id === board.teamId);
    if (!team) return;

    console.log('🔄 Auto-syncing team members to board:', {
      boardId,
      boardTitle: board.title,
      teamId: team.id,
      teamName: team.name
    });

    // Convert team members to board members
    const teamMembersAsBoardMembers = team.members.map(teamMember => {
      const userData = state.users.find(user => user.id === teamMember.userId);
      if (!userData) return null;

      return {
        id: userData.id,
        name: userData.name,
        email: userData.email,
        avatar: userData.avatar,
        role: teamMember.role.toLowerCase() as 'admin' | 'member' | 'observer',
        joinedAt: new Date()
      };
    }).filter(Boolean);

    // Merge with existing members (avoid duplicates)
    const existingMemberIds = board.members.map(m => m.id);
    const newMembers = teamMembersAsBoardMembers.filter(member =>
      member && !existingMemberIds.includes(member.id)
    );

    if (newMembers.length > 0) {
      const updatedBoard = {
        ...board,
        members: [...board.members, ...newMembers],
        updatedAt: new Date()
      };

      console.log('✅ Auto-synced team members to board:', {
        newMembersCount: newMembers.length,
        totalMembersAfter: updatedBoard.members.length
      });

      dispatch({ type: 'UPDATE_BOARD', payload: updatedBoard });
    }
  };

  // User operations
  const createUser = async (userData: any): Promise<User> => {
    const newUser = await ApiService.createUser(userData);
    dispatch({ type: 'ADD_USER', payload: newUser });
    return newUser;
  };

  const updateUser = async (id: string, updates: any): Promise<void> => {
    const updatedUser = await ApiService.updateUser(id, updates);
    dispatch({ type: 'UPDATE_USER', payload: updatedUser });
  };

  const deleteUser = async (id: string): Promise<void> => {
    await ApiService.deleteUser(id);
    dispatch({ type: 'DELETE_USER', payload: id });
  };

  // Team operations
  const createTeam = async (teamData: any): Promise<Team> => {
    if (!user) throw new Error('User not authenticated');

    // No need to add creatorId to teamData since it's now sent via query parameter
    const newTeam = await ApiService.createTeam(teamData);

    dispatch({ type: 'ADD_TEAM', payload: newTeam });
    return newTeam;
  };

  const updateTeam = async (id: string, updates: any): Promise<void> => {
    const updatedTeam = await ApiService.updateTeam(id, updates);
    dispatch({ type: 'UPDATE_TEAM', payload: updatedTeam });
  };

  const deleteTeam = async (id: string): Promise<void> => {
    await ApiService.deleteTeam(id);
    dispatch({ type: 'DELETE_TEAM', payload: id });
  };

  // Preferences
  const updateUserPreferences = async (preferences: Partial<AppState['userPreferences']>): Promise<void> => {
    if (!user) return;

    await UserPreferencesService.updateUserPreferences(user.id, preferences);
    dispatch({ type: 'SET_USER_PREFERENCES', payload: preferences });
  };

  const updateLastBoard = async (boardId: string): Promise<void> => {
    if (!user) return;

    console.log('💾 Updating last board preference:', { userId: user.id, boardId });
    try {
      await UserPreferencesService.updateLastBoard(user.id, boardId);
      dispatch({
        type: 'SET_USER_PREFERENCES',
        payload: { lastBoardId: boardId }
      });
      console.log('✅ Last board preference updated successfully');
    } catch (error) {
      console.error('❌ Failed to update last board preference:', error);
    }
  };

  const getVisibleBoards = (): Board[] => {
    if (!user) return [];
    return getVisibleBoardsForUser(state.boards, user);
  };

  const getLastBoardId = (): string | null => {
    return state.userPreferences.lastBoardId || null;
  };

  const getAllUsersForAdmin = async (): Promise<User[]> => {
    if (!user || user.role !== 'admin') {
      console.warn('getAllUsersForAdmin called by non-admin user');
      return [];
    }

    try {
      // Make a direct API call to get all users without filtering
      const response = await fetch(`${import.meta.env.VITE_API_URL || getApiBaseUrl()}/api/users/admin/all`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': user.id
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const allUsers = await response.json();
      console.log(`🛡️ Admin fetched all ${allUsers.length} users for team management`);
      return allUsers;
    } catch (error) {
      console.error('Error fetching all users for admin:', error);
      return [];
    }
  };

  // List operations
  const addList = async (boardId: string, listData: Omit<List, 'id'>) => {
    try {
      const newList = await ApiService.createList(boardId, listData);
      dispatch({ type: 'ADD_LIST', payload: { boardId, list: newList } });
    } catch (error) {
      console.error('Error adding list:', error);
    }
  };

  const updateList = async (boardId: string, listData: List) => {
    try {
      await ApiService.updateList(boardId, listData.id, listData);
      dispatch({ type: 'UPDATE_LIST', payload: { boardId, list: listData } });
    } catch (error) {
      console.error('Error updating list:', error);
    }
  };

  const deleteList = async (boardId: string, listId: string) => {
    try {
      await ApiService.deleteList(boardId, listId);
      dispatch({ type: 'DELETE_LIST', payload: { boardId, listId } });
    } catch (error) {
      console.error('Error deleting list:', error);
    }
  };

  // Card operations
  const addCard = async (boardId: string, listId: string, cardData: any): Promise<void> => {
    try {
      const newCard = await ApiService.createCard(boardId, listId, cardData);
      dispatch({ type: 'ADD_CARD', payload: { boardId, listId, card: newCard } });
    } catch (error) {
      console.error('Error adding card:', error);
    }
  };

  const updateCard = async (boardId: string, listId: string, cardData: any): Promise<void> => {
    try {
      // Update card in the server
      const updatedCard = await ApiService.updateCard(boardId, listId, cardData.id, cardData);

      // Update local state
      dispatch({
        type: 'UPDATE_CARD',
        payload: { boardId, listId, card: updatedCard }
      });
    } catch (error) {
      console.error('Error updating card:', error);
    }
  };

  // Search and filter functions
  const updateSearchQuery = (query: string) => {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query });
  };

  const updateCardFilter = (filter: 'all' | 'assigned' | 'created' | 'due-today' | 'due-soon' | 'overdue') => {
    dispatch({ type: 'SET_CARD_FILTER', payload: filter });
  };

  // Label operations
  const createLabel = async (boardId: string, labelData: { name: string; color: string }) => {
    try {
      const newLabel = await ApiService.createLabel(boardId, labelData);
      dispatch({ type: 'ADD_LABEL', payload: newLabel });
      return newLabel;
    } catch (error) {
      console.error('Error creating label:', error);
      throw error;
    }
  };

  const value: AppContextType = {
    state,
    dispatch,
    loadAllData,
    loadBoards,
    loadUsers,
    loadTeams,
    createBoard,
    updateBoard,
    deleteBoard,
    addMemberToBoard,
    removeMemberFromBoard,
    updateMemberRole,
    setCurrentBoard,
    getCurrentBoard,
    syncTeamMembersToBoard,
    createUser,
    updateUser,
    deleteUser,
    createTeam,
    updateTeam,
    deleteTeam,
    updateUserPreferences,
    updateLastBoard,
    getVisibleBoards,
    getLastBoardId,
    getAllUsersForAdmin,
    updateSearchQuery,
    updateCardFilter,
    addList,
    updateList,
    deleteList,
    addCard,
    updateCard,
    createLabel,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

export function useDatabaseApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useDatabaseApp must be used within a DatabaseAppProvider');
  }
  return context;
}
