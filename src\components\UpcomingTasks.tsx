import React, { useMemo } from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { Card as CardType } from '../types';
import { 
  Clock, 
  AlertTriangle, 
  Calendar,
  CheckCircle,
  User,
  Tag,
  ArrowRight
} from 'lucide-react';

interface UpcomingTasksProps {
  onCardClick?: (card: CardType) => void;
  onOpenCalendar?: () => void;
}

export function UpcomingTasks({ onCardClick, onOpenCalendar }: UpcomingTasksProps) {
  const { state } = useDatabaseApp();

  // Get upcoming tasks sorted by due date
  const upcomingTasks = useMemo(() => {
    const tasks: (CardType & { boardTitle: string; listTitle: string })[] = [];
    const now = new Date();
    const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    state.boards.forEach(board => {
      board.lists.forEach(list => {
        list.cards.forEach(card => {
          if (card.dueDate && !card.archived) {
            const dueDate = new Date(card.dueDate);
            if (dueDate <= weekFromNow) {
              tasks.push({
                ...card,
                boardTitle: board.title,
                listTitle: list.title
              });
            }
          }
        });
      });
    });

    return tasks.sort((a, b) => 
      new Date(a.dueDate!).getTime() - new Date(b.dueDate!).getTime()
    ).slice(0, 10); // Show only next 10 tasks
  }, [state.boards]);

  const getTaskPriority = (card: CardType) => {
    const now = new Date();
    const dueDate = new Date(card.dueDate!);
    const diffHours = (dueDate.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    if (diffHours < 0) return { level: 'overdue', color: 'text-red-600', bgColor: 'bg-red-50', icon: AlertTriangle };
    if (diffHours < 24) return { level: 'urgent', color: 'text-orange-600', bgColor: 'bg-orange-50', icon: Clock };
    if (diffHours < 72) return { level: 'soon', color: 'text-yellow-600', bgColor: 'bg-yellow-50', icon: Calendar };
    return { level: 'normal', color: 'text-blue-600', bgColor: 'bg-blue-50', icon: CheckCircle };
  };

  const formatRelativeTime = (date: Date) => {
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffMs < 0) {
      const pastHours = Math.abs(diffHours);
      const pastDays = Math.abs(diffDays);
      if (pastDays > 0) return `Vencida hace ${pastDays} día${pastDays > 1 ? 's' : ''}`;
      if (pastHours > 0) return `Vencida hace ${pastHours} hora${pastHours > 1 ? 's' : ''}`;
      return 'Vencida hace poco';
    }

    if (diffDays > 0) return `En ${diffDays} día${diffDays > 1 ? 's' : ''}`;
    if (diffHours > 0) return `En ${diffHours} hora${diffHours > 1 ? 's' : ''}`;
    return 'Muy pronto';
  };

  if (upcomingTasks.length === 0) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Próximas Tareas</h3>
          <button
            onClick={onOpenCalendar}
            className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
          >
            Ver calendario
          </button>
        </div>
        
        <div className="text-center py-8">
          <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-3" />
          <p className="text-gray-600 mb-2">¡Excelente! No tienes tareas próximas</p>
          <p className="text-sm text-gray-500">Todas tus tareas están bajo control</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Próximas Tareas</h3>
        <button
          onClick={onOpenCalendar}
          className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-700 transition-colors"
        >
          <span>Ver todas</span>
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>

      <div className="space-y-3">
        {upcomingTasks.map((task) => {
          const priority = getTaskPriority(task);
          const PriorityIcon = priority.icon;
          const isAssignedToMe = task.assignedMembers.some(member => member.id === state.currentUser?.id);

          return (
            <div
              key={task.id}
              onClick={() => onCardClick?.(task)}
              className={`p-3 rounded-lg border cursor-pointer hover:shadow-md transition-all duration-200 ${priority.bgColor} border-gray-200 hover:border-gray-300`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    <PriorityIcon className={`w-4 h-4 ${priority.color}`} />
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {task.title}
                    </h4>
                    {isAssignedToMe && (
                      <div className="flex items-center space-x-1">
                        <User className="w-3 h-3 text-blue-500" />
                        <span className="text-xs text-blue-600 font-medium">Mía</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-3 text-xs text-gray-600 mb-2">
                    <span className="truncate">{task.boardTitle} › {task.listTitle}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs font-medium ${priority.color}`}>
                        {formatRelativeTime(new Date(task.dueDate!))}
                      </span>
                      
                      {task.labels.length > 0 && (
                        <div className="flex items-center space-x-1">
                          <Tag className="w-3 h-3 text-gray-400" />
                          <div className="flex space-x-1">
                            {task.labels.slice(0, 2).map(label => (
                              <div
                                key={label.id}
                                className="w-2 h-2 rounded-full"
                                style={{ backgroundColor: label.color }}
                                title={label.name}
                              />
                            ))}
                            {task.labels.length > 2 && (
                              <span className="text-xs text-gray-500">+{task.labels.length - 2}</span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="text-xs text-gray-500">
                      {new Date(task.dueDate!).toLocaleDateString('es-ES', {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>

                  {/* Assigned Members */}
                  {task.assignedMembers.length > 0 && (
                    <div className="flex items-center space-x-1 mt-2">
                      <div className="flex -space-x-1">
                        {task.assignedMembers.slice(0, 3).map(member => (
                          <img
                            key={member.id}
                            src={member.avatar}
                            alt={member.name}
                            className="w-5 h-5 rounded-full border border-white"
                            title={member.name}
                          />
                        ))}
                        {task.assignedMembers.length > 3 && (
                          <div className="w-5 h-5 rounded-full bg-gray-200 border border-white flex items-center justify-center text-xs font-medium text-gray-600">
                            +{task.assignedMembers.length - 3}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {upcomingTasks.length >= 10 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <button
            onClick={onOpenCalendar}
            className="w-full text-center text-sm text-blue-600 hover:text-blue-700 transition-colors"
          >
            Ver más tareas en el calendario
          </button>
        </div>
      )}
    </div>
  );
}
