async function testApiEndpoints() {
  console.log('🧪 Testing all API endpoints...\n');

  try {
    // Test Users endpoint
    console.log('👥 Testing Users endpoint...');
    const usersResponse = await fetch('http://localhost:3001/api/users');
    const users = await usersResponse.json();
    console.log(`✅ Users: ${users.length} found`);

    // Test Teams endpoint
    console.log('\n🏢 Testing Teams endpoint...');
    const teamsResponse = await fetch('http://localhost:3001/api/teams');
    const teams = await teamsResponse.json();
    console.log(`✅ Teams: ${teams.length} found`);
    if (teams.length > 0) {
      console.log(`   - ${teams[0].name}: ${teams[0].members.length} members`);
    }

    // Test Boards endpoint
    console.log('\n📋 Testing Boards endpoint...');
    const boardsResponse = await fetch('http://localhost:3001/api/boards');
    const boards = await boardsResponse.json();
    console.log(`✅ Boards: ${boards.length} found`);
    if (boards.length > 0) {
      const board = boards[0];
      console.log(`   - ${board.title}: ${board.lists.length} lists, ${board.members.length} members`);
      console.log(`   - Labels: ${board.labels.length}`);
      
      // Count total cards
      const totalCards = board.lists.reduce((sum, list) => sum + list.cards.length, 0);
      console.log(`   - Total cards: ${totalCards}`);
    }

    // Test Health endpoint
    console.log('\n🏥 Testing Health endpoint...');
    const healthResponse = await fetch('http://localhost:3001/health');
    const health = await healthResponse.json();
    console.log(`✅ Health: ${health.message}`);

    console.log('\n🎉 All API endpoints are working correctly!');

    // Summary
    console.log('\n📊 API Summary:');
    console.log(`   - Users: ${users.length}`);
    console.log(`   - Teams: ${teams.length}`);
    console.log(`   - Boards: ${boards.length}`);
    console.log(`   - Server: Running`);

  } catch (error) {
    console.error('❌ API test failed:', error);
  }
}

testApiEndpoints();
