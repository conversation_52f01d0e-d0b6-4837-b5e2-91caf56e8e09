import React, { useState } from 'react';
import { Menu, X, Home, Users, Settings, Calendar, Bell, LogOut, Grid3X3, Search, Plus } from 'lucide-react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { useAuth } from '../context/AuthContext';
import { useUserPermissions } from '../hooks/useUserPermissions';
import { useResponsive } from '../hooks/useResponsive';

interface MobileNavigationProps {
  onBoardManagerOpen: () => void;
  onTeamManagerOpen: () => void;
  onUserManagerOpen: () => void;
  onCalendarOpen: () => void;
  onNotificationsOpen: () => void;
}

export function MobileNavigation({
  onBoardManagerOpen,
  onTeamManagerOpen,
  onUserManagerOpen,
  onCalendarOpen,
  onNotificationsOpen,
}: MobileNavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { state, getVisibleBoards } = useDatabaseApp();
  const { logout } = useAuth();
  const { isAdmin } = useUserPermissions();
  const { isMobile } = useResponsive();

  const visibleBoards = getVisibleBoards();

  const handleMenuItemClick = (action: () => void) => {
    action();
    setIsOpen(false);
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`md:hidden ${isMobile ? 'p-3 min-w-[44px] min-h-[44px]' : 'p-2'} rounded-lg hover:bg-gray-100 transition-colors flex items-center justify-center`}
        aria-label="Abrir menú"
      >
        {isOpen ? (
          <X className="w-6 h-6 text-gray-600" />
        ) : (
          <Menu className="w-6 h-6 text-gray-600" />
        )}
      </button>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Menu Panel */}
          <div className="absolute top-0 left-0 w-80 max-w-[85vw] h-full bg-white shadow-xl">
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Menú</h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <X className="w-5 h-5 text-gray-600" />
                </button>
              </div>

              {/* Navigation Items */}
              <div className="flex-1 overflow-y-auto p-4">
                <nav className="space-y-2">
                  {/* Boards Section */}
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">
                      Tableros
                    </h3>
                    <button
                      onClick={() => handleMenuItemClick(onBoardManagerOpen)}
                      className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 transition-colors text-left"
                    >
                      <Home className="w-5 h-5 text-gray-600" />
                      <span className="text-gray-900">Gestión de Tableros</span>
                    </button>
                    
                    {/* Recent Boards */}
                    {visibleBoards.slice(0, 3).map((board) => (
                      <button
                        key={board.id}
                        onClick={() => {
                          // TODO: Navigate to board
                          setIsOpen(false);
                        }}
                        className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 transition-colors text-left"
                      >
                        <div className="w-5 h-5 rounded bg-blue-500 flex-shrink-0" />
                        <span className="text-gray-700 truncate">{board.title}</span>
                      </button>
                    ))}
                  </div>

                  {/* Management Section */}
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">
                      Gestión
                    </h3>
                    
                    <button
                      onClick={() => handleMenuItemClick(onTeamManagerOpen)}
                      className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 transition-colors text-left"
                    >
                      <Users className="w-5 h-5 text-gray-600" />
                      <span className="text-gray-900">Gestión de Equipos</span>
                    </button>

                    {isAdmin() && (
                      <button
                        onClick={() => handleMenuItemClick(onUserManagerOpen)}
                        className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 transition-colors text-left"
                      >
                        <Settings className="w-5 h-5 text-gray-600" />
                        <span className="text-gray-900">Gestión de Usuarios</span>
                      </button>
                    )}

                    <button
                      onClick={() => handleMenuItemClick(onCalendarOpen)}
                      className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 transition-colors text-left"
                    >
                      <Calendar className="w-5 h-5 text-gray-600" />
                      <span className="text-gray-900">Calendario</span>
                    </button>

                    <button
                      onClick={() => handleMenuItemClick(onNotificationsOpen)}
                      className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 transition-colors text-left"
                    >
                      <Bell className="w-5 h-5 text-gray-600" />
                      <span className="text-gray-900">Notificaciones</span>
                    </button>
                  </div>
                </nav>
              </div>

              {/* Footer */}
              <div className="border-t border-gray-200 p-4">
                <button
                  onClick={() => {
                    logout();
                    setIsOpen(false);
                  }}
                  className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-red-50 text-red-600 transition-colors"
                >
                  <LogOut className="w-5 h-5" />
                  <span>Cerrar Sesión</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
