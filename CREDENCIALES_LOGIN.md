# 🔐 Credenciales de Login - Sistema Trello

## Usuarios por Defecto

### 👨‍💼 Admin User
- **Email/Usuario**: `<EMAIL>` o `admin`
- **Contraseña**: `password123`
- **Rol**: Administrador
- **Permisos**: Acceso completo al sistema

### 👩‍💻 Ana García
- **Email/Usuario**: `<EMAIL>` o `ana.garcia`
- **Contraseña**: `password123`
- **Rol**: Miembro
- **Permisos**: <PERSON>rear tableros, editar tarjetas

### 👨‍💻 <PERSON>
- **Email/Usuario**: `<EMAIL>` o `carlos.lopez`
- **Contraseña**: `password123`
- **Rol**: Observador
- **Permisos**: Solo lectura

## Usuarios Creados Dinámicamente

Cualquier usuario que hayas creado a través del sistema de gestión de usuarios tendrá:
- **Contraseña por defecto**: `password123`
- **Login**: <PERSON>uedes usar su email o username

## Cómo Probar

1. **Logout** si estás logueado
2. Ve a la pantalla de login
3. Usa cualquiera de las credenciales de arriba
4. ¡Disfruta del sistema!

## Notas Importantes

- ✅ El sistema ahora está **completamente integrado**
- ✅ Los usuarios creados aparecen **inmediatamente** en la tabla
- ✅ Puedes hacer login con **email o username**
- ✅ Se verifica que el usuario esté **activo**
- ✅ Se actualiza el **último login** automáticamente

## Funcionalidades Integradas

- 🔄 **Sincronización completa** entre UserService y AuthService
- 👥 **Gestión de usuarios** en tiempo real
- 🔐 **Autenticación** con usuarios creados dinámicamente
- 📊 **Métricas de usuario** actualizadas
- 🎯 **Roles y permisos** funcionales
