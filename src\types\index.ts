export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'admin' | 'member' | 'observer';
  teams?: string[]; // IDs of teams the user belongs to
  username?: string;
  phone?: string;
  isActive?: boolean;
  createdAt?: Date;
  lastLogin?: Date;
  metrics?: UserMetrics;
}

export interface UserMetrics {
  tasksCompleted: number;
  tasksOnTime: number;
  totalTasks: number;
  averageCompletionTime: number; // in hours
  score: number; // calculated score based on performance
  recentActivity: ActivitySummary[];
}

export interface ActivitySummary {
  id: string;
  type: 'task_completed' | 'task_created' | 'comment_added' | 'card_moved';
  description: string;
  timestamp: Date;
  boardId?: string;
  cardId?: string;
}

export interface UserFormData {
  username: string;
  name: string;
  email: string;
  phone: string;
  password: string;
  role: 'admin' | 'member' | 'observer';
}

export interface UserPermissions {
  canCreateBoards: boolean;
  canEditCards: boolean;
  canDeleteCards: boolean;
  canManageUsers: boolean;
  canViewReports: boolean;
  canManageSettings: boolean;
  canInviteUsers: boolean;
  canArchiveCards: boolean;
}

export interface Team {
  id: string;
  name: string;
  description: string;
  avatar?: string;
  visibility: 'private' | 'team' | 'organization' | 'public';
  members: TeamMember[];
  boards: string[]; // Board IDs that belong to this team
  settings: TeamSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface TeamMember {
  userId: string;
  role: 'admin' | 'normal' | 'observer';
  joinedAt: Date;
  invitedBy?: string;
}

export interface BoardMember extends User {
  role: 'admin' | 'member' | 'observer';
  joinedAt: Date;
  invitedBy?: string;
}

export interface TeamSettings {
  allowMembersToCreateBoards: boolean;
  allowMembersToInvite: boolean;
  defaultBoardVisibility: 'private' | 'team' | 'organization' | 'public';
  requireApprovalForJoining: boolean;
}

export interface TeamInvitation {
  id: string;
  teamId: string;
  inviterUserId: string;
  inviteeEmail: string;
  role: 'admin' | 'normal' | 'observer';
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  createdAt: Date;
  expiresAt: Date;
  message?: string;
}

export interface Label {
  id: string;
  name: string;
  color: string;
}

export interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
}

export interface Comment {
  id: string;
  text: string;
  author: User;
  createdAt: Date;
  updatedAt?: Date;
  mentions?: string[];
}

export interface Attachment {
  id: string;
  name: string;
  url: string;
  type: 'file' | 'link';
  size?: number;
}

export interface Card {
  id: string;
  title: string;
  description?: string;
  labels: Label[];
  dueDate?: Date;
  assignedMembers: User[];
  checklist: ChecklistItem[];
  comments: Comment[];
  attachments: Attachment[];
  cover?: string; // URL or color/gradient for card cover
  position: number;
  archived: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface List {
  id: string;
  title: string;
  cards: Card[];
  position: number;
  archived: boolean;
}

export interface Board {
  id: string;
  title: string;
  description?: string;
  background: string;
  lists: List[];
  members: BoardMember[];
  labels: Label[];
  isPublic: boolean;
  isFavorite: boolean;
  teamId?: string; // ID of the team this board belongs to
  visibility: 'private' | 'team' | 'organization' | 'public';
  createdAt: Date;
  updatedAt: Date;
}

export interface Activity {
  id: string;
  type: 'card_created' | 'card_moved' | 'card_updated' | 'member_added' | 'comment_added';
  description: string;
  user: User;
  createdAt: Date;
  boardId: string;
  cardId?: string;
}

export interface AppNotification {
  id: string;
  type: 'mention' | 'due_date' | 'assignment' | 'comment';
  title: string;
  message: string;
  read: boolean;
  createdAt: Date;
  boardId?: string;
  cardId?: string;
}