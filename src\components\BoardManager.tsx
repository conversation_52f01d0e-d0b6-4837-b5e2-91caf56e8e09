import React, { useState, useEffect, useRef } from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { useUserPermissions } from '../hooks/useUserPermissions';
import { useResponsive } from '../hooks/useResponsive';
import { ResponsiveModal } from './ResponsiveModal';
import { Board, User } from '../types';
import { formatDate } from '../utils/dateUtils';
import { 
  Plus, 
  Star, 
  Users, 
  Lock, 
  Globe, 
  MoreHorizontal,
  Edit3,
  Trash2,
  Copy,
  Archive,
  X,
  Calendar,
  Search
} from 'lucide-react';

interface BoardManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function BoardManager({ isOpen, onClose }: BoardManagerProps) {
  const { state, loadBoards, createBoard, updateBoard, deleteBoard, setCurrentBoard, getCurrentBoard, getVisibleBoards } = useDatabaseApp();
  const { isAdmin, canCreateBoards } = useUserPermissions();
  const { isMobile } = useResponsive();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingBoard, setEditingBoard] = useState<Board | null>(null);
  const [showContextMenu, setShowContextMenu] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState<'all' | 'favorites' | 'recent'>('all');
  const [newBoard, setNewBoard] = useState({
    title: '',
    description: '',
    background: 'bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500',
    isPublic: false
  });
  const [selectedMembers, setSelectedMembers] = useState<User[]>(
    state.currentUser ? [state.currentUser] : []
  );
  const contextMenuRef = useRef<HTMLDivElement>(null);

  // Helper function to safely reset selected members
  const resetSelectedMembers = () => {
    setSelectedMembers(state.currentUser ? [state.currentUser] : []);
  };

  // Sync selectedMembers when currentUser changes
  useEffect(() => {
    if (state.currentUser && selectedMembers.length === 0) {
      setSelectedMembers([state.currentUser]);
    }
  }, [state.currentUser]);

  // Close context menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        setShowContextMenu(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const backgroundOptions = [
    'bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500',
    'bg-gradient-to-br from-green-400 to-blue-500',
    'bg-gradient-to-br from-purple-400 to-pink-400',
    'bg-gradient-to-br from-yellow-400 to-orange-500',
    'bg-gradient-to-br from-red-400 to-pink-500',
    'bg-gradient-to-br from-indigo-400 to-purple-500',
    'bg-gradient-to-br from-teal-400 to-blue-500',
    'bg-gradient-to-br from-orange-400 to-red-500'
  ];

  // Get visible boards for the current user and then filter
  const visibleBoards = getVisibleBoards();

  const filteredBoards = visibleBoards.filter(board => {
    const matchesSearch = board.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         board.description?.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (!matchesSearch) return false;

    switch (filter) {
      case 'favorites':
        return board.isFavorite;
      case 'recent':
        // Show boards updated in last 7 days
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        return new Date(board.updatedAt) > weekAgo;
      default:
        return true;
    }
  });

  const handleCreateBoard = async () => {
    if (!newBoard.title.trim()) return;

    try {
      const board = await createBoard({
        title: newBoard.title.trim(),
        description: newBoard.description.trim(),
        background: newBoard.background,
        members: selectedMembers,
        isPublic: newBoard.isPublic
      });

      setCurrentBoard(board.id);

      setNewBoard({
        title: '',
        description: '',
        background: backgroundOptions[0],
        isPublic: false
      });
      resetSelectedMembers();
      setShowCreateForm(false);
      onClose();
    } catch (error) {
      console.error('Error creating board:', error);
      alert('Error al crear el tablero. Por favor, inténtalo de nuevo.');
    }
  };

  const handleSelectBoard = (board: Board) => {
    setCurrentBoard(board.id);
    onClose();
  };

  const handleToggleFavorite = async (board: Board, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await updateBoard(board.id, { isFavorite: !board.isFavorite });
    } catch (error) {
      console.error('Error updating board favorite status:', error);
    }
  };



  const handleToggleMember = (member: User) => {
    setSelectedMembers(prev => {
      const isSelected = prev.some(m => m.id === member.id);
      if (isSelected) {
        // Don't allow removing the current user
        if (member.id === state.currentUser?.id) return prev;
        return prev.filter(m => m.id !== member.id);
      } else {
        return [...prev, member];
      }
    });
  };

  const handleEditBoard = (board: Board, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingBoard(board);
    setNewBoard({
      title: board.title,
      description: board.description || '',
      background: board.background,
      isPublic: board.isPublic
    });
    setSelectedMembers(board.members);
    setShowEditForm(true);
    setShowContextMenu(null);
  };

  const handleUpdateBoard = async () => {
    if (!editingBoard || !newBoard.title.trim()) return;

    try {
      await updateBoard(editingBoard.id, {
        title: newBoard.title.trim(),
        description: newBoard.description.trim(),
        background: newBoard.background,
        members: selectedMembers,
        isPublic: newBoard.isPublic
      });

      // Reset form
      setNewBoard({
        title: '',
        description: '',
        background: backgroundOptions[0],
        isPublic: false
      });
      resetSelectedMembers();
      setEditingBoard(null);
      setShowEditForm(false);
    } catch (error) {
      console.error('Error updating board:', error);
      alert('Error al actualizar el tablero. Por favor, inténtalo de nuevo.');
    }
  };

  const handleCancelEdit = () => {
    setNewBoard({
      title: '',
      description: '',
      background: backgroundOptions[0],
      isPublic: false
    });
    resetSelectedMembers();
    setEditingBoard(null);
    setShowEditForm(false);
  };

  const handleDeleteBoardConfirm = async (board: Board, e: React.MouseEvent) => {
    e.stopPropagation();
    setShowContextMenu(null);
    if (confirm(`¿Estás seguro de que quieres eliminar el tablero "${board.title}"?\n\nEsta acción no se puede deshacer y se perderán todas las listas, tarjetas y datos del tablero.`)) {
      try {
        await deleteBoard(board.id);

        // If we're deleting the current board, clear it
        const currentBoard = getCurrentBoard();
        if (currentBoard?.id === board.id) {
          setCurrentBoard(null);
        }
      } catch (error) {
        console.error('Error deleting board:', error);
        alert('Error al eliminar el tablero. Por favor, inténtalo de nuevo.');
      }
    }
  };

  const handleDuplicateBoard = async (board: Board, e: React.MouseEvent) => {
    e.stopPropagation();
    setShowContextMenu(null);

    try {
      await createBoard({
        title: `${board.title} (copia)`,
        description: board.description,
        background: board.background,
        members: board.members,
        isPublic: board.isPublic
      });
    } catch (error) {
      console.error('Error duplicating board:', error);
      alert('Error al duplicar el tablero. Por favor, inténtalo de nuevo.');
    }
  };

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      title="Gestión de Tableros"
      size="xl"
      className="overflow-hidden"
    >
        <div onClick={() => setShowContextMenu(null)}>
          {/* Search and Filters */}
          <div className="mt-4 space-y-3 md:space-y-0 md:flex md:items-center md:space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={isMobile ? "Buscar..." : "Buscar tableros..."}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm md:text-base"
              />
            </div>

            <div className="flex space-x-1 md:space-x-2 overflow-x-auto">
              {[
                { key: 'all', label: 'Todos' },
                { key: 'favorites', label: 'Favoritos' },
                { key: 'recent', label: 'Recientes' }
              ].map(option => (
                <button
                  key={option.key}
                  onClick={() => setFilter(option.key as any)}
                  className={`px-3 py-2 text-xs md:text-sm rounded-lg transition-colors whitespace-nowrap min-h-[44px] ${
                    filter === option.key
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>

            {isAdmin() && (
              <button
                onClick={() => {
                  setShowCreateForm(true);
                  resetSelectedMembers();
                }}
                className="flex items-center space-x-1 md:space-x-2 bg-blue-600 text-white px-3 md:px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors min-h-[44px] whitespace-nowrap"
              >
                <Plus className="w-4 h-4 flex-shrink-0" />
                <span className="text-sm md:text-base">{isMobile ? 'Nuevo' : 'Nuevo Tablero'}</span>
              </button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className={`${isMobile ? 'p-4' : 'p-6'} overflow-y-auto flex-1`}>
          {(showCreateForm || showEditForm) ? (
            /* Create/Edit Board Form */
            <div className="bg-gray-50 rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {showEditForm ? 'Editar Tablero' : 'Crear Nuevo Tablero'}
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Título del tablero
                  </label>
                  <input
                    type="text"
                    value={newBoard.title}
                    onChange={(e) => setNewBoard(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Ej: Proyecto de Marketing"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Descripción (opcional)
                  </label>
                  <textarea
                    value={newBoard.description}
                    onChange={(e) => setNewBoard(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe el propósito de este tablero..."
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Fondo del tablero
                  </label>
                  <div className="grid grid-cols-4 gap-2">
                    {backgroundOptions.map((bg, index) => (
                      <button
                        key={index}
                        onClick={() => setNewBoard(prev => ({ ...prev, background: bg }))}
                        className={`h-16 rounded-lg ${bg} ${
                          newBoard.background === bg ? 'ring-2 ring-blue-500' : ''
                        }`}
                      />
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Miembros del tablero
                  </label>
                  <div className="space-y-3">
                    {/* Selected Members */}
                    <div className="flex flex-wrap gap-2">
                      {selectedMembers.filter(member => member && member.id && member.name).map((member) => (
                        <div
                          key={member.id}
                          className="flex items-center space-x-2 bg-blue-50 border border-blue-200 rounded-lg px-3 py-2"
                        >
                          {member.avatar ? (
                            <img
                              src={member.avatar}
                              alt={member.name}
                              className="w-6 h-6 rounded-full object-cover"
                            />
                          ) : (
                            <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs font-bold">
                              {member.name.charAt(0).toUpperCase()}
                            </div>
                          )}
                          <span className="text-sm font-medium text-blue-900">{member.name}</span>
                          {member.id !== state.currentUser?.id && (
                            <button
                              onClick={() => handleToggleMember(member)}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          )}
                          {member.id === state.currentUser?.id && (
                            <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                              Tú
                            </span>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* Available Members */}
                    <div className="border border-gray-200 rounded-lg p-3 max-h-32 overflow-y-auto">
                      <div className="text-xs font-medium text-gray-500 mb-2">Agregar miembros:</div>
                      <div className="space-y-1">
                        {state.users
                          .filter(user => user && user.id && user.name && !selectedMembers.some(m => m.id === user.id))
                          .map((user) => (
                            <button
                              key={user.id}
                              onClick={() => handleToggleMember(user)}
                              className="w-full flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-lg text-left"
                            >
                              {user.avatar ? (
                                <img
                                  src={user.avatar}
                                  alt={user.name}
                                  className="w-6 h-6 rounded-full object-cover"
                                />
                              ) : (
                                <div className="w-6 h-6 rounded-full bg-gray-500 flex items-center justify-center text-white text-xs font-bold">
                                  {user.name.charAt(0).toUpperCase()}
                                </div>
                              )}
                              <div className="flex-1">
                                <div className="text-sm font-medium text-gray-900">{user.name}</div>
                                <div className="text-xs text-gray-500">{user.email}</div>
                              </div>
                            </button>
                          ))}
                      </div>
                      {state.users.filter(user => !selectedMembers.some(m => m.id === user.id)).length === 0 && (
                        <div className="text-sm text-gray-500 text-center py-2">
                          Todos los usuarios disponibles han sido agregados
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isPublic"
                    checked={newBoard.isPublic}
                    onChange={(e) => setNewBoard(prev => ({ ...prev, isPublic: e.target.checked }))}
                    className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="isPublic" className="text-sm text-gray-700">
                    Hacer público (otros pueden ver y unirse)
                  </label>
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={showEditForm ? handleUpdateBoard : handleCreateBoard}
                    disabled={!newBoard.title.trim()}
                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    {showEditForm ? 'Actualizar Tablero' : 'Crear Tablero'}
                  </button>
                  <button
                    onClick={showEditForm ? handleCancelEdit : () => {
                      setShowCreateForm(false);
                      setNewBoard({
                        title: '',
                        description: '',
                        background: backgroundOptions[0],
                        isPublic: false
                      });
                      resetSelectedMembers();
                    }}
                    className="flex-1 bg-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-300"
                  >
                    Cancelar
                  </button>
                </div>
              </div>
            </div>
          ) : null}

          {/* Boards Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 md:gap-4">
            {filteredBoards.map((board) => (
              <div
                key={board.id}
                onClick={() => handleSelectBoard(board)}
                className="group relative bg-white border border-gray-200 rounded-lg p-3 md:p-4 hover:shadow-md transition-all duration-200 cursor-pointer min-h-[120px] md:min-h-[140px]"
              >
                {/* Board Preview */}
                <div className={`h-20 rounded-lg mb-3 ${board.background} relative overflow-hidden`}>
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                  <div className="absolute bottom-2 left-2 right-2">
                    <div className="flex space-x-1">
                      {board.lists.slice(0, 3).map((list, index) => (
                        <div key={index} className="bg-white bg-opacity-80 rounded w-8 h-6"></div>
                      ))}
                      {board.lists.length > 3 && (
                        <div className="bg-white bg-opacity-60 rounded w-8 h-6 flex items-center justify-center">
                          <span className="text-xs text-gray-600">+{board.lists.length - 3}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Board Info */}
                <div className="space-y-2">
                  <div className="flex items-start justify-between">
                    <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                      {board.title}
                    </h3>
                    <button
                      onClick={(e) => handleToggleFavorite(board, e)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Star className={`w-4 h-4 ${
                        board.isFavorite ? 'text-yellow-500 fill-current' : 'text-gray-400'
                      }`} />
                    </button>
                  </div>

                  {board.description && (
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {board.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        <Users className="w-3 h-3" />
                        <span>{board.members.length}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        {board.isPublic ? <Globe className="w-3 h-3" /> : <Lock className="w-3 h-3" />}
                        <span>{board.isPublic ? 'Público' : 'Privado'}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>{formatDate(board.updatedAt)}</span>
                    </div>
                  </div>
                </div>

                {/* Actions Menu */}
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="relative">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowContextMenu(showContextMenu === board.id ? null : board.id);
                      }}
                      className="p-1 bg-white bg-opacity-80 hover:bg-opacity-100 rounded"
                    >
                      <MoreHorizontal className="w-4 h-4 text-gray-600" />
                    </button>

                    {/* Context Menu */}
                    {showContextMenu === board.id && (
                      <div
                        ref={contextMenuRef}
                        className="absolute right-0 top-8 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10"
                      >
                        <div className="py-1">
                          <button
                            onClick={(e) => handleEditBoard(board, e)}
                            className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                          >
                            <Edit3 className="w-4 h-4" />
                            <span>Editar tablero</span>
                          </button>
                          <button
                            onClick={(e) => handleDuplicateBoard(board, e)}
                            className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                          >
                            <Copy className="w-4 h-4" />
                            <span>Duplicar tablero</span>
                          </button>
                          <button
                            onClick={(e) => handleToggleFavorite(board, e)}
                            className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                          >
                            <Star className={`w-4 h-4 ${board.isFavorite ? 'text-yellow-500' : ''}`} />
                            <span>{board.isFavorite ? 'Quitar de favoritos' : 'Agregar a favoritos'}</span>
                          </button>
                          <hr className="my-1" />
                          <button
                            onClick={(e) => handleDeleteBoardConfirm(board, e)}
                            className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                          >
                            <Trash2 className="w-4 h-4" />
                            <span>Eliminar tablero</span>
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Current Board Indicator */}
                {state.currentBoard?.id === board.id && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full"></div>
                )}
              </div>
            ))}
          </div>

          {filteredBoards.length === 0 && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <Plus className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchQuery ? 'No se encontraron tableros' : 'No hay tableros'}
              </h3>
              <p className="text-gray-500 mb-4">
                {searchQuery 
                  ? 'Intenta con otros términos de búsqueda'
                  : 'Crea tu primer tablero para comenzar a organizar tus tareas'
                }
              </p>
              {!searchQuery && isAdmin() && (
                <button
                  onClick={() => {
                    setShowCreateForm(true);
                    resetSelectedMembers();
                  }}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Crear Tablero
                </button>
              )}
              {!searchQuery && !isAdmin() && (
                <div className="text-sm text-orange-600 bg-orange-50 p-3 rounded-lg">
                  Solo los administradores pueden crear tableros
                </div>
              )}
            </div>
          )}
        </div>
    </ResponsiveModal>
  );
}
