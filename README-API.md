# 🚀 Trello Clone - API Backend + Frontend

## 📋 Descripción

Este proyecto ahora utiliza una arquitectura **API Backend + Frontend** para resolver los problemas de compatibilidad de Prisma con el navegador.

## 🏗️ Arquitectura

```
┌─────────────────┐    HTTP/API    ┌─────────────────┐
│   Frontend      │ ◄─────────────► │   Backend       │
│   (React)       │                 │   (Express)     │
│   Port: 5173    │                 │   Port: 3001    │
└─────────────────┘                 └─────────────────┘
                                            │
                                            ▼
                                    ┌─────────────────┐
                                    │   Database      │
                                    │   (SQLite)      │
                                    │   + Prisma ORM  │
                                    └─────────────────┘
```

## 🚀 Inicio Rápido

### **Opción 1: Ejecutar ambos servidores automáticamente**
```bash
npm run dev:full
```

### **Opción 2: Ejecutar servidores por separado**

**Terminal 1 - Backend API:**
```bash
npm run server
```

**Terminal 2 - Frontend:**
```bash
npm run dev
```

## 🔧 Scripts Disponibles

| Script | Descripción |
|--------|-------------|
| `npm run dev` | Inicia solo el frontend (Vite) |
| `npm run server` | Inicia solo el backend (Express + Prisma) |
| `npm run dev:full` | Inicia ambos servidores simultáneamente |
| `npm run seed` | Crea usuarios por defecto en la base de datos |
| `npm run verify` | Verifica la conexión a Prisma y muestra usuarios |
| `npm run build` | Construye la aplicación para producción |

## 🌐 URLs de Acceso

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **Health Check**: http://localhost:3001/health

## 🔑 Credenciales de Prueba

| Usuario | Email | Contraseña | Rol |
|---------|-------|------------|-----|
| Administrador | <EMAIL> | admin123 | Admin |
| Ana García | <EMAIL> | ana123 | Member |
| Carlos López | <EMAIL> | carlos123 | Observer |

## 📡 API Endpoints

### **Autenticación**
- `POST /api/auth/login` - Iniciar sesión

### **Usuarios**
- `GET /api/users` - Obtener todos los usuarios
- `POST /api/users` - Crear nuevo usuario
- `PUT /api/users/:id` - Actualizar usuario
- `DELETE /api/users/:id` - Eliminar usuario

### **Health Check**
- `GET /health` - Verificar estado del servidor

## 🛠️ Configuración de Desarrollo

### **Requisitos**
- Node.js 18+
- npm o yarn

### **Instalación**
```bash
# Instalar dependencias
npm install

# Configurar Prisma
npx prisma generate
npx prisma migrate dev

# Crear usuarios por defecto
npm run seed
```

### **Variables de Entorno**
Crear archivo `.env` en la raíz del proyecto:
```env
DATABASE_URL="file:./dev.db"
PORT=3001
```

## 🔄 Fallback a localStorage

Si el servidor backend no está disponible, la aplicación automáticamente utiliza localStorage como fallback, manteniendo la funcionalidad básica.

## 🐛 Solución de Problemas

### **Error: API server is not running**
```bash
# Verificar que el backend esté ejecutándose
npm run server

# O ejecutar ambos servidores
npm run dev:full
```

### **Error: Database connection failed**
```bash
# Regenerar cliente Prisma
npx prisma generate

# Ejecutar migraciones
npx prisma migrate dev

# Verificar base de datos
npm run verify
```

### **Error: Users not found**
```bash
# Crear usuarios por defecto
npm run seed
```

## 📁 Estructura del Proyecto

```
├── server/
│   └── index.js              # Servidor Express + API
├── src/
│   ├── services/
│   │   ├── ApiService.ts      # Cliente API para frontend
│   │   ├── PrismaAuthService.ts # Servicio de autenticación
│   │   └── ApiUserService.ts  # Servicio de usuarios con fallback
│   └── ...
├── scripts/
│   ├── seed-users.js         # Script para crear usuarios
│   └── verify-prisma.js      # Script de verificación
├── prisma/
│   ├── schema.prisma         # Esquema de base de datos
│   └── migrations/           # Migraciones
└── package.json
```

## ✅ Funcionalidades

- ✅ **Autenticación** completa con API backend
- ✅ **Gestión de usuarios** via API
- ✅ **Fallback a localStorage** si API no disponible
- ✅ **Base de datos persistente** con Prisma + SQLite
- ✅ **Hot reload** en desarrollo
- ✅ **Usuarios por defecto** creados automáticamente

## 🎯 Próximos Pasos

1. Implementar endpoints para Teams y Boards
2. Agregar autenticación JWT
3. Implementar WebSockets para tiempo real
4. Agregar tests unitarios y de integración
5. Configurar Docker para deployment

## 🤝 Contribución

1. Fork el proyecto
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT.
