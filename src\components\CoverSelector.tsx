import React, { useState } from 'react';
import { X, Upload, Palette, Image as ImageIcon } from 'lucide-react';

interface CoverSelectorProps {
  currentCover?: string;
  onSetCover: (coverUrl: string) => void;
  onRemoveCover: () => void;
  onClose: () => void;
}

export function CoverSelector({ currentCover, onSetCover, onRemoveCover, onClose }: CoverSelectorProps) {
  const [activeTab, setActiveTab] = useState<'colors' | 'photos' | 'upload'>('colors');

  // Predefined color covers
  const colorCovers = [
    '#ef4444', // red
    '#f97316', // orange
    '#eab308', // yellow
    '#22c55e', // green
    '#06b6d4', // cyan
    '#3b82f6', // blue
    '#8b5cf6', // violet
    '#ec4899', // pink
    '#6b7280', // gray
    '#1f2937', // dark gray
  ];

  // Predefined gradient covers
  const gradientCovers = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
  ];

  // Unsplash photos (sample URLs)
  const photoCovers = [
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop',
    'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=200&fit=crop',
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop',
    'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400&h=200&fit=crop',
    'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=200&fit=crop',
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop',
  ];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        onSetCover(result);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div
      className="absolute top-full mt-2 left-0 w-full max-w-sm bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-hidden"
      onClick={(e) => e.stopPropagation()}
      style={{
        maxHeight: 'calc(100vh - 200px)',
        minWidth: '300px'
      }}
    >
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-gray-900">Portada</h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X className="w-4 h-4 text-gray-500" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mt-3 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('colors')}
            className={`flex-1 flex items-center justify-center space-x-1 py-2 px-3 rounded-md text-sm transition-colors ${
              activeTab === 'colors' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Palette className="w-4 h-4" />
            <span>Colores</span>
          </button>
          <button
            onClick={() => setActiveTab('photos')}
            className={`flex-1 flex items-center justify-center space-x-1 py-2 px-3 rounded-md text-sm transition-colors ${
              activeTab === 'photos' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <ImageIcon className="w-4 h-4" />
            <span>Fotos</span>
          </button>
          <button
            onClick={() => setActiveTab('upload')}
            className={`flex-1 flex items-center justify-center space-x-1 py-2 px-3 rounded-md text-sm transition-colors ${
              activeTab === 'upload' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Upload className="w-4 h-4" />
            <span>Subir</span>
          </button>
        </div>
      </div>

      <div className="p-4 max-h-64 overflow-y-auto">
        {activeTab === 'colors' && (
          <div className="space-y-4">
            {/* Solid Colors */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Colores sólidos</h4>
              <div className="grid grid-cols-5 gap-2">
                {colorCovers.map((color, index) => (
                  <button
                    key={index}
                    onClick={() => onSetCover(color)}
                    className="w-full h-12 rounded-lg border-2 border-gray-200 hover:border-gray-300 transition-colors"
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </div>

            {/* Gradients */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Gradientes</h4>
              <div className="grid grid-cols-2 gap-2">
                {gradientCovers.map((gradient, index) => (
                  <button
                    key={index}
                    onClick={() => onSetCover(gradient)}
                    className="w-full h-12 rounded-lg border-2 border-gray-200 hover:border-gray-300 transition-colors"
                    style={{ background: gradient }}
                  />
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'photos' && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Fotos</h4>
            <div className="grid grid-cols-2 gap-2">
              {photoCovers.map((photo, index) => (
                <button
                  key={index}
                  onClick={() => onSetCover(photo)}
                  className="w-full h-16 rounded-lg border-2 border-gray-200 hover:border-gray-300 transition-colors overflow-hidden"
                >
                  <img
                    src={photo}
                    alt={`Cover option ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'upload' && (
          <div className="text-center">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-3">
                Sube una imagen desde tu computadora
              </p>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
                id="cover-upload"
              />
              <label
                htmlFor="cover-upload"
                className="inline-block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 cursor-pointer transition-colors"
              >
                Seleccionar archivo
              </label>
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="p-4 border-t">
        {currentCover && (
          <button
            onClick={onRemoveCover}
            className="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors"
          >
            Quitar portada
          </button>
        )}
      </div>
    </div>
  );
}
