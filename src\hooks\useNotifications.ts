import { useEffect, useState, useCallback } from 'react';
import { NotificationService } from '../services/NotificationService';
import { AppNotification, Card, User } from '../types';
import { useAuth } from '../context/AuthContext';

export function useNotifications() {
  const [notifications, setNotifications] = useState<AppNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Get current user from auth context
  const { user: currentUser } = useAuth();

  const notificationService = NotificationService.getInstance();

  useEffect(() => {
    // Subscribe to notification changes
    const unsubscribe = notificationService.subscribe((newNotifications) => {
      setNotifications(newNotifications);
      setUnreadCount(newNotifications.filter(n => !n.read).length);
    });

    // Load initial notifications
    setNotifications(notificationService.getNotifications());
    setUnreadCount(notificationService.getUnreadCount());

    return unsubscribe;
  }, [notificationService]);

  const markAsRead = useCallback((notificationId: string) => {
    notificationService.markAsRead(notificationId);
  }, [notificationService]);

  const markAllAsRead = useCallback(() => {
    notificationService.markAllAsRead();
  }, [notificationService]);

  const clearAll = useCallback(() => {
    notificationService.clearAll();
  }, [notificationService]);

  const requestPermission = useCallback(async () => {
    return await NotificationService.requestPermission();
  }, []);

  // Helper functions for creating notifications
  const createAssignmentNotification = useCallback((assignedUser: User, card: Card, assigner: User, boardId: string) => {
    // Only notify if the assigned user is not the one doing the assignment and is the current user
    if (assignedUser.id !== assigner.id && assignedUser.id === currentUser?.id) {
      return notificationService.createAssignmentNotification(assignedUser, card, assigner, boardId);
    }
    return null;
  }, [notificationService, currentUser]);

  const createMentionNotification = useCallback((mentionedUser: User, card: Card, author: User, boardId: string) => {
    // Only notify if the mentioned user is not the author and is the current user
    if (mentionedUser.id !== author.id && mentionedUser.id === currentUser?.id) {
      return notificationService.createMentionNotification(mentionedUser, card, author, boardId);
    }
    return null;
  }, [notificationService, currentUser]);

  const createCommentNotification = useCallback((card: Card, author: User, boardId: string, assignedUsers: User[]) => {
    // Filter to only notify the current user if they're assigned and not the author
    const currentUserAssigned = assignedUsers.find(user => user.id === currentUser?.id);
    if (currentUserAssigned && author.id !== currentUser?.id) {
      return notificationService.createCommentNotification(card, author, boardId, [currentUserAssigned]);
    }
    return [];
  }, [notificationService, currentUser]);

  // Function to check for mentions in text (simplified without board context)
  const checkForMentions = useCallback((text: string, card: Card, author: User, boardId: string, boardMembers: User[] = []) => {
    if (!currentUser) return;

    const mentionRegex = /@(\w+)/g;
    let match;

    while ((match = mentionRegex.exec(text)) !== null) {
      const memberName = match[1];
      const mentionedMember = boardMembers.find(m =>
        m.name.toLowerCase() === memberName.toLowerCase()
      );

      if (mentionedMember) {
        createMentionNotification(mentionedMember, card, author, boardId);
      }
    }
  }, [currentUser, createMentionNotification]);

  // Function to check for new assignments
  const checkForNewAssignments = useCallback((
    oldAssignedMembers: User[],
    newAssignedMembers: User[],
    card: Card,
    assigner: User,
    boardId: string
  ) => {
    if (!currentUser) return;

    // Find newly assigned members
    const newlyAssigned = newAssignedMembers.filter(newMember =>
      !oldAssignedMembers.some(oldMember => oldMember.id === newMember.id)
    );

    // Create notifications for newly assigned members
    newlyAssigned.forEach(assignedUser => {
      createAssignmentNotification(assignedUser, card, assigner, boardId);
    });
  }, [currentUser, createAssignmentNotification]);

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearAll,
    requestPermission,
    notificationService,
    createAssignmentNotification,
    createMentionNotification,
    createCommentNotification,
    checkForMentions,
    checkForNewAssignments
  };
}
