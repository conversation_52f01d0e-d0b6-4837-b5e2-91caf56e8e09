import fs from 'fs';
import path from 'path';

const srcDir = path.join(process.cwd(), 'src');

function findLocalStorageUsage(dir) {
  const files = fs.readdirSync(dir);
  const results = [];

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      results.push(...findLocalStorageUsage(filePath));
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        if (line.includes('localStorage') || 
            line.includes('getItem') || 
            line.includes('setItem') || 
            line.includes('removeItem') ||
            line.includes('sessionStorage')) {
          results.push({
            file: filePath.replace(process.cwd(), ''),
            line: index + 1,
            content: line.trim()
          });
        }
      });
    }
  }

  return results;
}

function analyzeLocalStorageUsage() {
  console.log('🔍 Analyzing localStorage usage in the codebase...\n');
  
  const usage = findLocalStorageUsage(srcDir);
  
  if (usage.length === 0) {
    console.log('✅ No localStorage usage found in the codebase!');
    return;
  }

  console.log(`❌ Found ${usage.length} localStorage references:\n`);
  
  const groupedByFile = usage.reduce((acc, item) => {
    if (!acc[item.file]) {
      acc[item.file] = [];
    }
    acc[item.file].push(item);
    return acc;
  }, {});

  Object.entries(groupedByFile).forEach(([file, items]) => {
    console.log(`📄 ${file}:`);
    items.forEach(item => {
      console.log(`   Line ${item.line}: ${item.content}`);
    });
    console.log('');
  });

  console.log('🧹 Recommendations for cleanup:');
  console.log('1. Replace localStorage calls with API calls');
  console.log('2. Remove fallback localStorage mechanisms');
  console.log('3. Update service classes to use database-only operations');
  console.log('4. Remove localStorage-based state persistence');
}

function createCleanupReport() {
  const usage = findLocalStorageUsage(srcDir);
  const report = {
    timestamp: new Date().toISOString(),
    totalReferences: usage.length,
    files: Object.keys(usage.reduce((acc, item) => {
      acc[item.file] = true;
      return acc;
    }, {})),
    details: usage
  };

  fs.writeFileSync('localStorage-cleanup-report.json', JSON.stringify(report, null, 2));
  console.log('📊 Cleanup report saved to localStorage-cleanup-report.json');
}

// Run analysis
analyzeLocalStorageUsage();
createCleanupReport();
