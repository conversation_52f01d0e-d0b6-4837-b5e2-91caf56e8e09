import React, { useState } from 'react';
import { Team, TeamSettings, Board, User } from '../../types';
import { useDatabaseApp } from '../../context/DatabaseAppContext';
import { useUserPermissions } from '../../hooks/useUserPermissions';
import { useAuth } from '../../context/AuthContext';
import {
  X,
  Users,
  Lock,
  Globe,
  Building,
  Eye,
  Upload,
  Camera,
  Check,
  Plus,
  FolderPlus,
  ChevronDown,
  UserPlus,
  Search,
  Crown
} from 'lucide-react';
import { TeamMemberSelector } from './TeamMemberSelector';

interface CreateTeamModalProps {
  onClose: () => void;
  onCreateTeam: (team: Omit<Team, 'id' | 'createdAt' | 'updatedAt'>, newBoard?: Omit<Board, 'id' | 'createdAt' | 'updatedAt'>) => void;
}

export function CreateTeamModal({ onClose, onCreateTeam }: CreateTeamModalProps) {
  const { state } = useDatabaseApp();
  const { canCreateTeams, getRoleName } = useUserPermissions();
  const { user: currentUser } = useAuth();

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    visibility: 'team' as Team['visibility'],
    avatar: ''
  });

  const [settings, setSettings] = useState<TeamSettings>({
    allowMembersToCreateBoards: false,
    allowMembersToInvite: false,
    defaultBoardVisibility: 'team',
    requireApprovalForJoining: true // Cambiado a true por defecto
  });

  // Estados para la gestión de miembros
  const [selectedMembers, setSelectedMembers] = useState<User[]>([]);
  const [showMemberSelector, setShowMemberSelector] = useState(false);

  // Estados para la gestión de tableros
  const [boardOption, setBoardOption] = useState<'none' | 'existing' | 'new'>('none');
  const [selectedBoardId, setSelectedBoardId] = useState('');
  const [newBoardData, setNewBoardData] = useState({
    title: '',
    description: '',
    background: 'bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const visibilityOptions = [
    {
      value: 'private' as const,
      label: 'Privado',
      description: 'Solo los miembros invitados pueden ver y unirse',
      icon: Lock,
      color: 'text-red-600 bg-red-50'
    },
    {
      value: 'team' as const,
      label: 'Equipo',
      description: 'Los miembros del equipo pueden ver y unirse',
      icon: Users,
      color: 'text-blue-600 bg-blue-50'
    },
    {
      value: 'organization' as const,
      label: 'Organización',
      description: 'Todos en la organización pueden ver y unirse',
      icon: Building,
      color: 'text-purple-600 bg-purple-50'
    }
    // Opción 'public' eliminada según los requisitos
  ];

  // Obtener tableros disponibles (que no pertenezcan a ningún equipo)
  const availableBoards = state.boards.filter(board => !board.teamId);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'El nombre del equipo es obligatorio';
    } else if (formData.name.length < 3) {
      newErrors.name = 'El nombre debe tener al menos 3 caracteres';
    } else if (formData.name.length > 50) {
      newErrors.name = 'El nombre no puede tener más de 50 caracteres';
    }

    if (formData.description.length > 200) {
      newErrors.description = 'La descripción no puede tener más de 200 caracteres';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    let boardIds: string[] = [];
    let newBoard: Omit<Board, 'id' | 'createdAt' | 'updatedAt'> | undefined;

    // Manejar la asignación de tableros
    if (boardOption === 'existing' && selectedBoardId) {
      boardIds = [selectedBoardId];
    } else if (boardOption === 'new' && newBoardData.title.trim()) {
      newBoard = {
        title: newBoardData.title.trim(),
        description: newBoardData.description.trim(),
        background: newBoardData.background,
        lists: [],
        members: [],
        labels: [],
        isPublic: false,
        isFavorite: false,
        visibility: 'team'
      };
    }

    // Crear la lista de miembros del equipo (incluye al creador como admin)
    const teamMembers = [
      // El creador siempre es admin
      {
        userId: currentUser?.id || '',
        role: 'admin' as const,
        joinedAt: new Date()
      },
      // Agregar los miembros seleccionados como members
      ...selectedMembers
        .filter(member => member.id !== currentUser?.id) // Evitar duplicar al creador
        .map(member => ({
          userId: member.id,
          role: 'member' as const,
          joinedAt: new Date()
        }))
    ];

    const teamData: Omit<Team, 'id' | 'createdAt' | 'updatedAt'> = {
      name: formData.name.trim(),
      description: formData.description.trim(),
      visibility: formData.visibility,
      avatar: formData.avatar,
      members: teamMembers,
      boards: boardIds,
      settings
    };

    onCreateTeam(teamData, newBoard);
  };

  const handleAvatarUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setFormData(prev => ({ ...prev, avatar: e.target?.result as string }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleToggleMember = (member: User) => {
    setSelectedMembers(prev => {
      const isSelected = prev.some(m => m.id === member.id);
      if (isSelected) {
        return prev.filter(m => m.id !== member.id);
      } else {
        return [...prev, member];
      }
    });
  };

  const handleRemoveMember = (memberId: string) => {
    setSelectedMembers(prev => prev.filter(m => m.id !== memberId));
  };

  // Verificar permisos antes de mostrar el modal
  if (!canCreateTeams()) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
              <X className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Acceso Denegado
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              Solo los administradores pueden crear equipos. Tu rol actual es: <strong>{getRoleName()}</strong>
            </p>
            <button
              onClick={onClose}
              className="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              Entendido
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b bg-gradient-to-r from-purple-600 to-blue-600 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Users className="w-6 h-6" />
              <h2 className="text-xl font-bold">Crear Nuevo Equipo</h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Avatar */}
            <div className="flex items-center space-x-4">
              <div className="relative">
                {formData.avatar ? (
                  <img
                    src={formData.avatar}
                    alt="Avatar del equipo"
                    className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                  />
                ) : (
                  <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center">
                    <Users className="w-8 h-8 text-gray-400" />
                  </div>
                )}
                <label className="absolute -bottom-1 -right-1 bg-blue-600 text-white p-1.5 rounded-full cursor-pointer hover:bg-blue-700 transition-colors">
                  <Camera className="w-3 h-3" />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarUpload}
                    className="hidden"
                  />
                </label>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Avatar del Equipo</h3>
                <p className="text-sm text-gray-500">Sube una imagen para representar tu equipo</p>
              </div>
            </div>

            {/* Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nombre del Equipo *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Ej: Equipo de Desarrollo"
                maxLength={50}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                {formData.name.length}/50 caracteres
              </p>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descripción
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${
                  errors.description ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Describe el propósito y objetivos de tu equipo..."
                rows={3}
                maxLength={200}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                {formData.description.length}/200 caracteres
              </p>
            </div>

            {/* Board Assignment */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Asignación de Tableros
              </label>
              <div className="space-y-3">
                {/* No board option */}
                <label className={`flex items-start space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                  boardOption === 'none' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                }`}>
                  <input
                    type="radio"
                    name="boardOption"
                    value="none"
                    checked={boardOption === 'none'}
                    onChange={(e) => setBoardOption(e.target.value as any)}
                    className="sr-only"
                  />
                  <div className="p-2 rounded-lg text-gray-600 bg-gray-50">
                    <X className="w-4 h-4" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">Sin tablero</span>
                      {boardOption === 'none' && <Check className="w-4 h-4 text-blue-600" />}
                    </div>
                    <p className="text-sm text-gray-500">Crear el equipo sin asignar tableros</p>
                  </div>
                </label>

                {/* Existing board option */}
                <label className={`flex items-start space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                  boardOption === 'existing' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                }`}>
                  <input
                    type="radio"
                    name="boardOption"
                    value="existing"
                    checked={boardOption === 'existing'}
                    onChange={(e) => setBoardOption(e.target.value as any)}
                    className="sr-only"
                  />
                  <div className="p-2 rounded-lg text-blue-600 bg-blue-50">
                    <FolderPlus className="w-4 h-4" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">Asignar tablero existente</span>
                      {boardOption === 'existing' && <Check className="w-4 h-4 text-blue-600" />}
                    </div>
                    <p className="text-sm text-gray-500">Seleccionar un tablero ya creado</p>
                  </div>
                </label>

                {/* Existing board selector */}
                {boardOption === 'existing' && (
                  <div className="ml-12 space-y-2">
                    <select
                      value={selectedBoardId}
                      onChange={(e) => setSelectedBoardId(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Seleccionar tablero...</option>
                      {availableBoards.map(board => (
                        <option key={board.id} value={board.id}>
                          {board.title}
                        </option>
                      ))}
                    </select>
                    {availableBoards.length === 0 && (
                      <p className="text-sm text-gray-500">No hay tableros disponibles para asignar</p>
                    )}
                  </div>
                )}

                {/* New board option */}
                <label className={`flex items-start space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                  boardOption === 'new' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                }`}>
                  <input
                    type="radio"
                    name="boardOption"
                    value="new"
                    checked={boardOption === 'new'}
                    onChange={(e) => setBoardOption(e.target.value as any)}
                    className="sr-only"
                  />
                  <div className="p-2 rounded-lg text-green-600 bg-green-50">
                    <Plus className="w-4 h-4" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">Crear nuevo tablero</span>
                      {boardOption === 'new' && <Check className="w-4 h-4 text-blue-600" />}
                    </div>
                    <p className="text-sm text-gray-500">Crear un tablero nuevo para el equipo</p>
                  </div>
                </label>

                {/* New board form */}
                {boardOption === 'new' && (
                  <div className="ml-12 space-y-3">
                    <div>
                      <input
                        type="text"
                        value={newBoardData.title}
                        onChange={(e) => setNewBoardData(prev => ({ ...prev, title: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Nombre del tablero..."
                        maxLength={50}
                      />
                    </div>
                    <div>
                      <textarea
                        value={newBoardData.description}
                        onChange={(e) => setNewBoardData(prev => ({ ...prev, description: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                        placeholder="Descripción del tablero (opcional)..."
                        rows={2}
                        maxLength={200}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Visibility */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Visibilidad del Equipo
              </label>
              <div className="space-y-3">
                {visibilityOptions.map(option => {
                  const Icon = option.icon;
                  const isSelected = formData.visibility === option.value;
                  
                  return (
                    <label
                      key={option.value}
                      className={`flex items-start space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                        isSelected 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <input
                        type="radio"
                        name="visibility"
                        value={option.value}
                        checked={isSelected}
                        onChange={(e) => setFormData(prev => ({ ...prev, visibility: e.target.value as Team['visibility'] }))}
                        className="sr-only"
                      />
                      <div className={`p-2 rounded-lg ${option.color}`}>
                        <Icon className="w-4 h-4" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900">{option.label}</span>
                          {isSelected && <Check className="w-4 h-4 text-blue-600" />}
                        </div>
                        <p className="text-sm text-gray-500">{option.description}</p>
                      </div>
                    </label>
                  );
                })}
              </div>
            </div>

            {/* Team Members */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-gray-700">Miembros del Equipo</h3>
                <div className="relative">
                  <button
                    type="button"
                    onClick={() => setShowMemberSelector(!showMemberSelector)}
                    className="flex items-center space-x-2 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
                  >
                    <UserPlus className="w-4 h-4" />
                    <span>Agregar Miembros</span>
                  </button>

                  {showMemberSelector && (
                    <TeamMemberSelector
                      assignedMembers={selectedMembers}
                      onToggleMember={handleToggleMember}
                      onClose={() => setShowMemberSelector(false)}
                    />
                  )}
                </div>
              </div>

              {/* Selected Members Display */}
              <div className="space-y-2">
                {/* Current User (Creator) */}
                <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      {currentUser?.name?.charAt(0).toUpperCase() || 'U'}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {currentUser?.name || 'Usuario Actual'} (Tú)
                      </p>
                      <p className="text-xs text-gray-500">{currentUser?.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      <Crown className="w-3 h-3 mr-1" />
                      Admin
                    </span>
                  </div>
                </div>

                {/* Selected Members */}
                {selectedMembers.map(member => (
                  <div key={member.id} className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        {member.name.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{member.name}</p>
                        <p className="text-xs text-gray-500">{member.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <Users className="w-3 h-3 mr-1" />
                        Miembro
                      </span>
                      <button
                        type="button"
                        onClick={() => handleRemoveMember(member.id)}
                        className="p-1 hover:bg-gray-200 rounded transition-colors"
                      >
                        <X className="w-4 h-4 text-gray-500" />
                      </button>
                    </div>
                  </div>
                ))}

                {selectedMembers.length === 0 && (
                  <div className="text-center py-4 text-gray-500">
                    <Users className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                    <p className="text-sm">No hay miembros adicionales seleccionados</p>
                    <p className="text-xs">Haz clic en "Agregar Miembros" para invitar personas al equipo</p>
                  </div>
                )}
              </div>
            </div>

            {/* Team Settings */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Configuración del Equipo</h3>
              <div className="space-y-3">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings.allowMembersToCreateBoards}
                    onChange={(e) => setSettings(prev => ({ ...prev, allowMembersToCreateBoards: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Permitir a los miembros crear tableros</span>
                </label>

                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings.allowMembersToInvite}
                    onChange={(e) => setSettings(prev => ({ ...prev, allowMembersToInvite: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Permitir a los miembros invitar personas</span>
                </label>

                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings.requireApprovalForJoining}
                    onChange={(e) => setSettings(prev => ({ ...prev, requireApprovalForJoining: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Requerir aprobación para unirse</span>
                </label>
              </div>
            </div>

            {/* Form Buttons */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>



              <button
                type="submit"
                disabled={!formData.name.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"

              >
                Crear Equipo
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
