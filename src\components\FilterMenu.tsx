import React from 'react';
import { useDatabaseApp } from '../context/DatabaseAppContext';
import { useAuth } from '../context/AuthContext';
import { Check, User, Clock, Calendar, FileText } from 'lucide-react';

interface FilterMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

export function FilterMenu({ isOpen, onClose }: FilterMenuProps) {
  const { state, getCurrentBoard } = useDatabaseApp();
  const { user: currentUser } = useAuth();
  const { cardFilter } = state;
  const currentBoard = getCurrentBoard();

  if (!isOpen) return null;

  // Calculate counts for each filter
  const getFilterCounts = () => {
    if (!currentBoard) return { all: 0, assigned: 0, created: 0, dueSoon: 0 };

    const allCards = currentBoard.lists.flatMap(list => list.cards);
    
    const all = allCards.length;
    const assigned = allCards.filter(card => 
      card.assignedMembers.some(member => member.id === currentUser?.id)
    ).length;
    const created = allCards.filter(card => 
      card.createdAt && new Date(card.createdAt).toDateString() === new Date().toDateString()
    ).length;
    const dueSoon = allCards.filter(card => {
      if (!card.dueDate) return false;
      const dueDate = new Date(card.dueDate);
      const today = new Date();
      const diffTime = dueDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays <= 7 && diffDays >= 0;
    }).length;

    return { all, assigned, created, dueSoon };
  };

  const counts = getFilterCounts();

  const filterOptions = [
    {
      id: 'all' as const,
      label: 'Todas las tarjetas',
      icon: FileText,
      count: counts.all,
      description: 'Ver todas las tarjetas del tablero'
    },
    {
      id: 'assigned' as const,
      label: 'Asignadas a mí',
      icon: User,
      count: counts.assigned,
      description: 'Tarjetas donde estoy asignado'
    },
    {
      id: 'created' as const,
      label: 'Creadas hoy',
      icon: Clock,
      count: counts.created,
      description: 'Tarjetas creadas en el día de hoy'
    },
    {
      id: 'due-soon' as const,
      label: 'Vencen pronto',
      icon: Calendar,
      count: counts.dueSoon,
      description: 'Tarjetas que vencen en los próximos 7 días'
    }
  ];

  const handleFilterChange = (filterId: typeof cardFilter) => {
    dispatch({ type: 'SET_CARD_FILTER', payload: filterId });
    onClose();
  };

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 z-40" 
        onClick={onClose}
      />
      
      {/* Menu */}
      <div className="absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900">Filtrar tarjetas</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>

          <div className="space-y-2">
            {filterOptions.map((option) => {
              const Icon = option.icon;
              const isSelected = cardFilter === option.id;
              
              return (
                <button
                  key={option.id}
                  onClick={() => handleFilterChange(option.id)}
                  className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                    isSelected 
                      ? 'bg-blue-50 border-2 border-blue-200' 
                      : 'hover:bg-gray-50 border-2 border-transparent'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      isSelected ? 'bg-blue-100' : 'bg-gray-100'
                    }`}>
                      <Icon className={`w-4 h-4 ${
                        isSelected ? 'text-blue-600' : 'text-gray-600'
                      }`} />
                    </div>
                    <div className="text-left">
                      <div className={`font-medium ${
                        isSelected ? 'text-blue-900' : 'text-gray-900'
                      }`}>
                        {option.label}
                      </div>
                      <div className="text-sm text-gray-500">
                        {option.description}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                      isSelected 
                        ? 'bg-blue-100 text-blue-800' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {option.count}
                    </span>
                    {isSelected && (
                      <Check className="w-4 h-4 text-blue-600" />
                    )}
                  </div>
                </button>
              );
            })}
          </div>

          {/* Filter Summary */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="text-sm text-gray-600">
              {cardFilter === 'all' && `Mostrando todas las ${counts.all} tarjetas`}
              {cardFilter === 'assigned' && `Mostrando ${counts.assigned} tarjetas asignadas a ti`}
              {cardFilter === 'created' && `Mostrando ${counts.created} tarjetas creadas hoy`}
              {cardFilter === 'due-soon' && `Mostrando ${counts.dueSoon} tarjetas que vencen pronto`}
            </div>
            
            {cardFilter !== 'all' && (
              <button
                onClick={() => handleFilterChange('all')}
                className="text-sm text-blue-600 hover:text-blue-700 mt-2"
              >
                Limpiar filtro
              </button>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
