import express from 'express';
import cors from 'cors';

const app = express();
const PORT = process.env.PORT || 3001;

// Enhanced CORS for dev tunnels
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    // Allow localhost and dev tunnels
    const allowedOrigins = [
      'http://localhost:5173',
      'http://localhost:3000',
      'https://localhost:5173',
      'https://localhost:3000'
    ];
    
    // Allow any devtunnels.ms origin
    if (origin.includes('devtunnels.ms') || 
        origin.includes('ngrok.io') || 
        origin.includes('tunnels.dev') ||
        allowedOrigins.includes(origin)) {
      console.log('✅ CORS allowed origin:', origin);
      return callback(null, true);
    }
    
    console.log('🚫 CORS blocked origin:', origin);
    callback(new Error('Not allowed by CORS'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-user-id', 'Origin', 'X-Requested-With', 'Accept'],
  exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
  preflightContinue: false,
  optionsSuccessStatus: 200
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req, res) => {
  console.log('🏥 Health check requested from:', req.get('origin') || 'unknown');
  res.json({ 
    status: 'OK', 
    message: 'Trello API Server is running',
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

// Simple test endpoint
app.get('/api/test', (req, res) => {
  console.log('🧪 Test endpoint requested from:', req.get('origin') || 'unknown');
  res.json({ 
    success: true, 
    message: 'API is working', 
    timestamp: new Date().toISOString() 
  });
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 Debug Trello API Server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/test`);
  console.log(`🌐 CORS enabled for dev tunnels and localhost`);
  console.log('🔄 Server is ready to accept requests...');
});

// Keep the server alive
server.on('error', (error) => {
  console.error('❌ Server error:', error);
});

// Prevent the process from exiting
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  // Don't exit the process, just log the error
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit the process, just log the error
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down debug server...');
  server.close(() => {
    console.log('✅ Debug server closed');
    process.exit(0);
  });
});

console.log('🎯 Debug server script loaded successfully');
