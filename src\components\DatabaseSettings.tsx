import React, { useState } from 'react';
import { useDatabase } from '../context/DatabaseContext';
import { 
  Database, 
  HardDrive, 
  Cloud, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle,
  Info,
  Settings
} from 'lucide-react';

export function DatabaseSettings() {
  const { 
    usePrisma, 
    toggleDatabase, 
    loading, 
    error, 
    users, 
    teams, 
    boards,
    refreshData 
  } = useDatabase();
  
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshData();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleToggleDatabase = () => {
    if (confirm('¿Estás seguro de que quieres cambiar el sistema de base de datos? Esto puede afectar los datos actuales.')) {
      toggleDatabase();
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-blue-100 rounded-lg">
          <Database className="w-6 h-6 text-blue-600" />
        </div>
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Configuración de Base de Datos</h2>
          <p className="text-sm text-gray-500">Gestiona el sistema de almacenamiento de datos</p>
        </div>
      </div>

      {/* Current Status */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-medium text-gray-900">Estado Actual</h3>
          <button
            onClick={handleRefresh}
            disabled={isRefreshing || loading}
            className="flex items-center space-x-2 px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            <span>Actualizar</span>
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${usePrisma ? 'bg-green-100' : 'bg-blue-100'}`}>
              {usePrisma ? (
                <Cloud className="w-5 h-5 text-green-600" />
              ) : (
                <HardDrive className="w-5 h-5 text-blue-600" />
              )}
            </div>
            <div>
              <p className="font-medium text-gray-900">
                {usePrisma ? 'Base de Datos (Prisma)' : 'Almacenamiento Local'}
              </p>
              <p className="text-sm text-gray-500">
                {usePrisma ? 'SQLite con Prisma ORM' : 'localStorage del navegador'}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${error ? 'bg-red-100' : loading ? 'bg-yellow-100' : 'bg-green-100'}`}>
              {error ? (
                <AlertTriangle className="w-5 h-5 text-red-600" />
              ) : loading ? (
                <RefreshCw className="w-5 h-5 text-yellow-600 animate-spin" />
              ) : (
                <CheckCircle className="w-5 h-5 text-green-600" />
              )}
            </div>
            <div>
              <p className="font-medium text-gray-900">
                {error ? 'Error' : loading ? 'Cargando...' : 'Conectado'}
              </p>
              <p className="text-sm text-gray-500">
                {error || (loading ? 'Cargando datos...' : 'Sistema funcionando correctamente')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Data Statistics */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium text-gray-900 mb-3">Estadísticas de Datos</h3>
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{users.length}</div>
            <div className="text-sm text-gray-500">Usuarios</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{teams.length}</div>
            <div className="text-sm text-gray-500">Equipos</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{boards.length}</div>
            <div className="text-sm text-gray-500">Tableros</div>
          </div>
        </div>
      </div>

      {/* Database Options */}
      <div className="space-y-4">
        <h3 className="font-medium text-gray-900">Opciones de Base de Datos</h3>
        
        {/* LocalStorage Option */}
        <div className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
          !usePrisma ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
        }`}>
          <div className="flex items-start space-x-3">
            <div className={`p-2 rounded-lg ${!usePrisma ? 'bg-blue-100' : 'bg-gray-100'}`}>
              <HardDrive className={`w-5 h-5 ${!usePrisma ? 'text-blue-600' : 'text-gray-500'}`} />
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h4 className="font-medium text-gray-900">Almacenamiento Local</h4>
                {!usePrisma && <CheckCircle className="w-4 h-4 text-blue-600" />}
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Almacena los datos en el navegador usando localStorage. Ideal para desarrollo y pruebas.
              </p>
              <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                <span>✓ Rápido</span>
                <span>✓ Sin configuración</span>
                <span>⚠ Solo local</span>
              </div>
            </div>
          </div>
        </div>

        {/* Prisma Option */}
        <div className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
          usePrisma ? 'border-green-500 bg-green-50' : 'border-gray-200 hover:border-gray-300'
        }`}>
          <div className="flex items-start space-x-3">
            <div className={`p-2 rounded-lg ${usePrisma ? 'bg-green-100' : 'bg-gray-100'}`}>
              <Cloud className={`w-5 h-5 ${usePrisma ? 'text-green-600' : 'text-gray-500'}`} />
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h4 className="font-medium text-gray-900">Base de Datos con Prisma</h4>
                {usePrisma && <CheckCircle className="w-4 h-4 text-green-600" />}
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Utiliza SQLite con Prisma ORM para almacenamiento persistente y escalable.
              </p>
              <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                <span>✓ Persistente</span>
                <span>✓ Escalable</span>
                <span>✓ Relacional</span>
              </div>
            </div>
          </div>
        </div>

        {/* Toggle Button */}
        <div className="pt-4 border-t border-gray-200">
          <button
            onClick={handleToggleDatabase}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:bg-gray-400 transition-colors"
          >
            <Settings className="w-4 h-4" />
            <span>
              Cambiar a {usePrisma ? 'Almacenamiento Local' : 'Base de Datos Prisma'}
            </span>
          </button>
        </div>

        {/* Info Box */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <Info className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">Información Importante</h4>
              <p className="text-sm text-blue-700 mt-1">
                Al cambiar el sistema de base de datos, los datos actuales pueden no estar disponibles 
                inmediatamente. Se recomienda hacer una copia de seguridad antes de cambiar.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
