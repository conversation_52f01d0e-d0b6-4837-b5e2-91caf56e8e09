import ApiService from './ApiService';

export interface UserPreferences {
  id?: string;
  userId: string;
  sidebarOpen: boolean;
  theme: string;
  language: string;
  lastBoardId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

class UserPreferencesService {
  /**
   * Get user preferences
   */
  static async getUserPreferences(userId: string): Promise<UserPreferences | null> {
    try {
      const response = await ApiService.request(`/users/${userId}/preferences`);
      return response;
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return null;
    }
  }

  /**
   * Update user preferences
   */
  static async updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<UserPreferences | null> {
    try {
      const response = await ApiService.request(`/users/${userId}/preferences`, {
        method: 'PUT',
        body: JSON.stringify(preferences),
      });
      return response;
    } catch (error) {
      console.error('Error updating user preferences:', error);
      return null;
    }
  }

  /**
   * Update last board used
   */
  static async updateLastBoard(userId: string, boardId: string): Promise<void> {
    try {
      await this.updateUserPreferences(userId, { lastBoardId: boardId });
    } catch (error) {
      console.error('Error updating last board:', error);
    }
  }

  /**
   * Get last board used
   */
  static async getLastBoard(userId: string): Promise<string | null> {
    try {
      const preferences = await this.getUserPreferences(userId);
      return preferences?.lastBoardId || null;
    } catch (error) {
      console.error('Error getting last board:', error);
      return null;
    }
  }

  /**
   * Update sidebar state
   */
  static async updateSidebarState(userId: string, isOpen: boolean): Promise<void> {
    try {
      await this.updateUserPreferences(userId, { sidebarOpen: isOpen });
    } catch (error) {
      console.error('Error updating sidebar state:', error);
    }
  }

  /**
   * Update theme
   */
  static async updateTheme(userId: string, theme: string): Promise<void> {
    try {
      await this.updateUserPreferences(userId, { theme });
    } catch (error) {
      console.error('Error updating theme:', error);
    }
  }

  /**
   * Update language
   */
  static async updateLanguage(userId: string, language: string): Promise<void> {
    try {
      await this.updateUserPreferences(userId, { language });
    } catch (error) {
      console.error('Error updating language:', error);
    }
  }

  /**
   * Create default preferences for new user
   */
  static async createDefaultPreferences(userId: string): Promise<UserPreferences | null> {
    try {
      const defaultPreferences: Partial<UserPreferences> = {
        userId,
        sidebarOpen: false,
        theme: 'light',
        language: 'es',
        lastBoardId: undefined
      };

      const response = await ApiService.request(`/users/${userId}/preferences`, {
        method: 'POST',
        body: JSON.stringify(defaultPreferences),
      });
      return response;
    } catch (error) {
      console.error('Error creating default preferences:', error);
      return null;
    }
  }
}

export default UserPreferencesService;
